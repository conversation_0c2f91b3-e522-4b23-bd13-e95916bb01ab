# 高速公路计费路段API文档

## 概述
本文档描述了高速公路计费路段管理相关的API接口，主要用于获取路段的几何信息用于地图标注。

**核心特性：**
- 支持大路段筛选和所有路段查询
- 保持大路段下小路段的层级结构
- 每个小路段独立保存几何信息和属性
- 支持按大路段进行筛选和标绘

## 数据表结构

### sys_road_marker (公路编号表)
存储公路的基础信息，如G0412-南横段、G0412-玉横段等。

### sys_road_billing_segment (计费路段表)
存储具体的计费路段信息，包含几何坐标数据，通过road_marker_id关联到公路编号表。

## API接口

### 1. 获取路段完整几何信息

**接口地址：** `GET /system/road/billing/geometry`

**请求参数：**
- `code` (string, optional): 公路编码，如 "G0412"
- `sectionName` (string, optional): 路段名称，如 "南横段"
- `simplify` (boolean, optional): 是否启用坐标抽稀，默认true

**使用场景：**
1. **获取指定路段**: 传入 `code` 和 `sectionName` 参数
2. **获取所有路段**: 不传任何参数，返回所有路段的几何信息

**请求示例：**
```bash
# 获取指定路段（默认启用抽稀）
GET /system/road/billing/geometry?code=G0412&sectionName=南横段

# 获取指定路段（禁用抽稀，返回原始坐标）
GET /system/road/billing/geometry?code=G0412&sectionName=南横段&simplify=false

# 获取所有路段（默认启用抽稀）
GET /system/road/billing/geometry

# 获取所有路段（禁用抽稀）
GET /system/road/billing/geometry?simplify=false
```

**响应示例：**

**指定路段响应（单个路段）：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "roadCode": "G0412",
    "sectionName": "南横段",
    "totalLength": 13.789999961853028,
    "segmentCount": 2,
    "billingSegments": [
      {
        "id": 1,
        "name": "飞龙-平朗枢纽互通",
        "code": "S008xxxx",
        "direction": "上行",
        "length": 6.894999980926514,
        "kmStart": "K52+925",
        "kmEnd": "K46+289",
        "company": "灵山县",
        "planSpeed": 120,
        "carWayNum": "2|1",
        "coordinates": [
          [108.922332, 22.435356],
          [108.9219, 22.435481],
          [108.921198, 22.435659],
          [108.920516, 22.435803]
        ]
      },
      {
        "id": 2,
        "name": "飞龙-平朗枢纽互通",
        "code": "S008xxxx",
        "direction": "下行",
        "length": 6.894999980926514,
        "kmStart": "K52+925",
        "kmEnd": "K46+289",
        "company": "灵山县",
        "planSpeed": 120,
        "carWayNum": "2|1",
        "coordinates": [
          [108.922332, 22.435356],
          [108.9219, 22.435481],
          [108.921198, 22.435659],
          [108.920516, 22.435803]
        ]
      }
    ]
  }
}
```

**所有路段响应（路段数组）：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "roadCode": "G0412",
      "sectionName": "南横段",
      "totalLength": 13.789999961853028,
      "segmentCount": 2,
      "billingSegments": [
        {
          "id": 1,
          "name": "飞龙-平朗枢纽互通",
          "direction": "上行",
          "coordinates": [[108.922332, 22.435356], [108.9219, 22.435481]]
        },
        {
          "id": 2,
          "name": "飞龙-平朗枢纽互通",
          "direction": "下行",
          "coordinates": [[108.922332, 22.435356], [108.9219, 22.435481]]
        }
      ]
    },
    {
      "roadCode": "G0412",
      "sectionName": "玉横段",
      "totalLength": 25.456789012345678,
      "segmentCount": 4,
      "billingSegments": [
        {
          "id": 3,
          "name": "兴业南-福绵互通",
          "direction": "上行",
          "coordinates": [[109.123456, 23.456789], [109.234567, 23.567890]]
        }
      ]
    }
  ]
}
```

### 2. 根据路段ID获取几何信息

**接口地址：** `GET /system/road/billing/geometry/{roadMarkerId}`

**路径参数：**
- `roadMarkerId` (long, required): 公路编号ID

**请求示例：**
```
GET /system/road/billing/geometry/1
```

**响应格式：** 与接口1的指定路段响应相同

## 前端使用示例

### JavaScript调用示例

```javascript
// 获取指定路段几何信息并在地图上展示
async function loadRoadGeometry(code, sectionName) {
    try {
        const response = await axios.get('/system/road/billing/geometry', {
            params: { code, sectionName }
        });
        
        if (response.data.code === 200) {
            const data = response.data.data;
            console.log(`路段: ${data.roadCode} - ${data.sectionName}`);
            console.log(`计费路段数量: ${data.segmentCount}`);
            
            // 在地图上绘制每个计费路段
            data.billingSegments.forEach((segment, index) => {
                const color = getColorByDirection(segment.direction);
                drawSegmentOnMap(segment, color);
            });
        }
    } catch (error) {
        console.error('获取路段几何信息失败:', error);
    }
}

// 获取所有路段几何信息并在地图上展示
async function loadAllRoadGeometry() {
    try {
        const response = await axios.get('/system/road/billing/geometry');
        
        if (response.data.code === 200) {
            const roadSections = response.data.data;
            console.log(`获取到 ${roadSections.length} 个路段`);
            
            // 清除地图上现有的路段
            clearAllRoadsOnMap();
            
            // 在地图上绘制所有路段
            roadSections.forEach((roadSection, roadIndex) => {
                roadSection.billingSegments.forEach((segment, segmentIndex) => {
                    const color = getColorByRoadIndex(roadIndex);
                    drawSegmentOnMap(segment, color, roadSection.roadCode + '-' + roadSection.sectionName);
                });
            });
        }
    } catch (error) {
        console.error('获取所有路段几何信息失败:', error);
    }
}

// 在地图上绘制单个计费路段
function drawSegmentOnMap(segment, color, roadName = '') {
    if (!segment.coordinates || segment.coordinates.length === 0) {
        return;
    }
    
    // 创建折线
    const polyline = new AMap.Polyline({
        path: segment.coordinates,
        strokeColor: color,
        strokeWeight: 4,
        strokeOpacity: 0.8,
        strokeStyle: segment.direction === '上行' ? 'solid' : 'dashed'
    });
    
    // 添加到地图
    map.add(polyline);
    
    // 添加信息窗口
    polyline.on('click', () => {
        const infoWindow = new AMap.InfoWindow({
            content: `
                <div style="padding: 10px;">
                    <h4>${roadName} - ${segment.name}</h4>
                    <p>方向: ${segment.direction}</p>
                    <p>长度: ${segment.length}km</p>
                    <p>桩号: ${segment.kmStart} - ${segment.kmEnd}</p>
                    <p>管养单位: ${segment.company}</p>
                    <p>设计时速: ${segment.planSpeed}km/h</p>
                    <p>坐标点数量: ${segment.coordinates.length}</p>
                </div>
            `
        });
        infoWindow.open(map, segment.coordinates[0]);
    });
}

// 根据方向获取颜色
function getColorByDirection(direction) {
    return direction === '上行' ? '#FF5722' : '#2196F3';
}

// 根据路段索引获取颜色
function getColorByRoadIndex(index) {
    const colors = ['#FF5722', '#2196F3', '#4CAF50', '#FF9800', '#9C27B0'];
    return colors[index % colors.length];
}
```

## 数据结构说明

### RoadSectionGeometryVO (路段几何信息)
- `roadCode`: 公路编码
- `sectionName`: 路段名称  
- `totalLength`: 总长度
- `segmentCount`: 计费路段数量
- `billingSegments`: 计费路段列表

### BillingSegmentVO (计费路段)
- `id`: 计费路段ID
- `name`: 计费路段名称
- `code`: 计费路段编码
- `direction`: 方向（上行/下行）
- `length`: 长度
- `kmStart`: 起点桩号
- `kmEnd`: 终点桩号
- `company`: 管养单位
- `planSpeed`: 设计时速
- `carWayNum`: 车道数
- `coordinates`: 坐标点列表 `[[经度, 纬度], ...]`

## 优势特点

1. **层级清晰**: 大路段包含小路段列表，结构清晰
2. **信息完整**: 每个小路段包含完整的属性和几何信息
3. **筛选灵活**: 支持按大路段筛选或获取全部
4. **标绘方便**: 可以按路段、方向等维度进行差异化标绘
5. **扩展性好**: 便于后续添加更多路段属性
