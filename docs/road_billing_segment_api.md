# 高速公路计费路段API文档

## 概述
本文档描述了高速公路计费路段管理相关的API接口，主要用于获取路段的几何信息用于地图标注。

## 数据表结构

### sys_road_marker (公路编号表)
存储公路的基础信息，如G0412-南横段、G0412-玉横段等。

### sys_road_billing_segment (计费路段表)
存储具体的计费路段信息，包含几何坐标数据，通过road_marker_id关联到公路编号表。

## API接口

### 1. 获取路段完整几何信息

**接口地址：** `GET /system/road/billing/geometry`

**请求参数：**
- `code` (string, required): 公路编码，如 "G0412"
- `sectionName` (string, required): 路段名称，如 "南横段"

**请求示例：**
```
GET /system/road/billing/geometry?code=G0412&sectionName=南横段
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "roadCode": "G0412",
    "sectionName": "南横段",
    "totalLength": 13.789999961853028,
    "segmentCount": 2,
    "geometry": {
      "type": "FeatureCollection",
      "features": [
        {
          "type": "Feature",
          "properties": {
            "id": 1,
            "name": "飞龙-平朗枢纽互通",
            "direction": "上行",
            "length": 6.894999980926514,
            "kmStart": "K52+925",
            "kmEnd": "K46+289",
            "company": "灵xxx",
            "planSpeed": 120,
            "carWayNum": "2|1"
          },
          "geometry": {
            "type": "LineString",
            "coordinates": [
              [108.922332, 22.435356],
              [108.9219, 22.435481],
              [108.921198, 22.435659],
              [108.920516, 22.435803]
            ]
          }
        },
        {
          "type": "Feature",
          "properties": {
            "id": 2,
            "name": "飞龙-平朗枢纽互通",
            "direction": "下行",
            "length": 6.894999980926514,
            "kmStart": "K52+925",
            "kmEnd": "K46+289",
            "company": "灵xxx",
            "planSpeed": 120,
            "carWayNum": "2|1"
          },
          "geometry": {
            "type": "LineString",
            "coordinates": [
              [108.922332, 22.435356],
              [108.9219, 22.435481],
              [108.921198, 22.435659],
              [108.920516, 22.435803]
            ]
          }
        }
      ]
    }
  }
}
```

### 2. 根据路段ID获取几何信息

**接口地址：** `GET /system/road/billing/geometry/{roadMarkerId}`

**路径参数：**
- `roadMarkerId` (long, required): 公路编号ID

**请求示例：**
```
GET /system/road/billing/geometry/1
```

**响应格式：** 与接口1相同

### 3. 获取计费路段列表

**接口地址：** `GET /system/road/billing/segments/{roadMarkerId}`

**路径参数：**
- `roadMarkerId` (long, required): 公路编号ID

**请求示例：**
```
GET /system/road/billing/segments/1
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "roadMarkerId": 1,
      "code": "S008xxxx",
      "name": "飞龙-平朗枢纽互通",
      "geom": "{\"type\":\"LineString\",\"coordinates\":[[108.922332,22.435356],[108.9219,22.435481]]}",
      "lengthRoad": 6.894999980926514,
      "km1": "K52+925",
      "km2": "K46+289",
      "roadMin": "南xxx",
      "roadMax": "南xx",
      "company": "灵xxx",
      "carWayNum": "2|1",
      "planSpeed": 120,
      "direction": "上行",
      "milePost": "4251",
      "companyAlias": "新xxx",
      "highwayCode": "S81",
      "minPcu": 4240,
      "status": "0"
    }
  ]
}
```

## 前端使用示例

### JavaScript调用示例

```javascript
// 获取路段几何信息并在地图上展示
async function loadRoadGeometry(code, sectionName) {
    try {
        const response = await axios.get('/system/road/billing/geometry', {
            params: {
                code: code,
                sectionName: sectionName
            }
        });
        
        if (response.data.code === 200) {
            const data = response.data.data;
            console.log(`路段: ${data.roadCode} - ${data.sectionName}`);
            console.log(`总长度: ${data.totalLength}km, 路段数: ${data.segmentCount}`);
            
            // 在地图上绘制所有路段
            drawGeoJsonOnMap(data.geometry);
        }
    } catch (error) {
        console.error('获取路段几何信息失败:', error);
    }
}

// 在高德地图上绘制GeoJSON数据
function drawGeoJsonOnMap(geoJson) {
    geoJson.features.forEach(feature => {
        const coordinates = feature.geometry.coordinates;
        const properties = feature.properties;
        
        // 创建折线
        const polyline = new AMap.Polyline({
            path: coordinates,
            strokeColor: properties.direction === '上行' ? '#FF0000' : '#0000FF',
            strokeWeight: 4,
            strokeOpacity: 0.8
        });
        
        // 添加到地图
        map.add(polyline);
        
        // 添加信息窗口
        polyline.on('click', () => {
            const infoWindow = new AMap.InfoWindow({
                content: `
                    <div>
                        <h4>${properties.name}</h4>
                        <p>方向: ${properties.direction}</p>
                        <p>长度: ${properties.length}km</p>
                        <p>桩号: ${properties.kmStart} - ${properties.kmEnd}</p>
                    </div>
                `
            });
            infoWindow.open(map, polyline.getPath()[0]);
        });
    });
}
```

## 数据导入

可以使用以下SQL脚本导入计费路段数据：

```sql
-- 批量导入计费路段数据
INSERT INTO sys_road_billing_segment (
    road_marker_id, code, name, geom, length_road, km1, km2,
    road_min, road_max, company, car_way_num, plan_speed,
    direction, highway_code, status, create_by, create_time
) VALUES 
-- 数据行...
;

-- 更新关联关系
UPDATE sys_road_billing_segment rbs 
SET road_marker_id = (
    SELECT rm.id 
    FROM sys_road_marker rm 
    WHERE rm.code = rbs.highway_code 
    AND rm.name = rbs.road_max
    LIMIT 1
)
WHERE rbs.highway_code IS NOT NULL 
AND rbs.road_max IS NOT NULL;
```

## 注意事项

1. **GeoJSON格式**: 几何信息使用标准的GeoJSON LineString格式存储
2. **坐标系统**: 使用WGS84坐标系统 (EPSG:4326)
3. **性能优化**: 建议对大量数据进行分页查询
4. **缓存策略**: 可以考虑对几何信息进行缓存以提高性能
