<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>坐标抽稀效果对比测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=YOUR_AMAP_KEY"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }
        .control-group {
            margin-bottom: 10px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select, button {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .map-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .map-box {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .map-title {
            background: #007bff;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
        .map {
            width: 100%;
            height: 400px;
        }
        .stats {
            margin-top: 15px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>坐标抽稀效果对比测试</h1>
        
        <div class="controls">
            <div class="control-group">
                <label>公路编码:</label>
                <input type="text" id="roadCode" value="G0412" placeholder="如: G0412">
            </div>
            <div class="control-group">
                <label>路段名称:</label>
                <input type="text" id="sectionName" value="南横段" placeholder="如: 南横段">
            </div>
            <div class="control-group">
                <button onclick="loadAndCompare()">加载并对比</button>
                <button onclick="clearMaps()">清除地图</button>
            </div>
        </div>
        
        <div class="map-container">
            <div class="map-box">
                <div class="map-title">原始坐标（未抽稀）</div>
                <div id="originalMap" class="map"></div>
            </div>
            <div class="map-box">
                <div class="map-title">抽稀后坐标</div>
                <div id="simplifiedMap" class="map"></div>
            </div>
        </div>
        
        <div class="stats">
            <h3>统计信息</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="originalCount">-</div>
                    <div class="stat-label">原始坐标点数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="simplifiedCount">-</div>
                    <div class="stat-label">抽稀后坐标点数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="reductionRatio">-</div>
                    <div class="stat-label">压缩比例</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="segmentCount">-</div>
                    <div class="stat-label">计费路段数量</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化两个地图
        var originalMap = new AMap.Map('originalMap', {
            zoom: 12,
            center: [108.9, 22.4],
            mapStyle: 'amap://styles/normal'
        });

        var simplifiedMap = new AMap.Map('simplifiedMap', {
            zoom: 12,
            center: [108.9, 22.4],
            mapStyle: 'amap://styles/normal'
        });

        // 存储绘制的图形
        var originalPolylines = [];
        var simplifiedPolylines = [];

        // 加载并对比数据
        async function loadAndCompare() {
            const roadCode = document.getElementById('roadCode').value.trim();
            const sectionName = document.getElementById('sectionName').value.trim();
            
            if (!roadCode || !sectionName) {
                alert('请输入公路编码和路段名称');
                return;
            }

            try {
                // 并行请求原始数据和抽稀数据
                const [originalResponse, simplifiedResponse] = await Promise.all([
                    fetch(`/system/road/billing/geometry?code=${encodeURIComponent(roadCode)}&sectionName=${encodeURIComponent(sectionName)}&simplify=false`),
                    fetch(`/system/road/billing/geometry?code=${encodeURIComponent(roadCode)}&sectionName=${encodeURIComponent(sectionName)}&simplify=true`)
                ]);

                if (!originalResponse.ok || !simplifiedResponse.ok) {
                    throw new Error('请求失败');
                }

                const originalData = await originalResponse.json();
                const simplifiedData = await simplifiedResponse.json();

                if (originalData.code === 200 && simplifiedData.code === 200) {
                    // 清除现有图形
                    clearMaps();
                    
                    // 绘制原始数据
                    drawOnMap(originalMap, originalData.data, originalPolylines, '#FF5722');
                    
                    // 绘制抽稀数据
                    drawOnMap(simplifiedMap, simplifiedData.data, simplifiedPolylines, '#2196F3');
                    
                    // 计算统计信息
                    calculateStats(originalData.data, simplifiedData.data);
                    
                    // 同步两个地图的视野
                    syncMapViews();
                } else {
                    alert('数据加载失败');
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                alert('加载数据失败: ' + error.message);
            }
        }

        // 在地图上绘制数据
        function drawOnMap(map, data, polylinesArray, color) {
            data.billingSegments.forEach((segment, index) => {
                if (segment.coordinates && segment.coordinates.length > 0) {
                    const polyline = new AMap.Polyline({
                        path: segment.coordinates,
                        strokeColor: color,
                        strokeWeight: 4,
                        strokeOpacity: 0.8,
                        strokeStyle: segment.direction === '上行' ? 'solid' : 'dashed'
                    });
                    
                    map.add(polyline);
                    polylinesArray.push(polyline);
                    
                    // 添加点击事件
                    polyline.on('click', function() {
                        const infoWindow = new AMap.InfoWindow({
                            content: `
                                <div style="padding: 10px;">
                                    <h4>${segment.name}</h4>
                                    <p>方向: ${segment.direction}</p>
                                    <p>坐标点数: ${segment.coordinates.length}</p>
                                </div>
                            `
                        });
                        infoWindow.open(map, segment.coordinates[0]);
                    });
                }
            });
        }

        // 计算统计信息
        function calculateStats(originalData, simplifiedData) {
            let originalTotal = 0;
            let simplifiedTotal = 0;
            
            originalData.billingSegments.forEach(segment => {
                originalTotal += segment.coordinates ? segment.coordinates.length : 0;
            });
            
            simplifiedData.billingSegments.forEach(segment => {
                simplifiedTotal += segment.coordinates ? segment.coordinates.length : 0;
            });
            
            const reductionRatio = originalTotal > 0 ? 
                ((originalTotal - simplifiedTotal) / originalTotal * 100).toFixed(1) + '%' : '-';
            
            document.getElementById('originalCount').textContent = originalTotal.toLocaleString();
            document.getElementById('simplifiedCount').textContent = simplifiedTotal.toLocaleString();
            document.getElementById('reductionRatio').textContent = reductionRatio;
            document.getElementById('segmentCount').textContent = originalData.billingSegments.length;
        }

        // 同步两个地图的视野
        function syncMapViews() {
            if (originalPolylines.length > 0) {
                const bounds = new AMap.Bounds();
                originalPolylines.forEach(polyline => {
                    polyline.getPath().forEach(point => {
                        bounds.extend(point);
                    });
                });
                
                const center = bounds.getCenter();
                const zoom = Math.min(originalMap.getFitZoomLevel(originalPolylines), 15);
                
                originalMap.setZoomAndCenter(zoom, center);
                simplifiedMap.setZoomAndCenter(zoom, center);
            }
        }

        // 清除地图
        function clearMaps() {
            if (originalPolylines.length > 0) {
                originalMap.remove(originalPolylines);
                originalPolylines = [];
            }
            
            if (simplifiedPolylines.length > 0) {
                simplifiedMap.remove(simplifiedPolylines);
                simplifiedPolylines = [];
            }
            
            // 重置统计信息
            document.getElementById('originalCount').textContent = '-';
            document.getElementById('simplifiedCount').textContent = '-';
            document.getElementById('reductionRatio').textContent = '-';
            document.getElementById('segmentCount').textContent = '-';
        }

        // 同步地图操作
        originalMap.on('moveend', function() {
            const center = originalMap.getCenter();
            const zoom = originalMap.getZoom();
            simplifiedMap.setZoomAndCenter(zoom, center);
        });

        simplifiedMap.on('moveend', function() {
            const center = simplifiedMap.getCenter();
            const zoom = simplifiedMap.getZoom();
            originalMap.setZoomAndCenter(zoom, center);
        });

        // 页面加载完成
        window.onload = function() {
            console.log('坐标抽稀效果对比测试页面已加载');
        };
    </script>
</body>
</html>
