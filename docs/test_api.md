# API测试指南

## 问题修复说明

修复了 `java.lang.ClassCastException: java.math.BigDecimal cannot be cast to java.lang.Double` 错误。

### 问题原因
JSON解析时，数字类型被解析为 `BigDecimal` 而不是 `Double`，导致类型转换失败。

### 修复方案
1. **类型安全转换**: 使用 `Number` 接口的 `doubleValue()` 方法进行转换
2. **异常处理**: 添加详细的异常捕获和日志记录
3. **调试支持**: 新增调试接口帮助排查问题

## 测试步骤

### 1. 使用调试接口测试
```bash
# 调试几何信息解析
curl -X GET "http://localhost:8380/debug/road/debug-geometry?code=G0412&sectionName=南横段" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2. 测试正常接口
```bash
# 获取路段几何信息
curl -X GET "http://localhost:8380/system/road/billing/geometry?code=G0412&sectionName=南横段" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 预期返回格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "roadCode": "G0412",
    "sectionName": "南横段",
    "totalLength": 13.79,
    "segmentCount": 2,
    "coordinates": [
      [108.922332, 22.435356],
      [108.9219, 22.435481],
      [108.921198, 22.435659],
      [108.920516, 22.435803]
    ]
  }
}
```

## 日志监控

启动应用后，观察日志输出：

```
INFO  - 开始合并 2 个路段的坐标点
DEBUG - 处理路段ID: 4216, 几何信息: {...}
DEBUG - 路段ID: 4216 包含 4 个坐标点
DEBUG - 坐标值[0]: 108.922332 (BigDecimal) -> 108.922332
DEBUG - 坐标值[1]: 22.435356 (BigDecimal) -> 22.435356
INFO  - 坐标合并完成，总共合并了 8 个坐标点
```

## 错误排查

如果仍然出现错误，请：

1. **检查数据库中的几何信息格式**:
```sql
SELECT id, name, geom FROM sys_road_billing_segment 
WHERE road_marker_id IN (
  SELECT id FROM sys_road_marker 
  WHERE code = 'G0412' AND name = '南横段'
) LIMIT 5;
```

2. **使用调试接口查看详细信息**:
```bash
curl -X GET "http://localhost:8380/debug/road/debug-geometry?code=G0412&sectionName=南横段"
```

3. **检查应用日志**:
```bash
tail -f logs/sys-info.log | grep -E "(mergeAllCoordinates|convertToDoubleList)"
```

## 常见问题

### Q1: 坐标数据为空
**原因**: 数据库中没有对应的路段数据或几何信息为空
**解决**: 检查数据库数据，确保路段关联正确

### Q2: 坐标类型转换失败
**原因**: 几何信息格式不正确或包含非数字数据
**解决**: 使用调试接口查看原始数据格式

### Q3: 路段顺序不正确
**原因**: 桩号排序逻辑问题
**解决**: 检查桩号格式，可能需要自定义排序逻辑

## 性能优化建议

1. **缓存几何信息**: 对于频繁查询的路段，可以考虑缓存
2. **分页处理**: 对于包含大量计费路段的大路段，考虑分页处理
3. **索引优化**: 确保数据库索引正确设置

## 下一步开发

1. **前端集成**: 将API集成到前端地图组件
2. **数据导入**: 批量导入计费路段数据
3. **权限控制**: 添加适当的权限验证
4. **监控告警**: 添加API性能监控
