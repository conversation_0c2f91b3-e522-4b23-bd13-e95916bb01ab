<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>高速公路路段地图展示示例</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=YOUR_AMAP_KEY"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }
        .control-group {
            margin-bottom: 10px;
        }
        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        input, select, button {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        #mapContainer {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .info {
            margin-top: 15px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .legend {
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        .legend-item {
            margin: 5px 0;
            display: flex;
            align-items: center;
        }
        .legend-color {
            width: 20px;
            height: 4px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>高速公路路段地图展示</h1>
        
        <div class="controls">
            <div class="control-group">
                <label>公路编码:</label>
                <input type="text" id="roadCode" value="G0412" placeholder="如: G0412">
            </div>
            <div class="control-group">
                <label>路段名称:</label>
                <input type="text" id="sectionName" value="南横段" placeholder="如: 南横段">
            </div>
            <div class="control-group">
                <button onclick="loadRoadGeometry()">加载指定路段</button>
                <button onclick="loadAllRoadGeometry()">加载所有路段</button>
                <button onclick="clearMap()">清除地图</button>
            </div>
        </div>
        
        <div style="position: relative;">
            <div id="mapContainer"></div>
            <div class="legend">
                <h4>图例</h4>
                <div class="legend-item">
                    <div class="legend-color" style="background: #FF5722;"></div>
                    <span>上行方向</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #2196F3; border: 2px dashed #2196F3; height: 0px;"></div>
                    <span>下行方向</span>
                </div>
            </div>
        </div>
        
        <div id="info" class="info" style="display: none;"></div>
    </div>

    <script>
        // 初始化地图
        var map = new AMap.Map('mapContainer', {
            zoom: 10,
            center: [108.9, 22.4], // 广西南宁附近
            mapStyle: 'amap://styles/normal'
        });

        // 存储当前绘制的路段
        var currentPolylines = [];

        // 加载指定路段几何信息
        async function loadRoadGeometry() {
            const roadCode = document.getElementById('roadCode').value.trim();
            const sectionName = document.getElementById('sectionName').value.trim();
            
            if (!roadCode || !sectionName) {
                showInfo('请输入公路编码和路段名称', 'error');
                return;
            }

            try {
                showInfo('正在加载指定路段信息...', 'info');
                
                // 这里需要替换为实际的API地址
                const response = await fetch(`/system/road/billing/geometry?code=${encodeURIComponent(roadCode)}&sectionName=${encodeURIComponent(sectionName)}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const data = result.data;
                    
                    // 显示路段信息
                    showInfo(`
                        <strong>路段信息:</strong><br>
                        公路编码: ${data.roadCode}<br>
                        路段名称: ${data.sectionName}<br>
                        总长度: ${data.totalLength}km<br>
                        计费路段数量: ${data.segmentCount}<br>
                        计费路段列表: ${data.billingSegments.length} 个
                    `, 'success');
                    
                    // 清除现有路段
                    clearMap();
                    
                    // 在地图上绘制每个计费路段
                    data.billingSegments.forEach((segment, index) => {
                        const color = segment.direction === '上行' ? '#FF5722' : '#2196F3';
                        const roadName = `${data.roadCode}-${data.sectionName}`;
                        drawSegmentOnMap(segment, color, roadName);
                    });
                    
                    // 自动调整地图视野
                    if (currentPolylines.length > 0) {
                        map.setFitView(currentPolylines, false, [50, 50, 50, 50]);
                    }
                    
                } else {
                    showInfo(`加载失败: ${result.msg}`, 'error');
                }
                
            } catch (error) {
                console.error('加载路段信息失败:', error);
                showInfo(`加载失败: ${error.message}`, 'error');
            }
        }

        // 加载所有路段几何信息
        async function loadAllRoadGeometry() {
            try {
                showInfo('正在加载所有路段信息...', 'info');
                
                // 这里需要替换为实际的API地址
                const response = await fetch('/system/road/billing/geometry');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const roadSections = result.data;
                    
                    // 显示路段信息
                    let totalSegments = 0;
                    roadSections.forEach(section => {
                        totalSegments += section.billingSegments.length;
                    });
                    
                    showInfo(`
                        <strong>所有路段信息:</strong><br>
                        大路段总数: ${roadSections.length}<br>
                        计费路段总数: ${totalSegments}<br>
                        已在地图上标绘所有路段
                    `, 'success');
                    
                    // 清除现有路段
                    clearMap();
                    
                    // 在地图上绘制所有路段
                    roadSections.forEach((roadSection, roadIndex) => {
                        const roadName = `${roadSection.roadCode}-${roadSection.sectionName}`;
                        
                        roadSection.billingSegments.forEach((segment, segmentIndex) => {
                            const color = segment.direction === '上行' ? '#FF5722' : '#2196F3';
                            drawSegmentOnMap(segment, color, roadName);
                        });
                    });
                    
                    // 自动调整地图视野
                    if (currentPolylines.length > 0) {
                        map.setFitView(currentPolylines, false, [50, 50, 50, 50]);
                    }
                    
                } else {
                    showInfo(`加载失败: ${result.msg}`, 'error');
                }
                
            } catch (error) {
                console.error('加载所有路段信息失败:', error);
                showInfo(`加载失败: ${error.message}`, 'error');
            }
        }

        // 在地图上绘制单个计费路段
        function drawSegmentOnMap(segment, color, roadName) {
            if (!segment.coordinates || segment.coordinates.length === 0) {
                console.warn('计费路段坐标为空:', segment.name);
                return;
            }

            // 创建折线
            const polyline = new AMap.Polyline({
                path: segment.coordinates,
                strokeColor: color,
                strokeWeight: 6,
                strokeOpacity: 0.8,
                strokeStyle: segment.direction === '上行' ? 'solid' : 'dashed',
                lineJoin: 'round',
                lineCap: 'round'
            });

            // 添加到地图
            map.add(polyline);
            currentPolylines.push(polyline);

            // 添加信息窗口
            polyline.on('click', function(e) {
                const infoWindow = new AMap.InfoWindow({
                    content: `
                        <div style="padding: 10px; min-width: 200px;">
                            <h4>${roadName}</h4>
                            <p><strong>计费路段:</strong> ${segment.name}</p>
                            <p><strong>方向:</strong> ${segment.direction}</p>
                            <p><strong>长度:</strong> ${segment.length}km</p>
                            <p><strong>桩号:</strong> ${segment.kmStart} - ${segment.kmEnd}</p>
                            <p><strong>管养单位:</strong> ${segment.company}</p>
                            <p><strong>设计时速:</strong> ${segment.planSpeed}km/h</p>
                            <p><strong>车道数:</strong> ${segment.carWayNum}</p>
                            <p><strong>坐标点数量:</strong> ${segment.coordinates.length}</p>
                            <p><strong>点击位置:</strong> [${e.lnglat.lng.toFixed(6)}, ${e.lnglat.lat.toFixed(6)}]</p>
                        </div>
                    `,
                    offset: new AMap.Pixel(0, -30)
                });
                infoWindow.open(map, e.lnglat);
            });
        }

        // 清除地图上的路段
        function clearMap() {
            if (currentPolylines.length > 0) {
                map.remove(currentPolylines);
                currentPolylines = [];
            }
        }

        // 显示信息
        function showInfo(message, type = 'info') {
            const infoDiv = document.getElementById('info');
            infoDiv.innerHTML = message;
            infoDiv.className = `info ${type}`;
            infoDiv.style.display = 'block';
        }

        // 页面加载完成后自动加载示例数据
        window.onload = function() {
            showInfo('地图已初始化。可以输入公路编码和路段名称加载指定路段，或直接点击"加载所有路段"按钮查看所有路段。<br><strong>图例:</strong> 实线=上行，虚线=下行', 'info');
        };
    </script>
</body>
</html>
