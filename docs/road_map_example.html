<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>高速公路路段地图展示示例</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=YOUR_AMAP_KEY"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }
        .control-group {
            margin-bottom: 10px;
        }
        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        input, select, button {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        #mapContainer {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .info {
            margin-top: 15px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>高速公路路段地图展示</h1>
        
        <div class="controls">
            <div class="control-group">
                <label>公路编码:</label>
                <input type="text" id="roadCode" value="G0412" placeholder="如: G0412">
            </div>
            <div class="control-group">
                <label>路段名称:</label>
                <input type="text" id="sectionName" value="南横段" placeholder="如: 南横段">
            </div>
            <div class="control-group">
                <button onclick="loadRoadGeometry()">加载路段</button>
                <button onclick="clearMap()">清除地图</button>
            </div>
        </div>
        
        <div id="mapContainer"></div>
        
        <div id="info" class="info" style="display: none;"></div>
    </div>

    <script>
        // 初始化地图
        var map = new AMap.Map('mapContainer', {
            zoom: 10,
            center: [108.9, 22.4], // 广西南宁附近
            mapStyle: 'amap://styles/normal'
        });

        // 存储当前绘制的路段
        var currentPolylines = [];

        // 加载路段几何信息
        async function loadRoadGeometry() {
            const roadCode = document.getElementById('roadCode').value.trim();
            const sectionName = document.getElementById('sectionName').value.trim();
            
            if (!roadCode || !sectionName) {
                showInfo('请输入公路编码和路段名称', 'error');
                return;
            }

            try {
                showInfo('正在加载路段信息...', 'info');
                
                // 这里需要替换为实际的API地址
                const response = await fetch(`/system/road/billing/geometry?code=${encodeURIComponent(roadCode)}&sectionName=${encodeURIComponent(sectionName)}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.code === 200) {
                    const data = result.data;
                    
                    // 显示路段信息
                    showInfo(`
                        <strong>路段信息:</strong><br>
                        公路编码: ${data.roadCode}<br>
                        路段名称: ${data.sectionName}<br>
                        总长度: ${data.totalLength}km<br>
                        计费路段数量: ${data.segmentCount}<br>
                        坐标点数量: ${data.coordinates.length}
                    `, 'success');
                    
                    // 在地图上绘制路段
                    drawRoadOnMap(data.coordinates, `${data.roadCode}-${data.sectionName}`);
                    
                } else {
                    showInfo(`加载失败: ${result.msg}`, 'error');
                }
                
            } catch (error) {
                console.error('加载路段信息失败:', error);
                showInfo(`加载失败: ${error.message}`, 'error');
            }
        }

        // 在地图上绘制路段
        function drawRoadOnMap(coordinates, roadName) {
            // 清除之前的路段
            clearMap();
            
            if (!coordinates || coordinates.length === 0) {
                showInfo('没有可绘制的坐标数据', 'error');
                return;
            }

            // 创建折线
            const polyline = new AMap.Polyline({
                path: coordinates,
                strokeColor: '#FF5722',
                strokeWeight: 6,
                strokeOpacity: 0.8,
                strokeStyle: 'solid',
                lineJoin: 'round',
                lineCap: 'round'
            });

            // 添加到地图
            map.add(polyline);
            currentPolylines.push(polyline);

            // 添加起点和终点标记
            const startMarker = new AMap.Marker({
                position: coordinates[0],
                icon: new AMap.Icon({
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/start.png',
                    size: new AMap.Size(25, 34),
                    imageSize: new AMap.Size(25, 34)
                }),
                title: '起点'
            });

            const endMarker = new AMap.Marker({
                position: coordinates[coordinates.length - 1],
                icon: new AMap.Icon({
                    image: 'https://webapi.amap.com/theme/v1.3/markers/n/end.png',
                    size: new AMap.Size(25, 34),
                    imageSize: new AMap.Size(25, 34)
                }),
                title: '终点'
            });

            map.add([startMarker, endMarker]);
            currentPolylines.push(startMarker, endMarker);

            // 添加信息窗口
            polyline.on('click', function(e) {
                const infoWindow = new AMap.InfoWindow({
                    content: `
                        <div style="padding: 10px;">
                            <h4>${roadName}</h4>
                            <p>坐标点数量: ${coordinates.length}</p>
                            <p>起点: [${coordinates[0][0].toFixed(6)}, ${coordinates[0][1].toFixed(6)}]</p>
                            <p>终点: [${coordinates[coordinates.length-1][0].toFixed(6)}, ${coordinates[coordinates.length-1][1].toFixed(6)}]</p>
                            <p>点击位置: [${e.lnglat.lng.toFixed(6)}, ${e.lnglat.lat.toFixed(6)}]</p>
                        </div>
                    `,
                    offset: new AMap.Pixel(0, -30)
                });
                infoWindow.open(map, e.lnglat);
            });

            // 自动调整地图视野
            map.setFitView(currentPolylines, false, [50, 50, 50, 50]);
        }

        // 清除地图上的路段
        function clearMap() {
            if (currentPolylines.length > 0) {
                map.remove(currentPolylines);
                currentPolylines = [];
            }
        }

        // 显示信息
        function showInfo(message, type = 'info') {
            const infoDiv = document.getElementById('info');
            infoDiv.innerHTML = message;
            infoDiv.className = `info ${type}`;
            infoDiv.style.display = 'block';
        }

        // 页面加载完成后自动加载示例数据
        window.onload = function() {
            showInfo('地图已初始化，请输入公路编码和路段名称后点击"加载路段"按钮', 'info');
        };
    </script>
</body>
</html>
