# 新版路段API测试指南

## 数据结构变更说明

### 变更前（合并坐标模式）
```json
{
  "roadCode": "G0412",
  "sectionName": "南横段",
  "coordinates": [
    [108.922332, 22.435356],
    [108.9219, 22.435481]
  ]
}
```

### 变更后（分层结构模式）
```json
{
  "roadCode": "G0412",
  "sectionName": "南横段",
  "totalLength": 13.79,
  "segmentCount": 2,
  "billingSegments": [
    {
      "id": 1,
      "name": "飞龙-平朗枢纽互通",
      "direction": "上行",
      "length": 6.895,
      "coordinates": [[108.922332, 22.435356], [108.9219, 22.435481]]
    },
    {
      "id": 2,
      "name": "飞龙-平朗枢纽互通", 
      "direction": "下行",
      "length": 6.895,
      "coordinates": [[108.922332, 22.435356], [108.9219, 22.435481]]
    }
  ]
}
```

## 测试步骤

### 1. 测试指定路段查询
```bash
curl -X GET "http://localhost:8380/system/road/billing/geometry?code=G0412&sectionName=南横段" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

**预期返回：** 单个路段对象，包含 `billingSegments` 数组

### 2. 测试所有路段查询
```bash
curl -X GET "http://localhost:8380/system/road/billing/geometry" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

**预期返回：** 路段对象数组，每个对象包含 `billingSegments` 数组

### 3. 测试根据ID查询
```bash
curl -X GET "http://localhost:8380/system/road/billing/geometry/1" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

**预期返回：** 单个路段对象

## 前端适配指南

### 原来的代码（需要修改）
```javascript
// 旧版本 - 直接使用合并的坐标
const coordinates = response.data.data.coordinates;
drawRoadLineOnMap(coordinates, roadName);
```

### 新版本代码
```javascript
// 新版本 - 遍历计费路段
const billingSegments = response.data.data.billingSegments;
billingSegments.forEach(segment => {
    const color = segment.direction === '上行' ? '#FF5722' : '#2196F3';
    const style = segment.direction === '上行' ? 'solid' : 'dashed';
    drawSegmentOnMap(segment.coordinates, segment.name, color, style);
});
```

### 绘制函数示例
```javascript
function drawSegmentOnMap(coordinates, segmentName, color, style) {
    const polyline = new AMap.Polyline({
        path: coordinates,
        strokeColor: color,
        strokeWeight: 6,
        strokeStyle: style,
        strokeOpacity: 0.8
    });
    
    map.add(polyline);
    
    // 添加点击事件
    polyline.on('click', () => {
        showSegmentInfo(segmentName, coordinates.length);
    });
}
```

## 优势对比

### 新版本优势
1. **信息完整**: 每个计费路段包含完整的属性信息
2. **层级清晰**: 大路段 -> 计费路段的层级结构
3. **筛选灵活**: 可以按方向、长度等属性筛选
4. **标绘多样**: 支持不同样式标绘（实线/虚线、不同颜色）
5. **交互丰富**: 点击可显示详细的路段信息

### 使用场景
1. **全量标绘**: 调用无参接口，在地图上标绘所有路段
2. **分类标绘**: 按上行/下行使用不同样式
3. **详细信息**: 点击路段显示桩号、管养单位等详细信息
4. **路段筛选**: 根据大路段进行筛选显示

## 测试检查点

### 数据完整性检查
- [ ] `billingSegments` 数组不为空
- [ ] 每个计费路段包含 `coordinates` 数组
- [ ] 坐标数组格式正确 `[[经度, 纬度], ...]`
- [ ] 包含必要的属性字段（name, direction, length等）

### 功能性检查
- [ ] 指定路段查询返回正确数据
- [ ] 所有路段查询返回数组格式
- [ ] 根据ID查询正常工作
- [ ] 前端能正确解析和绘制

### 性能检查
- [ ] 查询响应时间合理（< 2秒）
- [ ] 大量路段数据不会导致前端卡顿
- [ ] 内存使用正常

## 故障排除

### 常见问题
1. **billingSegments为空**: 检查数据库关联关系
2. **坐标解析失败**: 检查GeoJSON格式
3. **前端显示异常**: 检查数据结构适配

### 调试工具
```bash
# 使用调试接口查看详细信息
curl -X GET "http://localhost:8380/debug/road/debug-geometry?code=G0412&sectionName=南横段"
```

## 迁移建议

1. **逐步迁移**: 先测试新接口，确认无误后替换前端代码
2. **保留备份**: 保留旧版本代码作为备份
3. **充分测试**: 在各种数据场景下测试新接口
4. **用户培训**: 如有必要，培训用户使用新的交互方式
