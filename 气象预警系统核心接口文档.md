# 气象预警系统核心接口文档

## 概述

本文档描述了气象预警系统的核心接口，包括预警创建、通知发送、确认处理和详情查询等功能。

### 基础信息
- **数据格式**: JSON
- **字符编码**: UTF-8

---

## 1. 新增气象预警

**接口地址**: `POST /weather/warning`

**接口描述**: 创建新的气象预警信息，同时生成预警发布告警

**权限要求**: `weather:warning:add`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningType | String | 是 | 预警类型（1-暴雨 2-台风 3-雷电等） | "1" |
| warningLevel | String | 是 | 预警等级（5-蓝色 6-黄色 7-橙色 8-红色） | "8" |
| warningContent | String | 是 | 预警内容描述 | "预计未来4小时内..." |
| preventionGuide | String | 否 | 防御指南 | "政府及相关部门..." |
| affectedRoads | String | 否 | 受影响道路 | "G80广昆高速..." |
| issueTime | String | 是 | 发布时间 | "2025-6-9 18:00:00" |
| expireTime | String | 否 | 失效时间 | "2025-6-10 18:00:00" |
| affectedAreas | Array | 是 | 影响区域列表 | 见下方示例 |

**影响区域对象结构**:
```json
{
  "regionId": "450100",    // 区域ID
  "regionName": "南宁市"   // 区域名称
}
```

**请求示例**:
```json
{
  "warningType": "1",
  "warningLevel": "8",
  "warningContent": "预计未来4小时内，南宁市区及各县区将出现100毫米以上强降雨，局部地区可能超过150毫米，并伴有雷电、短时大风等强对流天气。请注意防范山洪、地质灾害、城市内涝等次生灾害。",
  "preventionGuide": "政府及相关部门按照职责做好防暴雨应急工作；切断有危险的室外电源，暂停户外作业；处于危险地带的单位应当停课、停业，采取专门措施保护已到校学生、幼儿和其他上班人员的安全。",
  "affectedRoads": "G80广昆高速玉林段；S21玉铁高速全线；G324国道玉林至北流段",
  "issueTime": "2025-6-9 18:00:00",
  "expireTime": "2025-6-10 18:00:00",
  "affectedAreas": [
    {
      "regionId": "450100",
      "regionName": "南宁市"
    },
    {
      "regionId": "450103",
      "regionName": "青秀区"
    },
    {
      "regionId": "450105",
      "regionName": "江南区"
    }
  ]
}
```

**返回示例**:
```json
{
  "msg": "预警创建成功，ID：de28c6b8-f528-4a7c-9d52-848b8b8ff588",
  "code": 200
}
```

**业务说明**:
- 创建预警记录，状态为"有效"，通知状态为"未通知"
- 自动生成预警发布告警（告警类型3，子类型7）
- 告警归属于广西壮族自治区交通运输厅

---

## 2. 气象预警通知

**接口地址**: `POST /weather/warning/{warningId}/notify`

**接口描述**: 向指定人员发送预警通知，生成通知确认告警

**权限要求**: `weather:warning:edit`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID（路径参数） | "de28c6b8-f528-4a7c-9d52-848b8b8ff588" |
| notifyTargets | Array | 是 | 通知对象列表 | 见下方示例 |

**通知对象结构**:
```json
{
  "contactUserId": 1,                                    // 联系人用户ID
  "contactUserName": "系统管理员",                        // 联系人姓名
  "contactUnitId": "100",                               // 联系人单位ID
  "contactUnitName": "广西壮族自治区交通运输厅",           // 联系人单位名称
  "contactDeptName": "财务部门",                         // 联系人部门名称
  "contactPostName": "科长",                            // 联系人岗位名称
  "contactPhone": "18775356868",                        // 联系人电话
  "contactEmail": "<EMAIL>"                // 联系人邮箱
}
```

**请求示例**:
```json
{
  "notifyTargets": [
    {
      "contactUserId": 1,
      "contactUserName": "系统管理员",
      "contactUnitId": "100",
      "contactUnitName": "广西壮族自治区交通运输厅",
      "contactDeptName": "财务部门",
      "contactPostName": "科长",
      "contactPhone": "18775356868",
      "contactEmail": "<EMAIL>"
    },
    {
      "contactUserId": 2,
      "contactUserName": "若依",
      "contactUnitId": "103",
      "contactUnitName": "自治区公路发展中心",
      "contactDeptName": "安全监管科",
      "contactPostName": "副科长",
      "contactPhone": "18977998561",
      "contactEmail": "<EMAIL>"
    }
  ]
}
```

**返回示例**:
```json
{
  "msg": "通知发送成功，共2条",
  "code": 200
}
```

**业务说明**:
- 更新预警状态为"已通知"
- 创建通知记录
- 按单位分组生成通知确认告警（告警类型3，子类型8）
- 告警归属于各通知对象所在单位

---

## 3. 确认气象预警通知

**接口地址**: `POST /weather/notification/confirm/{warningId}`

**接口描述**: 确认收到的预警通知（使用当前登录用户ID）

**权限要求**: `weather:notification:confirm`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID（路径参数） | "de28c6b8-f528-4a7c-9d52-848b8b8ff588" |

**返回示例**:
```json
{
  "msg": "确认成功",
  "code": 200
}
```

**业务说明**:
- 自动使用当前登录用户ID进行确认
- 更新通知记录的确认状态和确认时间
- 记录确认人信息

---

## 4. 气象预警详情（通过预警ID查询）

**接口地址**: `GET /weather/warning/{warningId}`

**接口描述**: 根据预警ID查询预警详情，包含完整的通知进展信息

**权限要求**: `weather:warning:query`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningId | String | 是 | 预警ID（路径参数） | "de28c6b8-f528-4a7c-9d52-848b8b8ff588" |

**返回示例**:
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": {
    "warningId": "de28c6b8-f528-4a7c-9d52-848b8b8ff588",        // 预警ID
    "warningType": "1",                                          // 预警类型
    "warningTypeLabel": "暴雨预警",                              // 预警类型标签
    "warningLevel": "8",                                         // 预警等级
    "warningLevelLabel": "红色预警",                             // 预警等级标签
    "warningContent": "预计未来4小时内...",                      // 预警内容
    "preventionGuide": "政府及相关部门按照职责...",              // 防御指南
    "affectedRoads": "G80广昆高速玉林段...",                     // 受影响道路
    "issueTime": "2025-06-09 18:00:00",                         // 发布时间
    "expireTime": "2025-06-10 18:00:00",                        // 失效时间
    "status": "0",                                               // 状态（0-有效 1-失效 2-取消）
    "statusLabel": "有效",                                       // 状态标签
    "isNotified": "1",                                           // 是否已通知（0-未通知 1-已通知）
    "isNotifiedLabel": "已通知",                                 // 是否已通知标签
    "createTime": "2025-06-09 17:38:48",                        // 创建时间
    "createBy": "admin",                                         // 创建人
    "affectedAreas": [                                           // 影响区域列表
      {
        "warningId": "de28c6b8-f528-4a7c-9d52-848b8b8ff588",
        "regionId": "450100",                                    // 区域ID
        "regionName": "南宁市",                                  // 区域名称
        "regionFullPath": null,                                  // 完整路径
        "createTime": "2025-06-09 17:38:48"                     // 创建时间
      }
    ],
    "affectedAreasDesc": "南宁市、青秀区、江南区",               // 影响区域描述
    "totalNotifications": 2,                                     // 总通知数
    "confirmedNotifications": 1,                                 // 已确认数
    "unconfirmedNotifications": 1,                               // 未确认数
    "timeoutNotifications": 0,                                   // 超时数
    "alarmType": "warning_detail",                               // 告警类型标识
    "currentUserConfirmStatus": null,                            // 当前用户确认状态
    "currentUserConfirmTime": null,                              // 当前用户确认时间
    "notificationProgress": [                                    // 通知进展详情
      {
        "warningId": "de28c6b8-f528-4a7c-9d52-848b8b8ff588",
        "contactUserId": 1,                                      // 联系人用户ID
        "contactUnitName": "广西壮族自治区交通运输厅",           // 联系人单位
        "contactDeptName": "财务部门",                           // 联系人部门
        "contactPostName": "科长",                               // 联系人岗位
        "contactUserName": "系统管理员",                         // 联系人姓名
        "contactPhone": "18775356868",                           // 联系人电话
        "notificationTime": "2025-06-09 17:48:21",              // 通知时间
        "confirmStatus": "1",                                    // 确认状态（0-未确认 1-已确认）
        "confirmStatusLabel": "已确认",                          // 确认状态标签
        "confirmTime": "2025-06-09 17:49:48",                   // 确认时间
        "confirmUserName": "admin",                              // 确认人
        "timeoutMinutes": 15,                                    // 超时时间（分钟）
        "isTimeout": "0",                                        // 是否超时（0-未超时 1-已超时）
        "remainingMinutes": 0                                    // 剩余时间（分钟）
      }
    ]
  }
}
```

**业务说明**:
- 返回完整的预警信息和字典标签
- 包含影响区域列表和描述
- 如果已通知，返回详细的通知进展信息
- 显示每个被通知人员的确认状态

---

## 5. 气象预警详情（通过告警ID查询）

**接口地址**: `GET /weather/notification/warning-detail/{alarmId}`

**接口描述**: 根据告警ID查询预警详情，根据告警类型决定返回内容

**权限要求**: `weather:notification:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| alarmId | String | 是 | 告警ID（路径参数） | "9ecab212-0893-425e-bbc7-5989007bea1d" |

**返回示例**（通知确认告警）:
```json
{
  "msg": "操作成功",
  "code": 200,
  "data": {
    "warningId": "de28c6b8-f528-4a7c-9d52-848b8b8ff588",
    "warningType": "1",
    "warningTypeLabel": null,                                    // 字典标签可能为空
    "warningLevel": "8",
    "warningLevelLabel": null,
    "warningContent": "预计未来4小时内...",
    "preventionGuide": "政府及相关部门按照职责...",
    "affectedRoads": "G80广昆高速玉林段...",
    "issueTime": "2025-06-09 18:00:00",
    "expireTime": "2025-06-10 18:00:00",
    "status": "0",
    "statusLabel": null,
    "isNotified": "1",
    "isNotifiedLabel": null,
    "createTime": "2025-06-09 17:38:48",
    "createBy": "admin",
    "affectedAreas": null,                                       // 通知确认告警不返回区域列表
    "affectedAreasDesc": "南宁市、江南区、青秀区等3个区域",
    "totalNotifications": null,                                  // 通知确认告警不返回统计信息
    "confirmedNotifications": null,
    "unconfirmedNotifications": null,
    "timeoutNotifications": null,
    "alarmType": "notification_confirm",                         // 告警类型标识
    "currentUserConfirmStatus": "0",                             // 当前用户确认状态
    "currentUserConfirmTime": null,                              // 当前用户确认时间
    "notificationProgress": null                                 // 通知确认告警不返回进展列表
  }
}
```

**业务说明**:
- 先根据告警ID查询告警信息，再查询预警详情
- **预警发布告警**（子类型7）：返回完整的通知进展信息
- **通知确认告警**（子类型8）：只返回当前用户的确认状态
- 根据告警类型智能决定返回内容

---

## 6. 预警发布告警列表

**接口地址**: `GET /alarm/list?alarmType=3&alarmSubtype=7`

**接口描述**: 查询预警发布告警列表

**权限要求**: `alarm:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| alarmType | String | 是 | 告警类型 | "3" |
| alarmSubtype | String | 是 | 告警子类型 | "7" |
| pageNum | Integer | 否 | 页码 | 1 |
| pageSize | Integer | 否 | 每页数量 | 10 |

**业务说明**:
- 查询气象预警发布告警
- 告警归属于广西壮族自治区交通运输厅
- 可点击查看预警详情和通知进展

---

## 7. 通知确认告警列表

**接口地址**: `GET /alarm/list?alarmType=3&alarmSubtype=8`

**接口描述**: 查询通知确认告警列表

**权限要求**: `alarm:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| alarmType | String | 是 | 告警类型 | "3" |
| alarmSubtype | String | 是 | 告警子类型 | "8" |
| pageNum | Integer | 否 | 页码 | 1 |
| pageSize | Integer | 否 | 每页数量 | 10 |

**业务说明**:
- 查询气象预警通知确认告警
- 告警归属于各通知对象所在单位
- 可点击确认预警通知

---

## 8. 气象预警详细列表

**接口地址**: `GET /weather/warning/detail-list`

**接口描述**: 查询气象预警详细列表，包含影响区域和通知记录，支持条件筛选和分页

**权限要求**: `weather:warning:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|---|---|---|---|---|
| warningType | String | 否 | 预警类型 | "1" |
| warningLevel | String | 否 | 预警等级 | "8" |
| status | String | 否 | 预警状态 | "0" |
| isNotified | String | 否 | 通知状态 | "1" |
| warningContent | String | 否 | 预警内容关键字 | "暴雨" |
| createBy | String | 否 | 创建人 | "admin" |
| beginTime | String | 否 | 开始时间 | "2024-12-01" |
| endTime | String | 否 | 结束时间 | "2024-12-31" |
| pageNum | Integer | 否 | 页码 | 1 |
| pageSize | Integer | 否 | 每页数量 | 10 |

**返回示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "warningId": "de28c6b8-f528-4a7c-9d52-848b8b8ff588",
      "warningType": "1",
      "warningTypeLabel": "暴雨预警",
      "warningLevel": "8",
      "warningLevelLabel": "红色预警",
      "warningContent": "预计未来4小时内，南宁市区及各县区将出现100毫米以上强降雨...",
      "preventionGuide": "政府及相关部门按照职责做好防暴雨应急工作...",
      "affectedRoads": "G80广昆高速玉林段；S21玉铁高速全线...",
      "issueTime": "2025-06-09 18:00:00",
      "expireTime": "2025-06-10 18:00:00",
      "status": "0",
      "statusLabel": "有效",
      "isNotified": "1",
      "isNotifiedLabel": "已通知",
      "createTime": "2025-06-09 17:38:48",
      "createBy": "admin",
      "affectedAreas": [
        {
          "warningId": "de28c6b8-f528-4a7c-9d52-848b8b8ff588",
          "regionId": "450100",
          "regionName": "南宁市",
          "regionFullPath": null,
          "createTime": "2025-06-09 17:38:48"
        },
        {
          "warningId": "de28c6b8-f528-4a7c-9d52-848b8b8ff588",
          "regionId": "450103",
          "regionName": "青秀区",
          "regionFullPath": null,
          "createTime": "2025-06-09 17:38:48"
        }
      ],
      "affectedAreasDesc": "南宁市、青秀区、江南区等3个区域",
      "totalNotifications": 2,
      "confirmedNotifications": 1,
      "unconfirmedNotifications": 1,
      "timeoutNotifications": 0,
      "alarmType": "warning_list",
      "notificationProgress": [
        {
          "warningId": "de28c6b8-f528-4a7c-9d52-848b8b8ff588",
          "contactUserId": 1,
          "contactUnitName": "广西壮族自治区交通运输厅",
          "contactDeptName": "财务部门",
          "contactPostName": "科长",
          "contactUserName": "系统管理员",
          "contactPhone": "18775356868",
          "notificationTime": "2025-06-09 17:48:21",
          "confirmStatus": "1",
          "confirmStatusLabel": "已确认",
          "confirmTime": "2025-06-09 17:49:48",
          "confirmUserName": "admin",
          "timeoutMinutes": 15,
          "isTimeout": "0",
          "remainingMinutes": 0
        },
        {
          "warningId": "de28c6b8-f528-4a7c-9d52-848b8b8ff588",
          "contactUserId": 2,
          "contactUnitName": "自治区公路发展中心",
          "contactDeptName": "安全监管科",
          "contactPostName": "副科长",
          "contactUserName": "若依",
          "contactPhone": "18977998561",
          "notificationTime": "2025-06-09 17:48:21",
          "confirmStatus": "0",
          "confirmStatusLabel": "未确认",
          "confirmTime": null,
          "confirmUserName": null,
          "timeoutMinutes": 15,
          "isTimeout": "0",
          "remainingMinutes": 13
        }
      ]
    }
  ],
  "total": 1
}
```

**业务说明**:
- 支持多种条件筛选：预警类型、等级、状态、通知状态等
- 支持分页查询，避免数据量过大
- 返回完整的预警信息、影响区域和通知记录
- 包含通知统计信息：总数、已确认数、未确认数、超时数
- 显示每个被通知人员的详细确认状态

---

## 错误码说明

| 错误码 | 描述 |
|---|---|
| 200 | 操作成功 |
| 401 | 未授权，请先登录 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

---

## 使用流程

1. **创建预警** → 调用新增气象预警接口
2. **发送通知** → 调用气象预警通知接口
3. **查看告警** → 查询告警列表（预警发布告警或通知确认告警）
4. **查看详情** → 根据告警ID或预警ID查询详情
5. **确认通知** → 调用确认气象预警通知接口

---

## 联系方式

如有问题请联系开发团队。
