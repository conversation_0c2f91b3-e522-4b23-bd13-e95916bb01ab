package com.tocc.framework.config;

import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.framework.web.exception.GlobalExceptionHandler;
import org.apache.ibatis.binding.MapperMethod.ParamMap;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

/**
 * mybatis拦截器，自动注入创建人、创建时间、修改人、修改时间
 * <AUTHOR>
 * @Date  2019-01-19
 *
 */
@Component
@Intercepts({
//		@Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
		@Signature(type = Executor.class, method = "update", args = { MappedStatement.class, Object.class }) })
public class MybatisInterceptor implements Interceptor {

	private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

	/**
	 * 获取类的所有属性，包括父类
	 *
	 * @param object
	 * @return
	 */
	public static Field[] getAllFields(Object object) {
		Class<?> clazz = object.getClass();
		List<Field> fieldList = new ArrayList<>();
		while (clazz != null) {
			fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
			clazz = clazz.getSuperclass();
		}
		Field[] fields = new Field[fieldList.size()];
		fieldList.toArray(fields);
		return fields;
	}

	@Override
	public Object intercept(Invocation invocation) throws Throwable {
		MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
		String sqlId = mappedStatement.getId();
		log.debug("------sqlId------" + sqlId);
		SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
		Object parameter = invocation.getArgs()[1];
		log.debug("------sqlCommandType------" + sqlCommandType);

		if (parameter == null) {
			return invocation.proceed();
		}
		// 因为使用分页的时候这里不通用所以不再使用
//		if (SqlCommandType.SELECT == sqlCommandType) {
//			SysUser sysUser = this.getLoginUser();
//			Field[] fields = getAllFields(parameter);
//			for (Field field : fields) {
//				log.debug("------field.name------" + field.getName());
//				try {
//					if ("createById".equals(field.getName()) && sysUser.getRoles().get(0).getRoleId() == 2L) {
//						field.setAccessible(true);
//						Object local_createBy = field.get(parameter);
//						field.setAccessible(false);
//						if (local_createBy == null || local_createBy.equals("")) {
//							if (sysUser != null) {
//								BoundSql boundSql = mappedStatement.getBoundSql(parameter);
//								String oldsql = boundSql.getSql();
//								// 改变sql语句
//								if (oldsql.contains("where")) {
//									String[] sqls = oldsql.split("where");
//									for (int i = 0; i < sqls.length; i++) {
//										if (i != sqls.length-1) {
//											oldsql += sqls[i] + " where create_by_id = " + sysUser.getUserId() + " and ";
//										}
//									}
//								}else {
//									oldsql = oldsql + " where create_by_id = " + sysUser.getUserId();
//								}
//								BoundSql newBoundSql = new BoundSql(mappedStatement.getConfiguration(), oldsql,
//										boundSql.getParameterMappings(), boundSql.getParameterObject());
//								MappedStatement newMs = copyFromMappedStatement(mappedStatement, new BoundSqlSqlSource(newBoundSql));
//								invocation.getArgs()[0] = newMs;
//							}
//						}
//					}
//				}catch (Exception e) {}
//			}
//		}
		if (SqlCommandType.INSERT == sqlCommandType) {
			SysUser sysUser = this.getLoginUser();
			Field[] fields = getAllFields(parameter);
			for (Field field : fields) {
				log.debug("------field.name------" + field.getName());
				try {
					if ("createBy".equals(field.getName())) {
						field.setAccessible(true);
						Object local_createBy = field.get(parameter);
						field.setAccessible(false);
						if (local_createBy == null || local_createBy.equals("")) {
							if (sysUser != null) {
								// 登录人账号
								field.setAccessible(true);
								field.set(parameter, sysUser.getNickName());
								field.setAccessible(false);
							}
						}
					}
					if ("createById".equals(field.getName())) {
						field.setAccessible(true);
						Object local_createBy = field.get(parameter);
						field.setAccessible(false);
						if (local_createBy == null || local_createBy.equals("")) {
							if (sysUser != null) {
								// 登录人账号
								field.setAccessible(true);
								field.set(parameter, sysUser.getUserId().toString());
								field.setAccessible(false);
							}
						}
					}
					// 注入创建时间
					if ("createTime".equals(field.getName())) {
						field.setAccessible(true);
						Object local_createDate = field.get(parameter);
						field.setAccessible(false);
						if (local_createDate == null || local_createDate.equals("")) {
							field.setAccessible(true);
							field.set(parameter, new Date());
							field.setAccessible(false);
						}
					}
				} catch (Exception e) {
				}
			}
		}
		if (SqlCommandType.UPDATE == sqlCommandType) {
			SysUser sysUser = this.getLoginUser();
			Field[] fields = null;
			if (parameter instanceof ParamMap) {
				ParamMap<?> p = (ParamMap<?>) parameter;
				if (p.containsKey("et")) {
					parameter = p.get("et");
				} else {
					parameter = p.get("param1");
				}
				if (parameter == null) {
					return invocation.proceed();
				}
				fields = getAllFields(parameter);
			} else {
				fields = getAllFields(parameter);
			}

			for (Field field : fields) {
				log.debug("------field.name------" + field.getName());
				try {
					if ("updateBy".equals(field.getName())) {
						//获取登录用户信息
						if (sysUser != null) {
							// 登录账号
							field.setAccessible(true);
							field.set(parameter, sysUser.getUserName());
							field.setAccessible(false);
						}
					}
					if ("updateTime".equals(field.getName())) {
						field.setAccessible(true);
						String type = field.getType().getName();
						if (type.equals("java.lang.String")) {
							field.set(parameter, DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss"));
						}else {
							field.set(parameter, new Date());
						}
						field.setAccessible(false);
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
		return invocation.proceed();
	}

	@Override
	public Object plugin(Object target) {
		return Plugin.wrap(target, this);
	}

	@Override
	public void setProperties(Properties properties) {
		// TODO Auto-generated method stub
	}

	//update-begin--Author:scott  Date:20191213 for：关于使用Quzrtz 开启线程任务， #465
	private SysUser getLoginUser() {
		SysUser sysUser = null;
		try {
			sysUser = SecurityUtils.getLoginUser().getUser() != null ? SecurityUtils.getLoginUser().getUser() : null;
		} catch (Exception e) {
			//e.printStackTrace();
			sysUser = null;
		}
		return sysUser;
	}
	//update-end--Author:scott  Date:20191213 for：关于使用Quzrtz 开启线程任务， #465


	// 复制原始MappedStatement
	private MappedStatement copyFromMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
		MappedStatement.Builder builder = new MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource,
				ms.getSqlCommandType());
		builder.resource(ms.getResource());
		builder.fetchSize(ms.getFetchSize());
		builder.statementType(ms.getStatementType());
		builder.keyGenerator(ms.getKeyGenerator());
		if (ms.getKeyProperties() != null) {
			for (String keyProperty : ms.getKeyProperties()) {
				builder.keyProperty(keyProperty);
			}
		}
		builder.timeout(ms.getTimeout());
		builder.parameterMap(ms.getParameterMap());
		builder.resultMaps(ms.getResultMaps());
		builder.cache(ms.getCache());
		builder.useCache(ms.isUseCache());
		return builder.build();
	}

	public static class BoundSqlSqlSource implements SqlSource {
		BoundSql boundSql;

		public BoundSqlSqlSource(BoundSql boundSql) {
			this.boundSql = boundSql;
		}

		public BoundSql getBoundSql(Object parameterObject) {
			return boundSql;
		}
	}
}
