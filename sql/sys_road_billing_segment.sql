-- ----------------------------
-- 高速公路计费路段信息表
-- ----------------------------
DROP TABLE IF EXISTS `sys_road_billing_segment`;
CREATE TABLE `sys_road_billing_segment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '计费路段ID',
  `road_marker_id` bigint(20) DEFAULT NULL COMMENT '关联公路编号ID（可选关联）',
  `code` varchar(50) DEFAULT NULL COMMENT '计费路段编码',
  `name` varchar(255) DEFAULT NULL COMMENT '计费路段名称',
  `geom` text COMMENT '几何信息（GeoJSON格式）',
  `length_road` decimal(15,6) DEFAULT NULL COMMENT '计费路段长度',
  `km1` varchar(50) DEFAULT NULL COMMENT '起点桩号',
  `km2` varchar(50) DEFAULT NULL COMMENT '终点桩号',
  `road_min` varchar(255) DEFAULT NULL COMMENT '计费路段所属的小路段',
  `road_max` varchar(255) DEFAULT NULL COMMENT '计费路段所属的大路段',
  `company` varchar(255) DEFAULT NULL COMMENT '管养单位',
  `car_way_num` varchar(20) DEFAULT NULL COMMENT '车道数（格式2｜1，辅车道）',
  `plan_speed` int(5) DEFAULT NULL COMMENT '设计时速',
  `alias` varchar(255) DEFAULT NULL COMMENT '计费路段简称',
  `direction` varchar(20) DEFAULT NULL COMMENT '计费路段方向',
  `mile_post` varchar(50) DEFAULT NULL COMMENT '计费路段桩号',
  `company_alias` varchar(255) DEFAULT NULL COMMENT '管养单位简称',
  `highway_code` varchar(50) DEFAULT NULL COMMENT '高速公路编码',
  `min_pcu` int(10) DEFAULT NULL COMMENT '最小通行能力',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_road_marker_id` (`road_marker_id`),
  KEY `idx_code` (`code`),
  KEY `idx_highway_code` (`highway_code`),
  KEY `idx_direction` (`direction`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='高速公路计费路段信息表';

-- ----------------------------
-- 示例数据插入（基于您提供的数据结构）
-- ----------------------------

-- 首先插入公路编号数据（如果还没有的话）
INSERT INTO `sys_road_marker` (`id`, `code`, `all_name`, `name`, `road_type`) VALUES 
(1, 'G0412', '深圳-南宁高速公路（岑溪-大新公路横县至南宁段）', '南横段', '1'),
(2, 'G0412', '深圳-南宁高速公路（岑溪-大新公路玉林至横州段）', '玉横段', '1')
ON DUPLICATE KEY UPDATE 
`all_name` = VALUES(`all_name`),
`name` = VALUES(`name`),
`road_type` = VALUES(`road_type`);

-- 插入计费路段示例数据
INSERT INTO `sys_road_billing_segment` (
  `road_marker_id`, `code`, `name`, `geom`, `length_road`, `km1`, `km2`, 
  `road_min`, `road_max`, `company`, `car_way_num`, `plan_speed`, 
  `alias`, `direction`, `mile_post`, `company_alias`, `highway_code`, 
  `min_pcu`, `status`, `create_by`, `create_time`
) VALUES 
(
  1, 'S008xxxx', '飞龙-平朗枢纽互通', 
  '{"type":"LineString","coordinates":[[108.922332,22.435356],[108.9219,22.435481],[108.921198,22.435659],[108.920516,22.435803]]}',
  6.894999980926514, 'K52+925', 'K46+289', '南xxx', '南xx', '灵xxx', '2|1', 120,
  NULL, '上行', '4251', '新xxx', 'S81', 4240, '0', 'admin', NOW()
),
(
  1, 'S008xxxx', '飞龙-平朗枢纽互通', 
  '{"type":"LineString","coordinates":[[108.922332,22.435356],[108.9219,22.435481],[108.921198,22.435659],[108.920516,22.435803]]}',
  6.894999980926514, 'K52+925', 'K46+289', '南xxx', '南xx', '灵xxx', '2|1', 120,
  NULL, '下行', '4251', '新xxx', 'S81', 4240, '0', 'admin', NOW()
);

-- 更新关联关系（将计费路段关联到对应的公路编号）
UPDATE `sys_road_billing_segment` rbs 
SET `road_marker_id` = (
    SELECT rm.id 
    FROM `sys_road_marker` rm 
    WHERE rm.code = rbs.highway_code 
    AND rm.name = rbs.road_max
    LIMIT 1
)
WHERE rbs.highway_code IS NOT NULL 
AND rbs.road_max IS NOT NULL 
AND rbs.road_marker_id IS NULL;
