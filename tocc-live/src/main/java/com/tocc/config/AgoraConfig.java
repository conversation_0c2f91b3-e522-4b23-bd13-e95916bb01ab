package com.tocc.config;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class AgoraConfig {

    @Value("${agora.app-id}")
    private String appId;

    @Value("${agora.app-certificate}")
    private String appCertificate;

    @Value("${agora.token-expiration}")
    private int tokenExpiration;

    @Value("${agora.privilege-expiration}")
    private int privilegeExpiration;

}
