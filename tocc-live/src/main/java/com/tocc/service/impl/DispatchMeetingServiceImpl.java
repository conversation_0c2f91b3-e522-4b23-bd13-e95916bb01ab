package com.tocc.service.impl;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.config.AgoraConfig;
import io.agora.media.RtcTokenBuilder2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.mapper.DispatchMeetingMapper;
import com.tocc.domain.DispatchMeeting;
import com.tocc.service.IDispatchMeetingService;

import javax.annotation.Resource;

/**
 * 现场指挥协调会议Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Service
public class DispatchMeetingServiceImpl implements IDispatchMeetingService {

    @Autowired
    private DispatchMeetingMapper dispatchMeetingMapper;

    @Resource
    private AgoraConfig agoraConfig;

    private final RtcTokenBuilder2 tokenBuilder = new RtcTokenBuilder2();

    /**
     * 查询现场指挥协调会议
     *
     * @param id 现场指挥协调会议主键
     * @return 现场指挥协调会议
     */
    @Override
    public DispatchMeeting selectDispatchMeetingById(Long id) {
        return dispatchMeetingMapper.selectDispatchMeetingById(id);
    }

    /**
     * 查询现场指挥协调会议列表
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 现场指挥协调会议
     */
    @Override
    public List<DispatchMeeting> selectDispatchMeetingList(DispatchMeeting dispatchMeeting) {
        return dispatchMeetingMapper.selectDispatchMeetingList(dispatchMeeting);
    }

    /**
     * 新增现场指挥协调会议
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 结果
     */
    @Override
    public String insertDispatchMeeting(DispatchMeeting dispatchMeeting) {
        if (ObjectUtil.isNull(dispatchMeeting) || StrUtil.isEmpty(dispatchMeeting.getChannelName())) {
            throw new ServiceException("频道不能为空");
        }
        dispatchMeeting.setCreateTime(DateUtils.getNowDate());
        int result = dispatchMeetingMapper.insertDispatchMeeting(dispatchMeeting);
        String token = null;
        if (result > 0) {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (ObjectUtil.isNull(loginUser)) {
                throw new ServiceException("用户未登录,无法加入会商");
            }
            Long userId = loginUser.getUser().getUserId();
            String userIdStr = String.valueOf(userId);
            String channelName = dispatchMeeting.getChannelName();
            token = tokenBuilder.buildTokenWithUserAccount(agoraConfig.getAppId(), agoraConfig.getAppCertificate(),
                    channelName, userIdStr, RtcTokenBuilder2.Role.ROLE_PUBLISHER,
                    agoraConfig.getTokenExpiration(),
                    agoraConfig.getPrivilegeExpiration());
        }
        return token;
    }

    /**
     * 修改现场指挥协调会议
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 结果
     */
    @Override
    public int updateDispatchMeeting(DispatchMeeting dispatchMeeting) {
        dispatchMeeting.setUpdateTime(DateUtils.getNowDate());
        return dispatchMeetingMapper.updateDispatchMeeting(dispatchMeeting);
    }

    /**
     * 批量删除现场指挥协调会议
     *
     * @param ids 需要删除的现场指挥协调会议主键
     * @return 结果
     */
    @Override
    public int deleteDispatchMeetingByIds(Long[] ids) {
        return dispatchMeetingMapper.deleteDispatchMeetingByIds(ids);
    }

    /**
     * 删除现场指挥协调会议信息
     *
     * @param id 现场指挥协调会议主键
     * @return 结果
     */
    @Override
    public int deleteDispatchMeetingById(Long id) {
        return dispatchMeetingMapper.deleteDispatchMeetingById(id);
    }

    @Override
    public String generateTokenWithAccount(Long meetingId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (ObjectUtil.isNull(loginUser)) {
            throw new ServiceException("用户未登录,无法加入会商");
        }
        if (ObjectUtil.isNull(meetingId)) {
            throw new ServiceException("会议id不能为空");
        }
        DispatchMeeting dispatchMeeting = dispatchMeetingMapper.selectDispatchMeetingById(meetingId);
        if (ObjectUtil.isNull(dispatchMeeting)) {
            throw new ServiceException("会议不存在");
        }
        Long userId = loginUser.getUser().getUserId();
        String userIdStr = String.valueOf(userId);
        String token = tokenBuilder.buildTokenWithUserAccount(agoraConfig.getAppId(), agoraConfig.getAppCertificate(),
                dispatchMeeting.getChannelName(), userIdStr, RtcTokenBuilder2.Role.ROLE_PUBLISHER,
                agoraConfig.getTokenExpiration(),
                agoraConfig.getPrivilegeExpiration());
        return token;
    }
}
