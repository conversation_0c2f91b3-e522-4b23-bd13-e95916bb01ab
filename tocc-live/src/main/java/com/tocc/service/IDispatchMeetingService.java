package com.tocc.service;

import java.util.List;

import com.tocc.domain.DispatchMeeting;

/**
 * 现场指挥协调会议Service接口
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface IDispatchMeetingService {
    /**
     * 查询现场指挥协调会议
     *
     * @param id 现场指挥协调会议主键
     * @return 现场指挥协调会议
     */
    public DispatchMeeting selectDispatchMeetingById(Long id);

    /**
     * 查询现场指挥协调会议列表
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 现场指挥协调会议集合
     */
    public List<DispatchMeeting> selectDispatchMeetingList(DispatchMeeting dispatchMeeting);

    /**
     * 新增现场指挥协调会议
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 结果
     */
    public String insertDispatchMeeting(DispatchMeeting dispatchMeeting);

    /**
     * 修改现场指挥协调会议
     *
     * @param dispatchMeeting 现场指挥协调会议
     * @return 结果
     */
    public int updateDispatchMeeting(DispatchMeeting dispatchMeeting);

    /**
     * 批量删除现场指挥协调会议
     *
     * @param ids 需要删除的现场指挥协调会议主键集合
     * @return 结果
     */
    public int deleteDispatchMeetingByIds(Long[] ids);

    /**
     * 删除现场指挥协调会议信息
     *
     * @param id 现场指挥协调会议主键
     * @return 结果
     */
    public int deleteDispatchMeetingById(Long id);

    /**
     * 生成声网token
     *
     * @param meetingId
     * @return
     */
    public String generateTokenWithAccount(Long meetingId);
}
