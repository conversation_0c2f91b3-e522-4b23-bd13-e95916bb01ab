<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.DispatchMeetingMapper">

    <resultMap type="com.tocc.domain.DispatchMeeting" id="DispatchMeetingResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="host" column="host"/>
        <result property="code" column="code"/>
        <result property="channelName" column="channel_name"/>
        <result property="inviteUrl" column="invite_url"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDispatchMeetingVo">
        select id,
               title,
               host,
               code,
               channel_name,
               invite_url,
               status,
               description,
               create_by,
               create_time,
               update_by,
               update_time
        from dispatch_meeting
    </sql>

    <select id="selectDispatchMeetingList" parameterType="com.tocc.domain.DispatchMeeting"
            resultMap="DispatchMeetingResult">
        <include refid="selectDispatchMeetingVo"/>
        <where>
            <if test="title != null  and title != ''">and title = #{title}</if>
            <if test="host != null  and host != ''">and host = #{host}</if>
            <if test="code != null  and code != ''">and code = #{code}</if>
            <if test="channelName != null  and channelName != ''">and channel_name like concat('%', #{channelName},
                '%')
            </if>
            <if test="inviteUrl != null  and inviteUrl != ''">and invite_url = #{inviteUrl}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
        </where>
    </select>

    <select id="selectDispatchMeetingById" parameterType="Long" resultMap="DispatchMeetingResult">
        <include refid="selectDispatchMeetingVo"/>
        where id = #{id}
    </select>

    <insert id="insertDispatchMeeting" parameterType="com.tocc.domain.DispatchMeeting">
        insert into dispatch_meeting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="host != null">host,</if>
            <if test="code != null">code,</if>
            <if test="channelName != null">channel_name,</if>
            <if test="inviteUrl != null">invite_url,</if>
            <if test="status != null">status,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="host != null">#{host},</if>
            <if test="code != null">#{code},</if>
            <if test="channelName != null">#{channelName},</if>
            <if test="inviteUrl != null">#{inviteUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDispatchMeeting" parameterType="com.tocc.domain.DispatchMeeting">
        update dispatch_meeting
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="host != null">host = #{host},</if>
            <if test="code != null">code = #{code},</if>
            <if test="channelName != null">channel_name = #{channelName},</if>
            <if test="inviteUrl != null">invite_url = #{inviteUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDispatchMeetingById" parameterType="Long">
        delete
        from dispatch_meeting
        where id = #{id}
    </delete>

    <delete id="deleteDispatchMeetingByIds" parameterType="String">
        delete from dispatch_meeting where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>