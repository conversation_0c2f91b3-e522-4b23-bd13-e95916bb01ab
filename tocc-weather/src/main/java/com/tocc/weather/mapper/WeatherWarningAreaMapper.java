package com.tocc.weather.mapper;

import java.util.List;
import com.tocc.weather.domain.entity.WeatherWarningArea;

/**
 * 气象预警影响区域Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface WeatherWarningAreaMapper 
{
    /**
     * 查询气象预警影响区域
     * 
     * @param warningId 预警ID
     * @param regionId 区域ID
     * @return 气象预警影响区域
     */
    public WeatherWarningArea selectWeatherWarningAreaByIds(String warningId, String regionId);

    /**
     * 查询气象预警影响区域列表
     * 
     * @param warningId 预警ID
     * @return 气象预警影响区域集合
     */
    public List<WeatherWarningArea> selectWeatherWarningAreaList(String warningId);

    /**
     * 新增气象预警影响区域
     * 
     * @param weatherWarningArea 气象预警影响区域
     * @return 结果
     */
    public int insertWeatherWarningArea(WeatherWarningArea weatherWarningArea);

    /**
     * 批量新增气象预警影响区域
     * 
     * @param weatherWarningAreaList 气象预警影响区域列表
     * @return 结果
     */
    public int batchInsertWeatherWarningArea(List<WeatherWarningArea> weatherWarningAreaList);

    /**
     * 修改气象预警影响区域
     * 
     * @param weatherWarningArea 气象预警影响区域
     * @return 结果
     */
    public int updateWeatherWarningArea(WeatherWarningArea weatherWarningArea);

    /**
     * 删除气象预警影响区域
     * 
     * @param warningId 预警ID
     * @param regionId 区域ID
     * @return 结果
     */
    public int deleteWeatherWarningAreaByIds(String warningId, String regionId);

    /**
     * 删除预警的所有影响区域
     * 
     * @param warningId 预警ID
     * @return 结果
     */
    public int deleteWeatherWarningAreaByWarningId(String warningId);

    /**
     * 批量删除气象预警影响区域
     * 
     * @param warningIds 需要删除的预警ID集合
     * @return 结果
     */
    public int deleteWeatherWarningAreaByWarningIds(String[] warningIds);

    /**
     * 查询指定区域的预警列表
     * 
     * @param regionId 区域ID
     * @return 预警ID列表
     */
    public List<String> selectWarningIdsByRegionId(String regionId);

    /**
     * 查询预警影响区域的简要描述
     * 
     * @param warningId 预警ID
     * @return 区域描述字符串
     */
    public String selectAreaDescByWarningId(String warningId);
}
