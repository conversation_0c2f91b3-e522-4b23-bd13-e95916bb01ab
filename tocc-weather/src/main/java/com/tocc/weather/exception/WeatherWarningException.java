package com.tocc.weather.exception;

/**
 * 气象预警异常类
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningException extends RuntimeException 
{
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误详细信息
     */
    private Object[] args;

    public WeatherWarningException(String message) 
    {
        super(message);
    }

    public WeatherWarningException(String message, Throwable cause) 
    {
        super(message, cause);
    }

    public WeatherWarningException(String errorCode, String message) 
    {
        super(message);
        this.errorCode = errorCode;
    }

    public WeatherWarningException(String errorCode, String message, Object... args) 
    {
        super(message);
        this.errorCode = errorCode;
        this.args = args;
    }

    public WeatherWarningException(String errorCode, String message, Throwable cause) 
    {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public WeatherWarningException(String errorCode, String message, Throwable cause, Object... args) 
    {
        super(message, cause);
        this.errorCode = errorCode;
        this.args = args;
    }

    public String getErrorCode() 
    {
        return errorCode;
    }

    public void setErrorCode(String errorCode) 
    {
        this.errorCode = errorCode;
    }

    public Object[] getArgs() 
    {
        return args;
    }

    public void setArgs(Object[] args) 
    {
        this.args = args;
    }

    /**
     * 预警不存在异常
     */
    public static class WarningNotFoundException extends WeatherWarningException 
    {
        public WarningNotFoundException(String warningId) 
        {
            super("WARNING_NOT_FOUND", "预警不存在：" + warningId, warningId);
        }
    }

    /**
     * 预警已过期异常
     */
    public static class WarningExpiredException extends WeatherWarningException 
    {
        public WarningExpiredException(String warningId) 
        {
            super("WARNING_EXPIRED", "预警已过期：" + warningId, warningId);
        }
    }

    /**
     * 预警状态无效异常
     */
    public static class InvalidWarningStatusException extends WeatherWarningException 
    {
        public InvalidWarningStatusException(String status) 
        {
            super("INVALID_WARNING_STATUS", "无效的预警状态：" + status, status);
        }
    }

    /**
     * 通知已确认异常
     */
    public static class NotificationAlreadyConfirmedException extends WeatherWarningException 
    {
        public NotificationAlreadyConfirmedException(String warningId, Long userId) 
        {
            super("NOTIFICATION_ALREADY_CONFIRMED", "通知已确认：" + warningId + " - " + userId, warningId, userId);
        }
    }

    /**
     * 通知超时异常
     */
    public static class NotificationTimeoutException extends WeatherWarningException 
    {
        public NotificationTimeoutException(String warningId, Long userId) 
        {
            super("NOTIFICATION_TIMEOUT", "通知已超时：" + warningId + " - " + userId, warningId, userId);
        }
    }

    /**
     * 通知发送失败异常
     */
    public static class NotificationSendFailedException extends WeatherWarningException 
    {
        public NotificationSendFailedException(String message, Throwable cause) 
        {
            super("NOTIFICATION_SEND_FAILED", "通知发送失败：" + message, cause);
        }
    }

    /**
     * 告警创建失败异常
     */
    public static class AlarmCreateFailedException extends WeatherWarningException 
    {
        public AlarmCreateFailedException(String message, Throwable cause) 
        {
            super("ALARM_CREATE_FAILED", "告警创建失败：" + message, cause);
        }
    }

    /**
     * 参数验证异常
     */
    public static class ParameterValidationException extends WeatherWarningException 
    {
        public ParameterValidationException(String paramName, String message) 
        {
            super("PARAMETER_VALIDATION_ERROR", "参数验证失败 [" + paramName + "]：" + message, paramName, message);
        }
    }

    /**
     * 业务规则异常
     */
    public static class BusinessRuleException extends WeatherWarningException 
    {
        public BusinessRuleException(String rule, String message) 
        {
            super("BUSINESS_RULE_VIOLATION", "业务规则违反 [" + rule + "]：" + message, rule, message);
        }
    }
}
