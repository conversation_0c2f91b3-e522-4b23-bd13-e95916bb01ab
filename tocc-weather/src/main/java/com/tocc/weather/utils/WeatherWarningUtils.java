package com.tocc.weather.utils;

import java.util.Date;
import java.util.concurrent.TimeUnit;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.StringUtils;

/**
 * 气象预警工具类
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningUtils 
{
    /**
     * 根据预警等级获取超时时间（分钟）
     * 
     * @param warningLevel 预警等级
     * @return 超时时间（分钟）
     */
    public static int getTimeoutMinutesByLevel(String warningLevel) 
    {
        if (StringUtils.isEmpty(warningLevel)) {
            return 30; // 默认30分钟
        }
        
        switch (warningLevel) {
            case "8": // 红色预警
                return 15;
            case "7": // 橙色预警
                return 30;
            case "6": // 黄色预警
                return 60;
            case "5": // 蓝色预警
                return 120;
            default:
                return 30; // 默认30分钟
        }
    }

    /**
     * 根据预警等级获取等级名称
     * 
     * @param warningLevel 预警等级
     * @return 等级名称
     */
    public static String getWarningLevelName(String warningLevel) 
    {
        if (StringUtils.isEmpty(warningLevel)) {
            return "未知等级";
        }
        
        switch (warningLevel) {
            case "8": return "红色预警";
            case "7": return "橙色预警";
            case "6": return "黄色预警";
            case "5": return "蓝色预警";
            default: return "未知等级";
        }
    }

    /**
     * 根据预警类型获取类型名称
     * 
     * @param warningType 预警类型
     * @return 类型名称
     */
    public static String getWarningTypeName(String warningType) 
    {
        if (StringUtils.isEmpty(warningType)) {
            return "未知类型";
        }
        
        switch (warningType) {
            case "1": return "暴雨预警";
            case "2": return "台风预警";
            case "3": return "雷电预警";
            case "4": return "大风预警";
            case "5": return "冰雹预警";
            case "6": return "高温预警";
            case "7": return "寒潮预警";
            case "8": return "大雾预警";
            case "9": return "道路结冰预警";
            case "10": return "霜冻预警";
            default: return "未知类型";
        }
    }

    /**
     * 根据状态获取状态名称
     * 
     * @param status 状态
     * @return 状态名称
     */
    public static String getStatusName(String status) 
    {
        if (StringUtils.isEmpty(status)) {
            return "未知状态";
        }
        
        switch (status) {
            case "0": return "有效";
            case "1": return "失效";
            case "2": return "取消";
            default: return "未知状态";
        }
    }

    /**
     * 计算剩余超时时间（分钟）
     * 
     * @param notificationTime 通知时间
     * @param timeoutMinutes 超时时间（分钟）
     * @return 剩余时间（分钟），如果已超时返回0
     */
    public static int calculateRemainingMinutes(Date notificationTime, int timeoutMinutes) 
    {
        if (notificationTime == null) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        long notifyTime = notificationTime.getTime();
        long timeoutTime = notifyTime + TimeUnit.MINUTES.toMillis(timeoutMinutes);
        
        if (currentTime >= timeoutTime) {
            return 0; // 已超时
        }
        
        long remainingMillis = timeoutTime - currentTime;
        return (int) TimeUnit.MILLISECONDS.toMinutes(remainingMillis);
    }

    /**
     * 判断通知是否已超时
     * 
     * @param notificationTime 通知时间
     * @param timeoutMinutes 超时时间（分钟）
     * @return 是否超时
     */
    public static boolean isTimeout(Date notificationTime, int timeoutMinutes) 
    {
        return calculateRemainingMinutes(notificationTime, timeoutMinutes) <= 0;
    }

    /**
     * 判断预警是否已过期
     * 
     * @param expireTime 失效时间
     * @return 是否过期
     */
    public static boolean isExpired(Date expireTime) 
    {
        if (expireTime == null) {
            return false; // 没有设置失效时间，认为不过期
        }
        
        return expireTime.before(new Date());
    }

    /**
     * 判断预警是否即将过期
     * 
     * @param expireTime 失效时间
     * @param beforeMinutes 提前多少分钟
     * @return 是否即将过期
     */
    public static boolean isExpiringSoon(Date expireTime, int beforeMinutes) 
    {
        if (expireTime == null) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        long expireTimeMillis = expireTime.getTime();
        long beforeTimeMillis = currentTime + TimeUnit.MINUTES.toMillis(beforeMinutes);
        
        return expireTimeMillis <= beforeTimeMillis && expireTimeMillis > currentTime;
    }

    /**
     * 格式化预警标题
     * 
     * @param warningType 预警类型
     * @param warningLevel 预警等级
     * @param areaName 区域名称
     * @return 格式化的标题
     */
    public static String formatWarningTitle(String warningType, String warningLevel, String areaName) 
    {
        StringBuilder title = new StringBuilder();
        
        if (StringUtils.isNotEmpty(areaName)) {
            title.append(areaName);
        }
        
        title.append(getWarningTypeName(warningType));
        title.append(getWarningLevelName(warningLevel));
        
        return title.toString();
    }

    /**
     * 构建短信通知内容
     * 
     * @param warningType 预警类型
     * @param warningLevel 预警等级
     * @param issueTime 发布时间
     * @return 短信内容
     */
    public static String buildSmsContent(String warningType, String warningLevel, Date issueTime)
    {
        return String.format(
            "【气象预警】%s%s，发布时间：%s。请及时确认并采取相应措施。",
            getWarningTypeName(warningType),
            getWarningLevelName(warningLevel),
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, issueTime)
        );
    }

    /**
     * 构建邮件主题
     * 
     * @param warningType 预警类型
     * @param warningLevel 预警等级
     * @return 邮件主题
     */
    public static String buildEmailSubject(String warningType, String warningLevel) 
    {
        return String.format("气象预警通知 - %s%s", 
            getWarningTypeName(warningType), 
            getWarningLevelName(warningLevel));
    }

    /**
     * 验证预警等级是否有效
     * 
     * @param warningLevel 预警等级
     * @return 是否有效
     */
    public static boolean isValidWarningLevel(String warningLevel) 
    {
        return StringUtils.isNotEmpty(warningLevel) && 
               ("5".equals(warningLevel) || "6".equals(warningLevel) || 
                "7".equals(warningLevel) || "8".equals(warningLevel));
    }

    /**
     * 验证预警类型是否有效
     * 
     * @param warningType 预警类型
     * @return 是否有效
     */
    public static boolean isValidWarningType(String warningType) 
    {
        if (StringUtils.isEmpty(warningType)) {
            return false;
        }
        
        try {
            int type = Integer.parseInt(warningType);
            return type >= 1 && type <= 10;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证预警状态是否有效
     * 
     * @param status 状态
     * @return 是否有效
     */
    public static boolean isValidStatus(String status) 
    {
        return StringUtils.isNotEmpty(status) && 
               ("0".equals(status) || "1".equals(status) || "2".equals(status));
    }

    /**
     * 比较预警等级优先级
     * 
     * @param level1 等级1
     * @param level2 等级2
     * @return 比较结果，正数表示level1优先级高于level2
     */
    public static int compareWarningLevel(String level1, String level2) 
    {
        int priority1 = getWarningLevelPriority(level1);
        int priority2 = getWarningLevelPriority(level2);
        return priority1 - priority2;
    }

    /**
     * 获取预警等级优先级
     * 
     * @param warningLevel 预警等级
     * @return 优先级（数字越大优先级越高）
     */
    private static int getWarningLevelPriority(String warningLevel) 
    {
        switch (warningLevel) {
            case "8": return 4; // 红色预警
            case "7": return 3; // 橙色预警
            case "6": return 2; // 黄色预警
            case "5": return 1; // 蓝色预警
            default: return 0;
        }
    }
}
