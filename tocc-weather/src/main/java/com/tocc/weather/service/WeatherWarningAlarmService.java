package com.tocc.weather.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.AlarmInfoDTO;
import com.tocc.service.IAlarmService;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.entity.WeatherWarningArea;
import com.tocc.weather.domain.dto.WeatherWarningCreateDTO;
import com.tocc.weather.domain.dto.WeatherWarningNotifyTargetDTO;
import com.tocc.weather.mapper.WeatherWarningAreaMapper;
import com.tocc.weather.constants.WeatherWarningConstants;

/**
 * 气象预警告警服务
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class WeatherWarningAlarmService
{
    private static final Logger log = LoggerFactory.getLogger(WeatherWarningAlarmService.class);

    @Autowired
    private IAlarmService alarmService;

    @Autowired
    private WeatherWarningAreaMapper weatherWarningAreaMapper;

    /**
     * 创建预警发布告警
     *
     * @param createDTO 预警创建DTO
     * @param warning 预警信息
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void createWarningAlarm(WeatherWarningCreateDTO createDTO, WeatherWarning warning)
    {
        try {
            AlarmInfoDTO alarmInfo = new AlarmInfoDTO();

            // 设置告警ID（必填）
            alarmInfo.setAlarmId(IdUtils.fastUUID());

            // 设置告警标题：xx行政辖区发生xx预警
            String alarmTitle = buildAlarmTitle(createDTO, warning);
            alarmInfo.setAlarmTitle(alarmTitle);

            // 设置告警类型和级别
            alarmInfo.setAlarmType(WeatherWarningConstants.ALARM_TYPE); // 气象告警
            alarmInfo.setAlarmSubtype(WeatherWarningConstants.ALARM_SUBTYPE_WARNING); // 预警发布告警子类型
            alarmInfo.setAlarmLevel(mapWarningLevelToAlarmLevel(warning.getWarningLevel()));

            // 设置详细的告警内容
            String alarmContent = buildAlarmContent(createDTO, warning);
            alarmInfo.setAlarmContent(alarmContent);

            // 设置关联信息
            alarmInfo.setSourceId(warning.getWarningId());
            alarmInfo.setSourceType("weather_warning");

            // 设置组织信息（广西壮族自治区交通运输厅）
            alarmInfo.setOrgId(WeatherWarningConstants.GUANGXI_TRANSPORT_DEPT_ID);
            alarmInfo.setOrgName("广西壮族自治区交通运输厅");

            // 设置行政区划ID（取第一个影响区域）
            if (createDTO.getAffectedAreas() != null && !createDTO.getAffectedAreas().isEmpty()) {
                String firstRegionId = createDTO.getAffectedAreas().get(0).getRegionId();
                alarmInfo.setAdministrativeAreaId(firstRegionId);
            } else {
                // 如果没有区域信息，使用默认值
                alarmInfo.setAdministrativeAreaId("450000"); // 广西壮族自治区
            }

            // 设置告警状态和时间
            alarmInfo.setStatus("0"); // 未处理
            alarmInfo.setAlarmTime(warning.getIssueTime()); // 使用预警发布时间作为告警时间

            // 设置创建信息
            alarmInfo.setCreateBy(SecurityUtils.getUsername());
            alarmInfo.setCreateTime(new Date());

            // 调用告警服务创建告警
            log.info("准备创建气象预警告警，告警ID：{}，预警ID：{}", alarmInfo.getAlarmId(), warning.getWarningId());
            log.info("告警信息：标题={}, 类型={}, 子类型={}, 等级={}, 组织ID={}, 区域ID={}",
                alarmInfo.getAlarmTitle(), alarmInfo.getAlarmType(), alarmInfo.getAlarmSubtype(),
                alarmInfo.getAlarmLevel(), alarmInfo.getOrgId(), alarmInfo.getAdministrativeAreaId());

            alarmService.insertAlarmInfo(alarmInfo);

            log.info("创建气象预警告警成功：告警ID={}，预警ID={}", alarmInfo.getAlarmId(), warning.getWarningId());

        } catch (Exception e) {
            // 告警创建失败不影响主业务流程，只记录日志
            log.error("创建气象预警告警失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建通知确认告警（按单位分组）
     *
     * @param warning 预警信息
     * @param notifyTargets 通知对象列表
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void createNotificationAlarms(WeatherWarning warning, List<WeatherWarningNotifyTargetDTO> notifyTargets)
    {
        try {
            // 按单位分组
            Map<String, List<WeatherWarningNotifyTargetDTO>> unitGroups = notifyTargets.stream()
                .collect(Collectors.groupingBy(WeatherWarningNotifyTargetDTO::getContactUnitId));

            for (Map.Entry<String, List<WeatherWarningNotifyTargetDTO>> entry : unitGroups.entrySet()) {
                String unitId = entry.getKey();
                List<WeatherWarningNotifyTargetDTO> targets = entry.getValue();

                AlarmInfoDTO alarmInfo = new AlarmInfoDTO();

                // 设置告警ID
                alarmInfo.setAlarmId(IdUtils.fastUUID());

                // 设置告警标题
                String warningTypeName = getWarningTypeLabel(warning.getWarningType());
                String warningLevelName = getWarningLevelLabel(warning.getWarningLevel());
                alarmInfo.setAlarmTitle(warningTypeName + warningLevelName + "通知确认");

                // 设置告警类型和级别
                alarmInfo.setAlarmType(WeatherWarningConstants.ALARM_TYPE);
                alarmInfo.setAlarmSubtype(WeatherWarningConstants.ALARM_SUBTYPE_NOTIFICATION); // 通知确认告警子类型
                alarmInfo.setAlarmLevel(mapWarningLevelToAlarmLevel(warning.getWarningLevel()));

                // 设置告警内容
                String alarmContent = buildNotificationAlarmContent(warning, targets);
                alarmInfo.setAlarmContent(alarmContent);

                // 设置关联信息
                alarmInfo.setSourceId(warning.getWarningId());
                alarmInfo.setSourceType("weather_warning");

                // 设置组织信息（通知对象所在单位）
                alarmInfo.setOrgId(unitId);
                alarmInfo.setOrgName(targets.get(0).getContactUnitName());

                // 设置行政区划ID（使用第一个通知对象的单位区域）
                alarmInfo.setAdministrativeAreaId(unitId);

                // 设置告警状态和时间
                alarmInfo.setStatus("0"); // 未处理
                alarmInfo.setAlarmTime(new Date()); // 使用当前时间作为告警时间

                // 设置创建信息
                alarmInfo.setCreateBy(SecurityUtils.getUsername());
                alarmInfo.setCreateTime(new Date());

                // 调用告警服务创建告警
                log.info("准备创建通知确认告警，单位：{}，人员数量：{}", targets.get(0).getContactUnitName(), targets.size());

                alarmService.insertAlarmInfo(alarmInfo);

                log.info("创建通知确认告警成功：单位={}，告警ID={}", targets.get(0).getContactUnitName(), alarmInfo.getAlarmId());
            }

        } catch (Exception e) {
            // 告警创建失败不影响主业务流程，只记录日志
            log.error("创建通知确认告警失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建告警标题
     */
    private String buildAlarmTitle(WeatherWarningCreateDTO createDTO, WeatherWarning warning)
    {
        String warningTypeName = getWarningTypeLabel(warning.getWarningType());
        String warningLevelName = getWarningLevelLabel(warning.getWarningLevel());

        return String.format("发布%s%s", warningTypeName, warningLevelName);
    }

    /**
     * 构建告警内容
     */
    private String buildAlarmContent(WeatherWarningCreateDTO createDTO, WeatherWarning warning)
    {
        StringBuilder content = new StringBuilder();

        // 发布时间（年月日时分格式）
        String issueTimeStr = DateUtils.parseDateToStr("yyyy年MM月dd日HH时mm分", warning.getIssueTime());
        content.append(issueTimeStr).append("，");

        // 影响区域列表
        if (createDTO.getAffectedAreas() != null && !createDTO.getAffectedAreas().isEmpty()) {
            for (int i = 0; i < createDTO.getAffectedAreas().size(); i++) {
                if (i > 0) content.append("、");
                content.append(createDTO.getAffectedAreas().get(i).getRegionName());
            }
            if (createDTO.getAffectedAreas().size() > 1) {
                content.append("等").append(createDTO.getAffectedAreas().size()).append("个区域");
            }
        } else {
            content.append("相关区域");
        }

        // 预警信息
        String warningTypeName = getWarningTypeLabel(warning.getWarningType());
        String warningLevelName = getWarningLevelLabel(warning.getWarningLevel());
        content.append("发布").append(warningTypeName).append(warningLevelName);

        // 失效时间
        if (warning.getExpireTime() != null) {
            String expireTimeStr = DateUtils.parseDateToStr("yyyy年MM月dd日HH时mm分", warning.getExpireTime());
            content.append("，失效时间：").append(expireTimeStr);
        }

        // 预警内容
        content.append("\n\n预警内容：\n").append(warning.getWarningContent());

        // 防御指南
        if (warning.getPreventionGuide() != null && !warning.getPreventionGuide().trim().isEmpty()) {
            content.append("\n\n防御指南：\n").append(warning.getPreventionGuide());
        }

        return content.toString();
    }

    /**
     * 构建通知确认告警内容
     */
    private String buildNotificationAlarmContent(WeatherWarning warning, List<WeatherWarningNotifyTargetDTO> targets)
    {
        StringBuilder content = new StringBuilder();

        // 发布时间（年月日时分格式）
        String issueTimeStr = DateUtils.parseDateToStr("yyyy年MM月dd日HH时mm分", warning.getIssueTime());
        content.append(issueTimeStr).append("，");

        // 获取影响区域信息
        try {
            List<WeatherWarningArea> areas = weatherWarningAreaMapper.selectWeatherWarningAreaList(warning.getWarningId());
            if (areas != null && !areas.isEmpty()) {
                for (int i = 0; i < areas.size(); i++) {
                    if (i > 0) content.append("、");
                    content.append(areas.get(i).getRegionName());
                }
                if (areas.size() > 1) {
                    content.append("等").append(areas.size()).append("个区域");
                }
            } else {
                content.append("相关区域");
            }
        } catch (Exception e) {
            log.error("查询影响区域失败", e);
            content.append("相关区域");
        }

        // 预警信息
        String warningTypeName = getWarningTypeLabel(warning.getWarningType());
        String warningLevelName = getWarningLevelLabel(warning.getWarningLevel());
        content.append("发布").append(warningTypeName).append(warningLevelName);

        // 失效时间
        if (warning.getExpireTime() != null) {
            String expireTimeStr = DateUtils.parseDateToStr("yyyy年MM月dd日HH时mm分", warning.getExpireTime());
            content.append("，失效时间：").append(expireTimeStr);
        }

        content.append("。请及时确认！");

        return content.toString();
    }

    /**
     * 将预警等级映射为告警等级
     */
    private String mapWarningLevelToAlarmLevel(String warningLevel)
    {
        // 预警等级映射到告警等级
        switch (warningLevel) {
            case "8": return "8"; // 红色预警 -> 红色告警
            case "7": return "7"; // 橙色预警 -> 橙色告警
            case "6": return "6"; // 黄色预警 -> 黄色告警
            case "5": return "5"; // 蓝色预警 -> 蓝色告警
            default: return "6"; // 默认黄色告警
        }
    }

    /**
     * 获取预警类型标签
     */
    private String getWarningTypeLabel(String warningType)
    {
        switch (warningType) {
            case "1": return "暴雨预警";
            case "2": return "台风预警";
            case "3": return "雷电预警";
            case "4": return "大风预警";
            case "5": return "冰雹预警";
            case "6": return "高温预警";
            case "7": return "寒潮预警";
            case "8": return "大雾预警";
            case "9": return "道路结冰预警";
            case "10": return "霜冻预警";
            default: return "未知预警";
        }
    }

    /**
     * 获取预警等级标签
     */
    private String getWarningLevelLabel(String warningLevel)
    {
        switch (warningLevel) {
            case "5": return "蓝色预警";
            case "6": return "黄色预警";
            case "7": return "橙色预警";
            case "8": return "红色预警";
            default: return "未知等级";
        }
    }

    /**
     * 根据预警ID查询预警发布告警
     *
     * @param warningId 预警ID
     * @return 告警ID
     */
    public String getWarningPublishAlarmId(String warningId)
    {
        try {
            // 这里需要调用告警服务查询接口
            // 查询条件：source_id = warningId AND subtype = 'warning_publish'
            // 暂时返回null，需要实现具体的查询逻辑
            log.info("查询预警发布告警：预警ID={}", warningId);
            return null;
        } catch (Exception e) {
            log.error("查询预警发布告警失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据预警ID查询通知确认告警列表
     *
     * @param warningId 预警ID
     * @return 告警ID列表
     */
    public List<String> getNotificationConfirmAlarmIds(String warningId)
    {
        try {
            // 这里需要调用告警服务查询接口
            // 查询条件：source_id = warningId AND subtype = 'notification_confirm'
            // 暂时返回空列表，需要实现具体的查询逻辑
            log.info("查询通知确认告警：预警ID={}", warningId);
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("查询通知确认告警失败: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
