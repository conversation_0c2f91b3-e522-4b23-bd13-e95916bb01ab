package com.tocc.weather.service.impl;

import java.util.List;
import java.util.UUID;
import java.util.Date;
import java.util.ArrayList;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.exception.ServiceException;
import com.tocc.system.service.ISysDictDataService;
import com.tocc.system.service.ISysUserService;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.service.IOrganizationService;
import com.tocc.weather.mapper.WeatherWarningMapper;
import com.tocc.weather.mapper.WeatherWarningAreaMapper;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.entity.WeatherWarningArea;
import com.tocc.weather.domain.dto.WeatherWarningDTO;
import com.tocc.weather.domain.dto.WeatherWarningCreateDTO;
import com.tocc.weather.domain.dto.WeatherWarningAreaDTO;
import com.tocc.weather.domain.vo.WeatherWarningVO;
import com.tocc.weather.domain.vo.WeatherWarningAreaVO;
import com.tocc.weather.domain.vo.WeatherWarningProgressVO;
import com.tocc.weather.service.IWeatherWarningService;
import com.tocc.weather.service.IWeatherWarningNotificationService;
import com.tocc.weather.service.WeatherWarningAlarmService;
import com.tocc.weather.domain.dto.WeatherWarningNotifyRequestDTO;
import com.tocc.weather.domain.dto.WeatherWarningNotifyTargetDTO;
import com.tocc.weather.constants.WeatherWarningConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 气象预警信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class WeatherWarningServiceImpl implements IWeatherWarningService
{
    private static final Logger log = LoggerFactory.getLogger(WeatherWarningServiceImpl.class);

    @Autowired
    private WeatherWarningMapper weatherWarningMapper;

    @Autowired
    private WeatherWarningAreaMapper weatherWarningAreaMapper;

    @Autowired
    private IWeatherWarningNotificationService notificationService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private WeatherWarningAlarmService weatherWarningAlarmService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IOrganizationService organizationService;

    /**
     * 查询气象预警信息
     * 
     * @param warningId 气象预警信息主键
     * @return 气象预警信息
     */
    @Override
    public WeatherWarning selectWeatherWarningByWarningId(String warningId)
    {
        return weatherWarningMapper.selectWeatherWarningByWarningId(warningId);
    }

    /**
     * 查询气象预警信息列表
     * 
     * @param weatherWarningDTO 气象预警信息查询条件
     * @return 气象预警信息
     */
    @Override
    public List<WeatherWarning> selectWeatherWarningList(WeatherWarningDTO weatherWarningDTO)
    {
        List<WeatherWarning> warnings = weatherWarningMapper.selectWeatherWarningList(weatherWarningDTO);
        
        // 为每个预警添加影响区域描述
        for (WeatherWarning warning : warnings) {
            String areaDesc = weatherWarningAreaMapper.selectAreaDescByWarningId(warning.getWarningId());
            warning.setAffectedAreasDesc(areaDesc);
        }
        
        return warnings;
    }

    /**
     * 新增气象预警信息
     * 
     * @param weatherWarning 气象预警信息
     * @return 结果
     */
    @Override
    public int insertWeatherWarning(WeatherWarning weatherWarning)
    {
        weatherWarning.setCreateTime(DateUtils.getNowDate());
        return weatherWarningMapper.insertWeatherWarning(weatherWarning);
    }

    /**
     * 修改气象预警信息
     * 
     * @param weatherWarning 气象预警信息
     * @return 结果
     */
    @Override
    public int updateWeatherWarning(WeatherWarning weatherWarning)
    {
        weatherWarning.setUpdateTime(DateUtils.getNowDate());
        return weatherWarningMapper.updateWeatherWarning(weatherWarning);
    }

    /**
     * 批量删除气象预警信息
     * 
     * @param warningIds 需要删除的气象预警信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWeatherWarningByWarningIds(String[] warningIds)
    {
        // 删除关联的影响区域
        weatherWarningAreaMapper.deleteWeatherWarningAreaByWarningIds(warningIds);
        
        // 删除关联的通知记录
        notificationService.deleteWeatherWarningNotificationByWarningIds(warningIds);
        
        return weatherWarningMapper.deleteWeatherWarningByWarningIds(warningIds);
    }

    /**
     * 删除气象预警信息信息
     * 
     * @param warningId 气象预警信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWeatherWarningByWarningId(String warningId)
    {
        // 删除关联的影响区域
        weatherWarningAreaMapper.deleteWeatherWarningAreaByWarningId(warningId);
        
        // 删除关联的通知记录
        notificationService.deleteWeatherWarningNotificationByWarningId(warningId);
        
        return weatherWarningMapper.deleteWeatherWarningByWarningId(warningId);
    }

    /**
     * 创建预警并发送通知
     * 
     * @param createDTO 创建预警DTO
     * @return 预警ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createWarning(WeatherWarningCreateDTO createDTO)
    {
        // 1. 创建预警主记录
        WeatherWarning warning = new WeatherWarning();
        BeanUtils.copyProperties(createDTO, warning);
        warning.setWarningId(UUID.randomUUID().toString());
        warning.setStatus(WeatherWarningConstants.STATUS_ACTIVE); // 有效状态
        warning.setIsNotified(WeatherWarningConstants.NOT_NOTIFIED); // 未通知
        warning.setCreateBy(SecurityUtils.getUsername());
        warning.setCreateTime(new Date());

        weatherWarningMapper.insertWeatherWarning(warning);

        // 2. 创建影响区域记录
        List<WeatherWarningArea> areas = new ArrayList<>();
        for (WeatherWarningAreaDTO areaDto : createDTO.getAffectedAreas()) {
            WeatherWarningArea area = new WeatherWarningArea();
            area.setWarningId(warning.getWarningId());
            area.setRegionId(areaDto.getRegionId());
            area.setRegionName(areaDto.getRegionName());
            area.setCreateTime(new Date());
            areas.add(area);
        }
        if (!areas.isEmpty()) {
            weatherWarningAreaMapper.batchInsertWeatherWarningArea(areas);
        }

        // 3. 创建预警发布告警
        weatherWarningAlarmService.createWarningAlarm(createDTO, warning);

        return warning.getWarningId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int sendNotifications(String warningId, WeatherWarningNotifyRequestDTO notifyDTO)
    {
        // 1. 获取预警信息
        WeatherWarning warning = weatherWarningMapper.selectWeatherWarningByWarningId(warningId);
        if (warning == null) {
            throw new ServiceException("预警信息不存在：" + warningId);
        }

        // 2. 更新预警为已通知状态
        warning.setIsNotified(WeatherWarningConstants.NOTIFIED);
        warning.setUpdateBy(SecurityUtils.getUsername());
        warning.setUpdateTime(new Date());
        weatherWarningMapper.updateWeatherWarning(warning);

        // 3. 创建通知记录
        int notificationCount = notificationService.batchCreateNotifications(warningId, notifyDTO.getNotifyTargets());

        // 4. 创建通知确认告警（按单位分组）
        weatherWarningAlarmService.createNotificationAlarms(warning, notifyDTO.getNotifyTargets());

        return notificationCount;
    }

    /**
     * 根据用户ID列表发送预警通知
     *
     * @param warningId 预警ID
     * @param userIds 用户ID列表
     * @return 发送成功的通知数量
     */
    @Override
    @Transactional
    public int sendNotificationsByUserIds(String warningId, List<Long> userIds)
    {
        // 1. 验证预警信息
        WeatherWarning warning = weatherWarningMapper.selectWeatherWarningByWarningId(warningId);
        if (warning == null) {
            throw new ServiceException("预警信息不存在：" + warningId);
        }

        // 2. 根据用户ID查询用户信息
        List<WeatherWarningNotifyTargetDTO> notifyTargets = new ArrayList<>();
        for (Long userId : userIds) {
            // 查询用户信息（这里需要调用用户服务获取用户详细信息）
            WeatherWarningNotifyTargetDTO target = getUserInfoById(userId);
            if (target != null) {
                notifyTargets.add(target);
            }
        }

        if (notifyTargets.isEmpty()) {
            throw new ServiceException("未找到有效的通知用户");
        }

        // 3. 更新预警为已通知状态
        warning.setIsNotified(WeatherWarningConstants.NOTIFIED);
        warning.setUpdateBy(SecurityUtils.getUsername());
        warning.setUpdateTime(new Date());
        weatherWarningMapper.updateWeatherWarning(warning);

        // 4. 创建通知记录
        int notificationCount = notificationService.batchCreateNotifications(warningId, notifyTargets);

        // 5. 创建通知确认告警（按单位分组）
        weatherWarningAlarmService.createNotificationAlarms(warning, notifyTargets);

        return notificationCount;
    }

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 通知目标DTO
     */
    private WeatherWarningNotifyTargetDTO getUserInfoById(Long userId)
    {
        try {
            // 查询用户信息
            SysUser user = userService.selectUserById(userId);
            if (user == null) {
                log.warn("用户不存在：userId={}", userId);
                return null;
            }

            // 转换为通知目标DTO
            WeatherWarningNotifyTargetDTO target = new WeatherWarningNotifyTargetDTO();
            target.setContactUserId(userId);
            target.setContactUserName(user.getNickName() != null ? user.getNickName() : user.getUserName());
            target.setContactPhone(user.getPhonenumber());
            target.setDeptId(user.getDeptId());

            // 设置部门名称
            if (user.getDept() != null) {
                target.setDeptName(user.getDept().getDeptName());
                target.setContactDeptName(user.getDept().getDeptName());
            } else {
                target.setDeptName("未知部门");
                target.setContactDeptName("未知部门");
            }

            // 设置单位信息（使用用户的orgId作为单位ID）
            target.setContactUnitId(user.getOrgId() != null ? user.getOrgId().toString() : "0");

            // 查询单位名称（需要根据orgId查询单位表获取单位名称）
            String unitName = organizationService.getUnitNameByOrgId(user.getOrgId());
            target.setContactUnitName(unitName);

            // 设置默认超时时间（黄色预警60分钟）
            target.setTimeoutMinutes(getTimeoutMinutesByLevel("6"));

            return target;
        } catch (Exception e) {
            log.error("获取用户信息失败：userId={}", userId, e);
            return null;
        }
    }

    /**
     * 获取预警详情（包含统计信息）
     * 
     * @param warningId 预警ID
     * @return 预警详情VO
     */
    @Override
    public WeatherWarningVO selectWeatherWarningDetail(String warningId)
    {
        // 查询预警信息（包含统计）
        WeatherWarning warning = weatherWarningMapper.selectWeatherWarningWithStats(warningId);
        if (warning == null) {
            return null;
        }

        // 转换为VO
        WeatherWarningVO vo = new WeatherWarningVO();
        BeanUtils.copyProperties(warning, vo);

        // 设置字典标签
        vo.setWarningTypeLabel(dictDataService.selectDictLabel("weather_warning_type", warning.getWarningType()));
        vo.setWarningLevelLabel(dictDataService.selectDictLabel("alarm_level", warning.getWarningLevel()));
        vo.setStatusLabel(getStatusLabel(warning.getStatus()));

        // 设置是否已通知标签
        vo.setIsNotifiedLabel("1".equals(warning.getIsNotified()) ? "已通知" : "未通知");

        // 查询影响区域
        List<WeatherWarningArea> areas = weatherWarningAreaMapper.selectWeatherWarningAreaList(warningId);
        List<WeatherWarningAreaVO> areaVOs = areas.stream().map(area -> {
            WeatherWarningAreaVO areaVO = new WeatherWarningAreaVO();
            BeanUtils.copyProperties(area, areaVO);
            return areaVO;
        }).collect(Collectors.toList());
        vo.setAffectedAreas(areaVOs);
        
        // 设置影响区域描述
        String areaDesc = weatherWarningAreaMapper.selectAreaDescByWarningId(warningId);
        vo.setAffectedAreasDesc(areaDesc);

        // 查询通知进展信息
        if ("1".equals(warning.getIsNotified())) {
            // 如果已通知，查询通知进展详情
            List<WeatherWarningProgressVO> progressList = notificationService.selectNotificationProgress(warningId);
            vo.setNotificationProgress(progressList);

            // 统计信息
            vo.setTotalNotifications(progressList.size());
            long confirmedCount = progressList.stream().filter(p -> "1".equals(p.getConfirmStatus())).count();
            vo.setConfirmedNotifications((int) confirmedCount);
            vo.setUnconfirmedNotifications(progressList.size() - (int) confirmedCount);
        } else {
            // 未通知的预警
            vo.setTotalNotifications(0);
            vo.setConfirmedNotifications(0);
            vo.setUnconfirmedNotifications(0);
            vo.setNotificationProgress(new ArrayList<>());
        }

        // 设置告警类型标识（预警详情接口固定为预警类型）
        vo.setAlarmType("warning_detail");

        return vo;
    }

    /**
     * 更新预警状态
     * 
     * @param warningId 预警ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateWeatherWarningStatus(String warningId, String status)
    {
        return weatherWarningMapper.updateWeatherWarningStatus(warningId, status);
    }

    /**
     * 取消预警
     * 
     * @param warningId 预警ID
     * @param reason 取消原因
     * @return 结果
     */
    @Override
    public int cancelWarning(String warningId, String reason)
    {
        // 更新预警状态为取消
        int result = weatherWarningMapper.updateWeatherWarningStatus(warningId, "2");
        
        // TODO: 发送取消通知
        
        return result;
    }

    /**
     * 查询有效的预警列表
     * 
     * @return 有效预警列表
     */
    @Override
    public List<WeatherWarning> selectActiveWarnings()
    {
        return weatherWarningMapper.selectActiveWarnings();
    }

    /**
     * 查询即将过期的预警列表
     * 
     * @param minutes 提前多少分钟
     * @return 即将过期的预警列表
     */
    @Override
    public List<WeatherWarning> selectExpiringWarnings(int minutes)
    {
        return weatherWarningMapper.selectExpiringWarnings(minutes);
    }

    /**
     * 根据预警等级获取超时时间
     * 
     * @param warningLevel 预警等级
     * @return 超时时间（分钟）
     */
    @Override
    public int getTimeoutMinutesByLevel(String warningLevel)
    {
        switch (warningLevel) {
            case "8": // 红色预警
                return 15;
            case "7": // 橙色预警
                return 30;
            case "6": // 黄色预警
                return 60;
            case "5": // 蓝色预警
                return 120;
            default:
                return 30; // 默认30分钟
        }
    }

    /**
     * 查询气象预警详细列表（包含影响区域和通知记录）
     */
    @Override
    public List<WeatherWarningVO> selectWeatherWarningDetailList(WeatherWarningDTO queryDTO)
    {
        // 1. 查询预警基本信息
        List<WeatherWarning> warnings = weatherWarningMapper.selectWeatherWarningList(queryDTO);

        List<WeatherWarningVO> result = new ArrayList<>();

        for (WeatherWarning warning : warnings) {
            // 2. 转换为VO
            WeatherWarningVO vo = new WeatherWarningVO();
            BeanUtils.copyProperties(warning, vo);

            // 3. 设置字典标签
            vo.setWarningTypeLabel(dictDataService.selectDictLabel("weather_warning_type", warning.getWarningType()));
            vo.setWarningLevelLabel(dictDataService.selectDictLabel("alarm_level", warning.getWarningLevel()));
            vo.setStatusLabel(getStatusLabel(warning.getStatus()));
            vo.setIsNotifiedLabel("1".equals(warning.getIsNotified()) ? "已通知" : "未通知");

            // 4. 查询影响区域
            List<WeatherWarningArea> areas = weatherWarningAreaMapper.selectWeatherWarningAreaList(warning.getWarningId());
            List<WeatherWarningAreaVO> areaVOs = areas.stream().map(area -> {
                WeatherWarningAreaVO areaVO = new WeatherWarningAreaVO();
                BeanUtils.copyProperties(area, areaVO);
                return areaVO;
            }).collect(Collectors.toList());
            vo.setAffectedAreas(areaVOs);

            // 5. 设置影响区域描述
            String areaDesc = weatherWarningAreaMapper.selectAreaDescByWarningId(warning.getWarningId());
            vo.setAffectedAreasDesc(areaDesc);

            // 6. 查询通知记录
            if ("1".equals(warning.getIsNotified())) {
                // 如果已通知，查询通知进展详情
                List<WeatherWarningProgressVO> progressList = notificationService.selectNotificationProgress(warning.getWarningId());
                vo.setNotificationProgress(progressList);

                // 统计信息
                vo.setTotalNotifications(progressList.size());
                long confirmedCount = progressList.stream().filter(p -> "1".equals(p.getConfirmStatus())).count();
                vo.setConfirmedNotifications((int) confirmedCount);
                vo.setUnconfirmedNotifications(progressList.size() - (int) confirmedCount);

                // 计算超时数量
                long timeoutCount = progressList.stream().filter(p -> "1".equals(p.getIsTimeout())).count();
                vo.setTimeoutNotifications((int) timeoutCount);
            } else {
                // 未通知的预警
                vo.setTotalNotifications(0);
                vo.setConfirmedNotifications(0);
                vo.setUnconfirmedNotifications(0);
                vo.setTimeoutNotifications(0);
                vo.setNotificationProgress(new ArrayList<>());
            }

            // 7. 设置告警类型标识
            vo.setAlarmType("warning_list");

            result.add(vo);
        }

        return result;
    }

    /**
     * 获取状态标签
     */
    private String getStatusLabel(String status) {
        switch (status) {
            case "0": return "有效";
            case "1": return "失效";
            case "2": return "取消";
            default: return "未知";
        }
    }
}
