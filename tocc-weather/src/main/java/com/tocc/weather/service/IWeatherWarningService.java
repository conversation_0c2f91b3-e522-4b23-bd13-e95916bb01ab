package com.tocc.weather.service;

import java.util.List;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.dto.WeatherWarningDTO;
import com.tocc.weather.domain.dto.WeatherWarningCreateDTO;
import com.tocc.weather.domain.dto.WeatherWarningNotifyRequestDTO;
import com.tocc.weather.domain.vo.WeatherWarningVO;

/**
 * 气象预警信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IWeatherWarningService 
{
    /**
     * 查询气象预警信息
     * 
     * @param warningId 气象预警信息主键
     * @return 气象预警信息
     */
    public WeatherWarning selectWeatherWarningByWarningId(String warningId);

    /**
     * 查询气象预警信息列表
     * 
     * @param weatherWarningDTO 气象预警信息查询条件
     * @return 气象预警信息集合
     */
    public List<WeatherWarning> selectWeatherWarningList(WeatherWarningDTO weatherWarningDTO);

    /**
     * 新增气象预警信息
     * 
     * @param weatherWarning 气象预警信息
     * @return 结果
     */
    public int insertWeatherWarning(WeatherWarning weatherWarning);

    /**
     * 修改气象预警信息
     * 
     * @param weatherWarning 气象预警信息
     * @return 结果
     */
    public int updateWeatherWarning(WeatherWarning weatherWarning);

    /**
     * 批量删除气象预警信息
     * 
     * @param warningIds 需要删除的气象预警信息主键集合
     * @return 结果
     */
    public int deleteWeatherWarningByWarningIds(String[] warningIds);

    /**
     * 删除气象预警信息信息
     * 
     * @param warningId 气象预警信息主键
     * @return 结果
     */
    public int deleteWeatherWarningByWarningId(String warningId);

    /**
     * 创建预警
     *
     * @param createDTO 创建预警DTO
     * @return 预警ID
     */
    public String createWarning(WeatherWarningCreateDTO createDTO);

    /**
     * 发送通知
     *
     * @param warningId 预警ID
     * @param notifyDTO 通知请求DTO
     * @return 通知数量
     */
    public int sendNotifications(String warningId, WeatherWarningNotifyRequestDTO notifyDTO);

    /**
     * 获取预警详情（包含统计信息）
     * 
     * @param warningId 预警ID
     * @return 预警详情VO
     */
    public WeatherWarningVO selectWeatherWarningDetail(String warningId);

    /**
     * 更新预警状态
     * 
     * @param warningId 预警ID
     * @param status 状态
     * @return 结果
     */
    public int updateWeatherWarningStatus(String warningId, String status);

    /**
     * 取消预警
     * 
     * @param warningId 预警ID
     * @param reason 取消原因
     * @return 结果
     */
    public int cancelWarning(String warningId, String reason);

    /**
     * 查询有效的预警列表
     * 
     * @return 有效预警列表
     */
    public List<WeatherWarning> selectActiveWarnings();

    /**
     * 查询即将过期的预警列表
     * 
     * @param minutes 提前多少分钟
     * @return 即将过期的预警列表
     */
    public List<WeatherWarning> selectExpiringWarnings(int minutes);

    /**
     * 根据预警等级获取超时时间
     *
     * @param warningLevel 预警等级
     * @return 超时时间（分钟）
     */
    public int getTimeoutMinutesByLevel(String warningLevel);

    /**
     * 查询气象预警详细列表（包含影响区域和通知记录）
     *
     * @param queryDTO 查询条件
     * @return 预警详细列表
     */
    public List<WeatherWarningVO> selectWeatherWarningDetailList(WeatherWarningDTO queryDTO);
}
