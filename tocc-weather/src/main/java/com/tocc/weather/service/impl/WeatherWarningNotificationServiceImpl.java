package com.tocc.weather.service.impl;

import java.util.List;
import java.util.Date;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.exception.ServiceException;
import com.tocc.weather.mapper.WeatherWarningNotificationMapper;
import com.tocc.weather.domain.entity.WeatherWarning;
import com.tocc.weather.domain.entity.WeatherWarningArea;
import com.tocc.weather.domain.entity.WeatherWarningNotification;
import com.tocc.weather.domain.dto.WeatherWarningNotifyDTO;
import com.tocc.weather.domain.vo.WeatherWarningProgressVO;
import com.tocc.weather.service.IWeatherWarningService;
import com.tocc.weather.service.IWeatherWarningNotificationService;
import com.tocc.weather.utils.WeatherWarningUtils;
import com.tocc.weather.domain.dto.WeatherWarningNotifyTargetDTO;
import com.tocc.weather.domain.dto.WeatherWarningNotificationDTO;
import com.tocc.weather.domain.vo.WeatherWarningVO;
import com.tocc.weather.mapper.WeatherWarningAreaMapper;
import com.tocc.weather.constants.WeatherWarningConstants;
import com.tocc.service.IAlarmService;
import com.tocc.domain.vo.AlarmInfoVO;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.bean.BeanUtils;

/**
 * 气象预警通知记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class WeatherWarningNotificationServiceImpl implements IWeatherWarningNotificationService 
{
    private static final Logger log = LoggerFactory.getLogger(WeatherWarningNotificationServiceImpl.class);

    @Autowired
    private WeatherWarningNotificationMapper weatherWarningNotificationMapper;

    @Autowired
    private IWeatherWarningService weatherWarningService;

    @Autowired
    private IAlarmService alarmService;

    @Autowired
    private WeatherWarningAreaMapper weatherWarningAreaMapper;

    /**
     * 查询气象预警通知记录
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @return 气象预警通知记录
     */
    @Override
    public WeatherWarningNotification selectWeatherWarningNotificationByIds(String warningId, Long contactUserId)
    {
        return weatherWarningNotificationMapper.selectWeatherWarningNotificationByIds(warningId, contactUserId);
    }

    /**
     * 查询气象预警通知记录列表
     * 
     * @param warningId 预警ID
     * @return 气象预警通知记录集合
     */
    @Override
    public List<WeatherWarningNotification> selectWeatherWarningNotificationList(String warningId)
    {
        return weatherWarningNotificationMapper.selectWeatherWarningNotificationList(warningId);
    }

    /**
     * 查询用户的预警通知列表
     * 
     * @param contactUserId 联系人用户ID
     * @return 气象预警通知记录集合
     */
    @Override
    public List<WeatherWarningNotification> selectNotificationsByUserId(Long contactUserId)
    {
        return weatherWarningNotificationMapper.selectNotificationsByUserId(contactUserId);
    }

    /**
     * 新增气象预警通知记录
     * 
     * @param weatherWarningNotification 气象预警通知记录
     * @return 结果
     */
    @Override
    public int insertWeatherWarningNotification(WeatherWarningNotification weatherWarningNotification)
    {
        weatherWarningNotification.setCreateTime(DateUtils.getNowDate());
        return weatherWarningNotificationMapper.insertWeatherWarningNotification(weatherWarningNotification);
    }

    /**
     * 修改气象预警通知记录
     * 
     * @param weatherWarningNotification 气象预警通知记录
     * @return 结果
     */
    @Override
    public int updateWeatherWarningNotification(WeatherWarningNotification weatherWarningNotification)
    {
        weatherWarningNotification.setUpdateTime(DateUtils.getNowDate());
        return weatherWarningNotificationMapper.updateWeatherWarningNotification(weatherWarningNotification);
    }

    /**
     * 批量删除气象预警通知记录
     * 
     * @param warningIds 需要删除的预警ID集合
     * @return 结果
     */
    @Override
    public int deleteWeatherWarningNotificationByWarningIds(String[] warningIds)
    {
        return weatherWarningNotificationMapper.deleteWeatherWarningNotificationByWarningIds(warningIds);
    }

    /**
     * 删除预警的所有通知记录
     * 
     * @param warningId 预警ID
     * @return 结果
     */
    @Override
    public int deleteWeatherWarningNotificationByWarningId(String warningId)
    {
        return weatherWarningNotificationMapper.deleteWeatherWarningNotificationByWarningId(warningId);
    }

    /**
     * 批量创建通知记录
     * 
     * @param warningId 预警ID
     * @param areas 影响区域列表
     * @param notifyTargets 通知对象列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchCreateNotifications(String warningId, List<WeatherWarningArea> areas, List<WeatherWarningNotifyDTO> notifyTargets)
    {
        List<WeatherWarningNotification> notifications = new ArrayList<>();
        
        // 获取预警信息以确定超时时间
        WeatherWarning warning = weatherWarningService.selectWeatherWarningByWarningId(warningId);
        int timeoutMinutes = WeatherWarningUtils.getTimeoutMinutesByLevel(warning.getWarningLevel());
        
        for (WeatherWarningNotifyDTO target : notifyTargets) {
            WeatherWarningNotification notification = new WeatherWarningNotification();
            notification.setWarningId(warningId);
            notification.setContactUserId(target.getContactUserId());
            notification.setContactUnitName(target.getContactUnitName());
            notification.setContactDeptName(target.getContactDeptName());
            notification.setContactPostName(target.getContactPostName());
            notification.setContactUserName(target.getContactUserName());
            notification.setContactPhone(target.getContactPhone());
            notification.setContactEmail(target.getContactEmail());
            notification.setNotificationTime(new Date());
            notification.setConfirmStatus("0");
            notification.setTimeoutMinutes(timeoutMinutes);
            notification.setIsTimeout("0");
            notification.setCreateTime(new Date());
            
            notifications.add(notification);
        }
        
        if (notifications.isEmpty()) {
            throw new ServiceException("没有有效的通知对象");
        }
        
        // 批量插入通知记录
        int result = weatherWarningNotificationMapper.batchInsertWeatherWarningNotification(notifications);
        
        // 发送通知
        sendNotifications(notifications);
        
        return result;
    }

    /**
     * 批量创建通知记录（新版本）
     *
     * @param warningId 预警ID
     * @param notifyTargets 通知对象列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchCreateNotifications(String warningId, List<WeatherWarningNotifyTargetDTO> notifyTargets)
    {
        List<WeatherWarningNotification> notifications = new ArrayList<>();

        // 获取预警信息以确定超时时间
        WeatherWarning warning = weatherWarningService.selectWeatherWarningByWarningId(warningId);
        int timeoutMinutes = WeatherWarningUtils.getTimeoutMinutesByLevel(warning.getWarningLevel());

        for (WeatherWarningNotifyTargetDTO target : notifyTargets) {
            WeatherWarningNotification notification = new WeatherWarningNotification();
            notification.setWarningId(warningId);
            notification.setContactUserId(target.getContactUserId());
            notification.setContactUnitName(target.getContactUnitName());
            notification.setContactDeptName(target.getContactDeptName());
            notification.setContactPostName(target.getContactPostName());
            notification.setContactUserName(target.getContactUserName());
            notification.setContactPhone(target.getContactPhone());
            notification.setContactEmail(target.getContactEmail());
            notification.setNotificationTime(new Date());
            notification.setConfirmStatus("0");
            notification.setTimeoutMinutes(timeoutMinutes);
            notification.setIsTimeout("0");
            notification.setCreateTime(new Date());

            notifications.add(notification);
        }

        if (notifications.isEmpty()) {
            throw new ServiceException("没有有效的通知对象");
        }

        // 批量插入通知记录
        int result = weatherWarningNotificationMapper.batchInsertWeatherWarningNotification(notifications);

        // 发送通知
        sendNotifications(notifications);

        return result;
    }

    /**
     * 确认预警通知
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @param confirmUserId 确认人ID
     * @param confirmUserName 确认人姓名
     * @return 结果
     */
    @Override
    public int confirmNotification(String warningId, Long contactUserId, Long confirmUserId, String confirmUserName)
    {
        return weatherWarningNotificationMapper.confirmNotification(warningId, contactUserId, confirmUserId, confirmUserName);
    }

    /**
     * 查询预警通知进展
     * 
     * @param warningId 预警ID
     * @return 通知进展列表
     */
    @Override
    public List<WeatherWarningProgressVO> selectNotificationProgress(String warningId)
    {
        return weatherWarningNotificationMapper.selectNotificationProgress(warningId);
    }

    /**
     * 查询超时的通知记录
     * 
     * @return 超时通知记录列表
     */
    @Override
    public List<WeatherWarningNotification> selectTimeoutNotifications()
    {
        return weatherWarningNotificationMapper.selectTimeoutNotifications();
    }

    /**
     * 查询未确认的通知记录
     * 
     * @param warningId 预警ID
     * @return 未确认通知记录列表
     */
    @Override
    public List<WeatherWarningNotification> selectUnconfirmedNotifications(String warningId)
    {
        return weatherWarningNotificationMapper.selectUnconfirmedNotifications(warningId);
    }

    /**
     * 更新通知超时状态
     * 
     * @param warningId 预警ID
     * @param contactUserId 联系人用户ID
     * @param alarmId 告警ID
     * @return 结果
     */
    @Override
    public int updateNotificationTimeout(String warningId, Long contactUserId, String alarmId)
    {
        return weatherWarningNotificationMapper.updateNotificationTimeout(warningId, contactUserId, alarmId);
    }

    /**
     * 催办未确认通知
     * 
     * @param warningId 预警ID
     * @return 结果
     */
    @Override
    public int remindUnconfirmedNotifications(String warningId)
    {
        List<WeatherWarningNotification> unconfirmedList = selectUnconfirmedNotifications(warningId);
        
        if (unconfirmedList.isEmpty()) {
            return 0;
        }
        
        // 获取预警信息
        WeatherWarning warning = weatherWarningService.selectWeatherWarningByWarningId(warningId);
        
        // 发送催办通知
        for (WeatherWarningNotification notification : unconfirmedList) {
            sendSingleNotification(notification, warning);
        }
        
        return unconfirmedList.size();
    }

    /**
     * 发送通知
     * 
     * @param notifications 通知记录列表
     */
    @Override
    public void sendNotifications(List<WeatherWarningNotification> notifications)
    {
        if (notifications == null || notifications.isEmpty()) {
            return;
        }
        
        // 获取预警信息
        String warningId = notifications.get(0).getWarningId();
        WeatherWarning warning = weatherWarningService.selectWeatherWarningByWarningId(warningId);
        
        for (WeatherWarningNotification notification : notifications) {
            try {
                sendSingleNotification(notification, warning);
            } catch (Exception e) {
                log.error("发送通知失败：{}", notification.getContactUserName(), e);
                // 发送失败只记录日志，不影响主流程
            }
        }
    }

    /**
     * 发送单个通知
     * 
     * @param notification 通知记录
     * @param warning 预警信息
     */
    @Override
    public void sendSingleNotification(WeatherWarningNotification notification, WeatherWarning warning)
    {
        try {
            // 1. 系统内通知
            sendSystemNotification(notification, warning);
            
            // 2. 短信通知（如果有手机号）
            if (StringUtils.isNotEmpty(notification.getContactPhone())) {
                sendSmsNotification(notification, warning);
            }
            
            // 3. 邮件通知（如果有邮箱）
            if (StringUtils.isNotEmpty(notification.getContactEmail())) {
                sendEmailNotification(notification, warning);
            }
            
            log.info("预警通知发送成功：{} -> {}", warning.getWarningId(), notification.getContactUserName());
            
        } catch (Exception e) {
            log.error("发送预警通知失败：{} -> {}", warning.getWarningId(), notification.getContactUserName(), e);
            throw e;
        }
    }

    /**
     * 发送系统内通知
     */
    private void sendSystemNotification(WeatherWarningNotification notification, WeatherWarning warning)
    {
        // TODO: 调用系统通知服务
        log.info("发送系统通知：{} -> {}", warning.getWarningId(), notification.getContactUserName());
    }

    /**
     * 发送短信通知
     */
    private void sendSmsNotification(WeatherWarningNotification notification, WeatherWarning warning)
    {
        String content = String.format(
            "【气象预警】%s发布%s，发布时间：%s。请及时确认并采取相应措施。",
            getWarningTypeLabel(warning.getWarningType()),
            getWarningLevelLabel(warning.getWarningLevel()),
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, warning.getIssueTime())
        );
        
        // TODO: 调用短信服务
        log.info("发送短信通知：{} -> {} ({})", warning.getWarningId(), notification.getContactUserName(), notification.getContactPhone());
    }

    /**
     * 发送邮件通知
     */
    private void sendEmailNotification(WeatherWarningNotification notification, WeatherWarning warning)
    {
        String subject = String.format("气象预警通知 - %s%s", getWarningTypeLabel(warning.getWarningType()), getWarningLevelLabel(warning.getWarningLevel()));
        String content = buildEmailContent(warning, notification);
        
        // TODO: 调用邮件服务
        log.info("发送邮件通知：{} -> {} ({})", warning.getWarningId(), notification.getContactUserName(), notification.getContactEmail());
    }

    /**
     * 构建邮件内容
     */
    private String buildEmailContent(WeatherWarning warning, WeatherWarningNotification notification)
    {
        StringBuilder content = new StringBuilder();
        content.append("尊敬的").append(notification.getContactUserName()).append("：\n\n");
        content.append("您收到一条气象预警通知，详情如下：\n\n");
        content.append("预警类型：").append(getWarningTypeLabel(warning.getWarningType())).append("\n");
        content.append("预警等级：").append(getWarningLevelLabel(warning.getWarningLevel())).append("\n");
        content.append("发布时间：").append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, warning.getIssueTime())).append("\n");
        if (warning.getExpireTime() != null) {
            content.append("失效时间：").append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, warning.getExpireTime())).append("\n");
        }
        content.append("\n预警内容：\n").append(warning.getWarningContent()).append("\n");
        if (StringUtils.isNotEmpty(warning.getPreventionGuide())) {
            content.append("\n防御指南：\n").append(warning.getPreventionGuide()).append("\n");
        }
        content.append("\n请及时确认此预警信息并采取相应措施。");
        
        return content.toString();
    }

    /**
     * 获取预警类型标签
     */
    private String getWarningTypeLabel(String warningType)
    {
        return WeatherWarningUtils.getWarningTypeName(warningType);
    }

    /**
     * 获取预警等级标签
     */
    private String getWarningLevelLabel(String warningLevel)
    {
        return WeatherWarningUtils.getWarningLevelName(warningLevel);
    }

    /**
     * 发送短信催办未确认的通知
     */
    @Override
    public int sendSmsReminder(String warningId)
    {
        // 查询未确认的通知
        List<WeatherWarningNotification> unconfirmedNotifications = selectUnconfirmedNotifications(warningId);

        if (unconfirmedNotifications.isEmpty()) {
            log.info("预警{}没有未确认的通知", warningId);
            return 0;
        }

        // 获取预警信息
        WeatherWarning warning = weatherWarningService.selectWeatherWarningByWarningId(warningId);
        if (warning == null) {
            log.error("预警信息不存在：{}", warningId);
            return 0;
        }

        int successCount = 0;

        for (WeatherWarningNotification notification : unconfirmedNotifications) {
            try {
                // 构建短信内容
                String smsContent = buildSmsReminderContent(warning, notification);

                // 发送短信（参照应急事件的短信发送方式）
                // 这里需要调用短信服务，暂时记录日志
                log.info("发送催办短信：手机号={}, 内容={}", notification.getContactPhone(), smsContent);

                // TODO: 实际的短信发送逻辑
                // smsService.sendSms(notification.getContactPhone(), smsContent);

                successCount++;

            } catch (Exception e) {
                log.error("发送催办短信失败：{}", notification.getContactUserName(), e);
            }
        }

        log.info("催办短信发送完成：预警ID={}, 成功发送{}条", warningId, successCount);
        return successCount;
    }

    /**
     * 查询通知列表（通用查询）
     */
    @Override
    public List<WeatherWarningNotification> selectNotificationList(WeatherWarningNotificationDTO queryDTO)
    {
        // 根据查询类型设置查询条件
        if ("my".equals(queryDTO.getQueryType())) {
            // 查询我的通知
            Long currentUserId = SecurityUtils.getUserId();
            queryDTO.setContactUserId(currentUserId);
        } else if ("unconfirmed".equals(queryDTO.getQueryType())) {
            // 查询未确认的通知
            queryDTO.setConfirmStatus("0");
        } else if ("timeout".equals(queryDTO.getQueryType())) {
            // 查询超时的通知
            queryDTO.setIsTimeout("1");
        }

        // 调用Mapper查询
        return weatherWarningNotificationMapper.selectNotificationList(queryDTO);
    }

    /**
     * 构建催办短信内容
     */
    private String buildSmsReminderContent(WeatherWarning warning, WeatherWarningNotification notification)
    {
        String warningTypeName = getWarningTypeLabel(warning.getWarningType());
        String warningLevelName = getWarningLevelLabel(warning.getWarningLevel());
        String issueTime = DateUtils.parseDateToStr("MM月dd日HH时mm分", warning.getIssueTime());

        return String.format("【催办提醒】您收到的%s发布的%s%s尚未确认，请及时登录系统确认。发布时间：%s",
            issueTime, warningTypeName, warningLevelName, issueTime);
    }

    /**
     * 根据告警ID查询预警详情
     */
    @Override
    public WeatherWarningVO getWarningDetailByAlarmId(String alarmId)
    {
        try {
            // 1. 根据告警ID查询告警信息
            AlarmInfoVO alarmInfo = alarmService.selectAlarmInfoByAlarmId(alarmId);
            if (alarmInfo == null) {
                throw new ServiceException("告警信息不存在：" + alarmId);
            }

            // 2. 从告警信息中获取预警ID
            String warningId = alarmInfo.getSourceId();
            if (warningId == null || warningId.trim().isEmpty()) {
                throw new ServiceException("告警信息中未找到关联的预警ID");
            }

            // 3. 查询预警详情
            WeatherWarning warning = weatherWarningService.selectWeatherWarningByWarningId(warningId);
            if (warning == null) {
                throw new ServiceException("预警信息不存在：" + warningId);
            }

            // 4. 转换为VO对象
            WeatherWarningVO warningVO = new WeatherWarningVO();
            BeanUtils.copyBeanProp(warningVO, warning);

            // 5. 查询影响区域信息
            List<WeatherWarningArea> areas = weatherWarningAreaMapper.selectWeatherWarningAreaList(warningId);
            if (areas != null && !areas.isEmpty()) {
                // 设置影响区域描述
                warningVO.setAffectedAreasDesc(buildAreaDescription(areas));
            }

            // 6. 根据告警类型决定是否查询通知统计信息
            if (WeatherWarningConstants.ALARM_SUBTYPE_WARNING.equals(alarmInfo.getAlarmSubtype())) {
                // 预警发布告警：查询详细的通知进展
                if ("1".equals(warning.getIsNotified())) {
                    // 如果已通知，查询通知进展详情
                    List<WeatherWarningProgressVO> progressList = selectNotificationProgress(warningId);
                    warningVO.setNotificationProgress(progressList);

                    // 统计信息
                    warningVO.setTotalNotifications(progressList.size());
                    long confirmedCount = progressList.stream().filter(p -> "1".equals(p.getConfirmStatus())).count();
                    warningVO.setConfirmedNotifications((int) confirmedCount);
                    warningVO.setUnconfirmedNotifications(progressList.size() - (int) confirmedCount);

                    // 设置告警类型标识
                    warningVO.setAlarmType("warning_publish");
                } else {
                    // 未通知的预警
                    warningVO.setTotalNotifications(0);
                    warningVO.setConfirmedNotifications(0);
                    warningVO.setUnconfirmedNotifications(0);
                    warningVO.setNotificationProgress(new ArrayList<>());
                    warningVO.setAlarmType("warning_publish");
                }
            } else if (WeatherWarningConstants.ALARM_SUBTYPE_NOTIFICATION.equals(alarmInfo.getAlarmSubtype())) {
                // 通知确认告警：不查询通知进展，只标识告警类型
                warningVO.setAlarmType("notification_confirm");
                // 设置当前用户的确认状态
                Long currentUserId = SecurityUtils.getUserId();
                WeatherWarningNotification userNotification = selectWeatherWarningNotificationByIds(warningId, currentUserId);
                if (userNotification != null) {
                    warningVO.setCurrentUserConfirmStatus(userNotification.getConfirmStatus());
                    warningVO.setCurrentUserConfirmTime(userNotification.getConfirmTime());
                }
            }

            return warningVO;

        } catch (Exception e) {
            log.error("根据告警ID查询预警详情失败：{}", alarmId, e);
            throw new ServiceException("查询预警详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据告警ID确认预警通知
     */
    @Override
    public int confirmNotificationByAlarmId(String alarmId, Long confirmUserId, String confirmUserName)
    {
        try {
            // 1. 根据告警ID查询告警信息
            AlarmInfoVO alarmInfo = alarmService.selectAlarmInfoByAlarmId(alarmId);
            if (alarmInfo == null) {
                throw new ServiceException("告警信息不存在：" + alarmId);
            }

            // 2. 验证告警类型是否为通知确认告警
            if (!WeatherWarningConstants.ALARM_SUBTYPE_NOTIFICATION.equals(alarmInfo.getAlarmSubtype())) {
                throw new ServiceException("该告警不是通知确认告警，无法确认");
            }

            // 3. 从告警信息中获取预警ID
            String warningId = alarmInfo.getSourceId();
            if (warningId == null || warningId.trim().isEmpty()) {
                throw new ServiceException("告警信息中未找到关联的预警ID");
            }

            // 4. 确认通知
            int result = confirmNotification(warningId, confirmUserId, confirmUserId, confirmUserName);

            // 5. 如果确认成功，更新告警状态为已处理
            if (result > 0) {
                try {
                    // 这里可以调用告警服务更新告警状态
                    // alarmService.updateAlarmStatus(alarmId, "1"); // 已处理
                    log.info("通知确认成功，告警ID：{}，用户：{}", alarmId, confirmUserName);
                } catch (Exception e) {
                    log.warn("更新告警状态失败：{}", e.getMessage());
                    // 不影响主流程
                }
            }

            return result;

        } catch (Exception e) {
            log.error("根据告警ID确认通知失败：{}", alarmId, e);
            throw new ServiceException("确认通知失败：" + e.getMessage());
        }
    }

    /**
     * 构建区域描述
     */
    private String buildAreaDescription(List<WeatherWarningArea> areas)
    {
        if (areas == null || areas.isEmpty()) {
            return "";
        }

        StringBuilder desc = new StringBuilder();
        for (int i = 0; i < areas.size(); i++) {
            if (i > 0) desc.append("、");
            desc.append(areas.get(i).getRegionName());
        }

        if (areas.size() > 1) {
            desc.append("等").append(areas.size()).append("个区域");
        }

        return desc.toString();
    }
}
