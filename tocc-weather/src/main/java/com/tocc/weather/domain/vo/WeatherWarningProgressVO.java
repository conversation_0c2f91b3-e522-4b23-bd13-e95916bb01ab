package com.tocc.weather.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 气象预警通知进展VO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningProgressVO
{
    /** 预警ID */
    private String warningId;

    /** 联系人用户ID */
    private Long contactUserId;

    /** 联系人所属单位 */
    private String contactUnitName;

    /** 联系人所属部门 */
    private String contactDeptName;

    /** 联系人岗位 */
    private String contactPostName;

    /** 联系人姓名 */
    private String contactUserName;

    /** 联系人电话 */
    private String contactPhone;

    /** 通知时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date notificationTime;

    /** 确认状态 */
    private String confirmStatus;

    /** 确认状态标签 */
    private String confirmStatusLabel;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;

    /** 确认人姓名 */
    private String confirmUserName;

    /** 超时时间（分钟） */
    private Integer timeoutMinutes;

    /** 是否超时 */
    private String isTimeout;

    /** 超时剩余时间（分钟，前端计算用） */
    private Integer remainingMinutes;

    public String getWarningId() 
    {
        return warningId;
    }

    public void setWarningId(String warningId) 
    {
        this.warningId = warningId;
    }

    public Long getContactUserId() 
    {
        return contactUserId;
    }

    public void setContactUserId(Long contactUserId) 
    {
        this.contactUserId = contactUserId;
    }

    public String getContactUnitName() 
    {
        return contactUnitName;
    }

    public void setContactUnitName(String contactUnitName) 
    {
        this.contactUnitName = contactUnitName;
    }

    public String getContactDeptName() 
    {
        return contactDeptName;
    }

    public void setContactDeptName(String contactDeptName) 
    {
        this.contactDeptName = contactDeptName;
    }

    public String getContactPostName() 
    {
        return contactPostName;
    }

    public void setContactPostName(String contactPostName) 
    {
        this.contactPostName = contactPostName;
    }

    public String getContactUserName() 
    {
        return contactUserName;
    }

    public void setContactUserName(String contactUserName) 
    {
        this.contactUserName = contactUserName;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public Date getNotificationTime() 
    {
        return notificationTime;
    }

    public void setNotificationTime(Date notificationTime) 
    {
        this.notificationTime = notificationTime;
    }

    public String getConfirmStatus() 
    {
        return confirmStatus;
    }

    public void setConfirmStatus(String confirmStatus) 
    {
        this.confirmStatus = confirmStatus;
    }

    public String getConfirmStatusLabel() 
    {
        return confirmStatusLabel;
    }

    public void setConfirmStatusLabel(String confirmStatusLabel) 
    {
        this.confirmStatusLabel = confirmStatusLabel;
    }

    public Date getConfirmTime() 
    {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) 
    {
        this.confirmTime = confirmTime;
    }

    public String getConfirmUserName() 
    {
        return confirmUserName;
    }

    public void setConfirmUserName(String confirmUserName) 
    {
        this.confirmUserName = confirmUserName;
    }

    public Integer getTimeoutMinutes() 
    {
        return timeoutMinutes;
    }

    public void setTimeoutMinutes(Integer timeoutMinutes) 
    {
        this.timeoutMinutes = timeoutMinutes;
    }

    public String getIsTimeout() 
    {
        return isTimeout;
    }

    public void setIsTimeout(String isTimeout) 
    {
        this.isTimeout = isTimeout;
    }

    public Integer getRemainingMinutes() 
    {
        return remainingMinutes;
    }

    public void setRemainingMinutes(Integer remainingMinutes) 
    {
        this.remainingMinutes = remainingMinutes;
    }

    @Override
    public String toString() {
        return "WeatherWarningProgressVO{" +
                "warningId='" + warningId + '\'' +
                ", contactUserId=" + contactUserId +
                ", contactUnitName='" + contactUnitName + '\'' +
                ", contactUserName='" + contactUserName + '\'' +
                ", notificationTime=" + notificationTime +
                ", confirmStatus='" + confirmStatus + '\'' +
                ", confirmTime=" + confirmTime +
                ", timeoutMinutes=" + timeoutMinutes +
                ", isTimeout='" + isTimeout + '\'' +
                '}';
    }
}
