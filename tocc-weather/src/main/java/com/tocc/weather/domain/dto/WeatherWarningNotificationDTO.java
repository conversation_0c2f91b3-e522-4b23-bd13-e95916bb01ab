package com.tocc.weather.domain.dto;

import com.tocc.common.core.domain.BaseEntity;

/**
 * 气象预警通知查询DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningNotificationDTO extends BaseEntity
{
    /** 预警ID */
    private String warningId;

    /** 联系人用户ID */
    private Long contactUserId;

    /** 联系人单位名称 */
    private String contactUnitName;

    /** 联系人姓名 */
    private String contactUserName;

    /** 确认状态（0-未确认 1-已确认） */
    private String confirmStatus;

    /** 是否超时（0-未超时 1-已超时） */
    private String isTimeout;

    /** 查询类型（my-我的通知，all-所有通知，unconfirmed-未确认，timeout-超时） */
    private String queryType;

    public String getWarningId() 
    {
        return warningId;
    }

    public void setWarningId(String warningId) 
    {
        this.warningId = warningId;
    }

    public Long getContactUserId() 
    {
        return contactUserId;
    }

    public void setContactUserId(Long contactUserId) 
    {
        this.contactUserId = contactUserId;
    }

    public String getContactUnitName() 
    {
        return contactUnitName;
    }

    public void setContactUnitName(String contactUnitName) 
    {
        this.contactUnitName = contactUnitName;
    }

    public String getContactUserName() 
    {
        return contactUserName;
    }

    public void setContactUserName(String contactUserName) 
    {
        this.contactUserName = contactUserName;
    }

    public String getConfirmStatus() 
    {
        return confirmStatus;
    }

    public void setConfirmStatus(String confirmStatus) 
    {
        this.confirmStatus = confirmStatus;
    }

    public String getIsTimeout() 
    {
        return isTimeout;
    }

    public void setIsTimeout(String isTimeout) 
    {
        this.isTimeout = isTimeout;
    }

    public String getQueryType() 
    {
        return queryType;
    }

    public void setQueryType(String queryType) 
    {
        this.queryType = queryType;
    }

    @Override
    public String toString() {
        return "WeatherWarningNotificationDTO{" +
                "warningId='" + warningId + '\'' +
                ", contactUserId=" + contactUserId +
                ", contactUnitName='" + contactUnitName + '\'' +
                ", contactUserName='" + contactUserName + '\'' +
                ", confirmStatus='" + confirmStatus + '\'' +
                ", isTimeout='" + isTimeout + '\'' +
                ", queryType='" + queryType + '\'' +
                '}';
    }
}
