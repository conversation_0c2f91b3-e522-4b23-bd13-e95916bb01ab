package com.tocc.weather.domain.dto;

import java.util.List;
import javax.validation.constraints.NotEmpty;

/**
 * 气象预警通知简化请求DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningNotifySimpleDTO 
{
    /** 用户ID列表 */
    @NotEmpty(message = "请选择通知用户")
    private List<Long> userIds;

    public List<Long> getUserIds() 
    {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) 
    {
        this.userIds = userIds;
    }

    @Override
    public String toString() {
        return "WeatherWarningNotifySimpleDTO{" +
                "userIds=" + userIds +
                '}';
    }
}
