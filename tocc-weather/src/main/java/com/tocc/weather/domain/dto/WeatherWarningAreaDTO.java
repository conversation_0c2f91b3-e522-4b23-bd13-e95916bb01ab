package com.tocc.weather.domain.dto;

import javax.validation.constraints.NotBlank;

/**
 * 气象预警影响区域DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningAreaDTO
{
    /** 行政区划ID */
    @NotBlank(message = "行政区划ID不能为空")
    private String regionId;

    /** 行政区划名称 */
    @NotBlank(message = "行政区划名称不能为空")
    private String regionName;

    public String getRegionId() 
    {
        return regionId;
    }

    public void setRegionId(String regionId) 
    {
        this.regionId = regionId;
    }

    public String getRegionName() 
    {
        return regionName;
    }

    public void setRegionName(String regionName) 
    {
        this.regionName = regionName;
    }

    @Override
    public String toString() {
        return "WeatherWarningAreaDTO{" +
                "regionId='" + regionId + '\'' +
                ", regionName='" + regionName + '\'' +
                '}';
    }
}
