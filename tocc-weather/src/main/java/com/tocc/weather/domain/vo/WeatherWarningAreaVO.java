package com.tocc.weather.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 气象预警影响区域VO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningAreaVO
{
    /** 预警ID */
    private String warningId;

    /** 行政区划ID */
    private String regionId;

    /** 行政区划名称 */
    private String regionName;

    /** 完整路径 */
    private String regionFullPath;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    public String getWarningId() 
    {
        return warningId;
    }

    public void setWarningId(String warningId) 
    {
        this.warningId = warningId;
    }

    public String getRegionId() 
    {
        return regionId;
    }

    public void setRegionId(String regionId) 
    {
        this.regionId = regionId;
    }

    public String getRegionName() 
    {
        return regionName;
    }

    public void setRegionName(String regionName) 
    {
        this.regionName = regionName;
    }

    public String getRegionFullPath() 
    {
        return regionFullPath;
    }

    public void setRegionFullPath(String regionFullPath) 
    {
        this.regionFullPath = regionFullPath;
    }

    public Date getCreateTime() 
    {
        return createTime;
    }

    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "WeatherWarningAreaVO{" +
                "warningId='" + warningId + '\'' +
                ", regionId='" + regionId + '\'' +
                ", regionName='" + regionName + '\'' +
                ", regionFullPath='" + regionFullPath + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
