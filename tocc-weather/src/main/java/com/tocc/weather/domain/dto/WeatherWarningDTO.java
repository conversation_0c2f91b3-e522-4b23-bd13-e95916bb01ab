package com.tocc.weather.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 气象预警查询DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningDTO
{
    /** 预警类型 */
    private String warningType;

    /** 预警等级 */
    private String warningLevel;

    /** 状态 */
    private String status;

    /** 发布时间开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTimeStart;

    /** 发布时间结束 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTimeEnd;

    /** 按影响区域查询 */
    private String regionId;

    /** 按创建人查询 */
    private String createBy;

    /** 预警内容关键字 */
    private String contentKeyword;

    public String getWarningType() 
    {
        return warningType;
    }

    public void setWarningType(String warningType) 
    {
        this.warningType = warningType;
    }

    public String getWarningLevel() 
    {
        return warningLevel;
    }

    public void setWarningLevel(String warningLevel) 
    {
        this.warningLevel = warningLevel;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public Date getIssueTimeStart() 
    {
        return issueTimeStart;
    }

    public void setIssueTimeStart(Date issueTimeStart) 
    {
        this.issueTimeStart = issueTimeStart;
    }

    public Date getIssueTimeEnd() 
    {
        return issueTimeEnd;
    }

    public void setIssueTimeEnd(Date issueTimeEnd) 
    {
        this.issueTimeEnd = issueTimeEnd;
    }

    public String getRegionId() 
    {
        return regionId;
    }

    public void setRegionId(String regionId) 
    {
        this.regionId = regionId;
    }

    public String getCreateBy() 
    {
        return createBy;
    }

    public void setCreateBy(String createBy) 
    {
        this.createBy = createBy;
    }

    public String getContentKeyword() 
    {
        return contentKeyword;
    }

    public void setContentKeyword(String contentKeyword) 
    {
        this.contentKeyword = contentKeyword;
    }

    @Override
    public String toString() {
        return "WeatherWarningDTO{" +
                "warningType='" + warningType + '\'' +
                ", warningLevel='" + warningLevel + '\'' +
                ", status='" + status + '\'' +
                ", issueTimeStart=" + issueTimeStart +
                ", issueTimeEnd=" + issueTimeEnd +
                ", regionId='" + regionId + '\'' +
                ", createBy='" + createBy + '\'' +
                ", contentKeyword='" + contentKeyword + '\'' +
                '}';
    }
}
