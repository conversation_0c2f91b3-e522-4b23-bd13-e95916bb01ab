package com.tocc.weather.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tocc.common.annotation.Excel;
import java.util.Date;

/**
 * 气象预警通知记录对象 weather_warning_notification
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningNotification
{
    private static final long serialVersionUID = 1L;

    /** 预警ID */
    private String warningId;

    /** 联系人用户ID */
    private Long contactUserId;

    /** 联系人所属单位 */
    @Excel(name = "联系人单位")
    private String contactUnitName;

    /** 联系人所属部门 */
    @Excel(name = "联系人部门")
    private String contactDeptName;

    /** 联系人岗位 */
    @Excel(name = "联系人岗位")
    private String contactPostName;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    private String contactUserName;

    /** 联系人电话 */
    @Excel(name = "联系人电话")
    private String contactPhone;

    /** 联系人邮箱 */
    private String contactEmail;

    /** 通知时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "通知时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date notificationTime;

    /** 确认状态（0-未确认 1-已确认） */
    @Excel(name = "确认状态", readConverterExp = "0=未确认,1=已确认")
    private String confirmStatus;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;

    /** 确认人ID */
    private Long confirmUserId;

    /** 确认人姓名 */
    private String confirmUserName;

    /** 超时时间（分钟） */
    private Integer timeoutMinutes;

    /** 是否超时（0-否 1-是） */
    @Excel(name = "是否超时", readConverterExp = "0=否,1=是")
    private String isTimeout;

    /** 关联的告警ID */
    private String alarmId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 关联查询字段（不存数据库） */
    private WeatherWarning warning; // 关联的预警信息

    public void setWarningId(String warningId) 
    {
        this.warningId = warningId;
    }

    public String getWarningId() 
    {
        return warningId;
    }

    public void setContactUserId(Long contactUserId) 
    {
        this.contactUserId = contactUserId;
    }

    public Long getContactUserId() 
    {
        return contactUserId;
    }

    public void setContactUnitName(String contactUnitName) 
    {
        this.contactUnitName = contactUnitName;
    }

    public String getContactUnitName() 
    {
        return contactUnitName;
    }

    public void setContactDeptName(String contactDeptName) 
    {
        this.contactDeptName = contactDeptName;
    }

    public String getContactDeptName() 
    {
        return contactDeptName;
    }

    public void setContactPostName(String contactPostName) 
    {
        this.contactPostName = contactPostName;
    }

    public String getContactPostName() 
    {
        return contactPostName;
    }

    public void setContactUserName(String contactUserName) 
    {
        this.contactUserName = contactUserName;
    }

    public String getContactUserName() 
    {
        return contactUserName;
    }

    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }

    public void setContactEmail(String contactEmail) 
    {
        this.contactEmail = contactEmail;
    }

    public String getContactEmail() 
    {
        return contactEmail;
    }

    public void setNotificationTime(Date notificationTime) 
    {
        this.notificationTime = notificationTime;
    }

    public Date getNotificationTime() 
    {
        return notificationTime;
    }

    public void setConfirmStatus(String confirmStatus) 
    {
        this.confirmStatus = confirmStatus;
    }

    public String getConfirmStatus() 
    {
        return confirmStatus;
    }

    public void setConfirmTime(Date confirmTime) 
    {
        this.confirmTime = confirmTime;
    }

    public Date getConfirmTime() 
    {
        return confirmTime;
    }

    public void setConfirmUserId(Long confirmUserId) 
    {
        this.confirmUserId = confirmUserId;
    }

    public Long getConfirmUserId() 
    {
        return confirmUserId;
    }

    public void setConfirmUserName(String confirmUserName) 
    {
        this.confirmUserName = confirmUserName;
    }

    public String getConfirmUserName() 
    {
        return confirmUserName;
    }

    public void setTimeoutMinutes(Integer timeoutMinutes) 
    {
        this.timeoutMinutes = timeoutMinutes;
    }

    public Integer getTimeoutMinutes() 
    {
        return timeoutMinutes;
    }

    public void setIsTimeout(String isTimeout) 
    {
        this.isTimeout = isTimeout;
    }

    public String getIsTimeout() 
    {
        return isTimeout;
    }

    public void setAlarmId(String alarmId) 
    {
        this.alarmId = alarmId;
    }

    public String getAlarmId() 
    {
        return alarmId;
    }

    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    public Date getCreateTime() 
    {
        return createTime;
    }

    public void setUpdateTime(Date updateTime) 
    {
        this.updateTime = updateTime;
    }

    public Date getUpdateTime() 
    {
        return updateTime;
    }

    public WeatherWarning getWarning() 
    {
        return warning;
    }

    public void setWarning(WeatherWarning warning) 
    {
        this.warning = warning;
    }

    @Override
    public String toString() {
        return "WeatherWarningNotification{" +
                "warningId='" + warningId + '\'' +
                ", contactUserId=" + contactUserId +
                ", contactUnitName='" + contactUnitName + '\'' +
                ", contactDeptName='" + contactDeptName + '\'' +
                ", contactPostName='" + contactPostName + '\'' +
                ", contactUserName='" + contactUserName + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", notificationTime=" + notificationTime +
                ", confirmStatus='" + confirmStatus + '\'' +
                ", confirmTime=" + confirmTime +
                ", timeoutMinutes=" + timeoutMinutes +
                ", isTimeout='" + isTimeout + '\'' +
                '}';
    }
}
