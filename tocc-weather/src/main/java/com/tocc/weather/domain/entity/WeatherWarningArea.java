package com.tocc.weather.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 气象预警影响区域对象 weather_warning_area
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningArea
{
    private static final long serialVersionUID = 1L;

    /** 预警ID */
    private String warningId;

    /** 行政区划ID */
    private String regionId;

    /** 行政区划名称 */
    private String regionName;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 关联查询时的扩展字段（不存数据库） */
    private String regionFullPath; // 完整路径，如：广西壮族自治区/南宁市/青秀区

    public void setWarningId(String warningId) 
    {
        this.warningId = warningId;
    }

    public String getWarningId() 
    {
        return warningId;
    }

    public void setRegionId(String regionId) 
    {
        this.regionId = regionId;
    }

    public String getRegionId() 
    {
        return regionId;
    }

    public void setRegionName(String regionName) 
    {
        this.regionName = regionName;
    }

    public String getRegionName() 
    {
        return regionName;
    }

    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    public Date getCreateTime() 
    {
        return createTime;
    }

    public String getRegionFullPath() 
    {
        return regionFullPath;
    }

    public void setRegionFullPath(String regionFullPath) 
    {
        this.regionFullPath = regionFullPath;
    }

    @Override
    public String toString() {
        return "WeatherWarningArea{" +
                "warningId='" + warningId + '\'' +
                ", regionId='" + regionId + '\'' +
                ", regionName='" + regionName + '\'' +
                ", createTime=" + createTime +
                ", regionFullPath='" + regionFullPath + '\'' +
                '}';
    }
}
