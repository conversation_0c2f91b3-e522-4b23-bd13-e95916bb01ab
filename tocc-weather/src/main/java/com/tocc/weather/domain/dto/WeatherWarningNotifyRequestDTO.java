package com.tocc.weather.domain.dto;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 气象预警通知请求DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class WeatherWarningNotifyRequestDTO 
{
    /** 通知对象列表 */
    @NotEmpty(message = "请选择通知对象")
    @Valid
    private List<WeatherWarningNotifyTargetDTO> notifyTargets;

    public List<WeatherWarningNotifyTargetDTO> getNotifyTargets() 
    {
        return notifyTargets;
    }

    public void setNotifyTargets(List<WeatherWarningNotifyTargetDTO> notifyTargets) 
    {
        this.notifyTargets = notifyTargets;
    }

    @Override
    public String toString() {
        return "WeatherWarningNotifyRequestDTO{" +
                "notifyTargets=" + notifyTargets +
                '}';
    }
}
