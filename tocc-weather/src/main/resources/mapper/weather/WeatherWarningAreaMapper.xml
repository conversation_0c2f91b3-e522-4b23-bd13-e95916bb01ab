<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.weather.mapper.WeatherWarningAreaMapper">
    
    <resultMap type="WeatherWarningArea" id="WeatherWarningAreaResult">
        <result property="warningId"        column="warning_id"        />
        <result property="regionId"         column="region_id"         />
        <result property="regionName"       column="region_name"       />
        <result property="createTime"       column="create_time"       />
        <result property="regionFullPath"   column="region_full_path"  />
    </resultMap>

    <sql id="selectWeatherWarningAreaVo">
        select warning_id, region_id, region_name, create_time 
        from weather_warning_area
    </sql>

    <select id="selectWeatherWarningAreaList" parameterType="String" resultMap="WeatherWarningAreaResult">
        <include refid="selectWeatherWarningAreaVo"/>
        where warning_id = #{warningId}
        order by region_name
    </select>
    
    <select id="selectWeatherWarningAreaByIds" resultMap="WeatherWarningAreaResult">
        <include refid="selectWeatherWarningAreaVo"/>
        where warning_id = #{warningId} and region_id = #{regionId}
    </select>

    <select id="selectWarningIdsByRegionId" parameterType="String" resultType="String">
        select warning_id from weather_warning_area where region_id = #{regionId}
    </select>

    <select id="selectAreaDescByWarningId" parameterType="String" resultType="String">
        select group_concat(region_name separator '、') as area_desc
        from weather_warning_area 
        where warning_id = #{warningId}
        order by region_name
    </select>
        
    <insert id="insertWeatherWarningArea" parameterType="WeatherWarningArea">
        insert into weather_warning_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="warningId != null">warning_id,</if>
            <if test="regionId != null">region_id,</if>
            <if test="regionName != null">region_name,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="warningId != null">#{warningId},</if>
            <if test="regionId != null">#{regionId},</if>
            <if test="regionName != null">#{regionName},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertWeatherWarningArea" parameterType="list">
        insert into weather_warning_area (warning_id, region_id, region_name, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.warningId}, #{item.regionId}, #{item.regionName}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateWeatherWarningArea" parameterType="WeatherWarningArea">
        update weather_warning_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="regionName != null">region_name = #{regionName},</if>
        </trim>
        where warning_id = #{warningId} and region_id = #{regionId}
    </update>

    <delete id="deleteWeatherWarningAreaByIds">
        delete from weather_warning_area 
        where warning_id = #{warningId} and region_id = #{regionId}
    </delete>

    <delete id="deleteWeatherWarningAreaByWarningId" parameterType="String">
        delete from weather_warning_area where warning_id = #{warningId}
    </delete>

    <delete id="deleteWeatherWarningAreaByWarningIds" parameterType="String">
        delete from weather_warning_area where warning_id in 
        <foreach item="warningId" collection="array" open="(" separator="," close=")">
            #{warningId}
        </foreach>
    </delete>

</mapper>
