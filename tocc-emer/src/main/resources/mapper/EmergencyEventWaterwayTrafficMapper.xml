<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.EmergencyEventWaterwayTrafficMapper">

    <resultMap type="com.tocc.domain.dto.EmergencyEventWaterwayTrafficDTO" id="EmergencyEventWaterwayTrafficResult">
        <result property="id"                     column="id"                      />
        <result property="eventId"                column="event_id"                />
        <result property="waterwayName"           column="waterway_name"           />
        <result property="shipName"               column="ship_name"               />
        <result property="shipType"               column="ship_type"               />
        <result property="shipTonnage"            column="ship_tonnage"            />
        <result property="casualtySituation"      column="casualty_situation"      />
        <result property="cargoInfo"              column="cargo_info"              />
        <result property="environmentalImpact"    column="environmental_impact"    />
        <result property="createrId"              column="creater_id"              />
        <result property="createTime"             column="create_time"             />
        <result property="updaterId"              column="updater_id"              />
        <result property="updateTime"             column="update_time"             />
    </resultMap>

    <sql id="selectEmergencyEventWaterwayTrafficVo">
        select id, event_id, waterway_name, ship_name, ship_type, ship_tonnage, casualty_situation, cargo_info, environmental_impact, creater_id, create_time, updater_id, update_time from emergency_event_waterway_traffic
    </sql>

    <select id="selectEmergencyEventWaterwayTrafficList" parameterType="com.tocc.domain.dto.EmergencyEventWaterwayTrafficDTO" resultMap="EmergencyEventWaterwayTrafficResult">
        <include refid="selectEmergencyEventWaterwayTrafficVo"/>
        <where>  
            <if test="eventId != null  and eventId != ''"> and event_id = #{eventId}</if>
            <if test="waterwayName != null  and waterwayName != ''"> and waterway_name like concat('%', #{waterwayName}, '%')</if>
            <if test="shipName != null  and shipName != ''"> and ship_name like concat('%', #{shipName}, '%')</if>
            <if test="shipType != null  and shipType != ''"> and ship_type = #{shipType}</if>
        </where>
    </select>
    
    <select id="selectEmergencyEventWaterwayTrafficById" parameterType="String" resultMap="EmergencyEventWaterwayTrafficResult">
        <include refid="selectEmergencyEventWaterwayTrafficVo"/>
        where id = #{id}
    </select>

    <select id="selectEmergencyEventWaterwayTrafficByEventId" parameterType="String" resultMap="EmergencyEventWaterwayTrafficResult">
        <include refid="selectEmergencyEventWaterwayTrafficVo"/>
        where event_id = #{eventId}
    </select>
        
    <insert id="insertEmergencyEventWaterwayTraffic" parameterType="com.tocc.domain.dto.EmergencyEventWaterwayTrafficDTO">
        insert into emergency_event_waterway_traffic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="eventId != null and eventId != ''">event_id,</if>
            <if test="waterwayName != null">waterway_name,</if>
            <if test="shipName != null">ship_name,</if>
            <if test="shipType != null">ship_type,</if>
            <if test="shipTonnage != null">ship_tonnage,</if>
            <if test="casualtySituation != null">casualty_situation,</if>
            <if test="cargoInfo != null">cargo_info,</if>
            <if test="environmentalImpact != null">environmental_impact,</if>
            <if test="createrId != null">creater_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updaterId != null">updater_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="eventId != null and eventId != ''">#{eventId},</if>
            <if test="waterwayName != null">#{waterwayName},</if>
            <if test="shipName != null">#{shipName},</if>
            <if test="shipType != null">#{shipType},</if>
            <if test="shipTonnage != null">#{shipTonnage},</if>
            <if test="casualtySituation != null">#{casualtySituation},</if>
            <if test="cargoInfo != null">#{cargoInfo},</if>
            <if test="environmentalImpact != null">#{environmentalImpact},</if>
            <if test="createrId != null">#{createrId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateEmergencyEventWaterwayTraffic" parameterType="com.tocc.domain.dto.EmergencyEventWaterwayTrafficDTO">
        update emergency_event_waterway_traffic
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventId != null and eventId != ''">event_id = #{eventId},</if>
            <if test="waterwayName != null">waterway_name = #{waterwayName},</if>
            <if test="shipName != null">ship_name = #{shipName},</if>
            <if test="shipType != null">ship_type = #{shipType},</if>
            <if test="shipTonnage != null">ship_tonnage = #{shipTonnage},</if>
            <if test="casualtySituation != null">casualty_situation = #{casualtySituation},</if>
            <if test="cargoInfo != null">cargo_info = #{cargoInfo},</if>
            <if test="environmentalImpact != null">environmental_impact = #{environmentalImpact},</if>
            <if test="updaterId != null">updater_id = #{updaterId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmergencyEventWaterwayTrafficById" parameterType="String">
        delete from emergency_event_waterway_traffic where id = #{id}
    </delete>

    <delete id="deleteEmergencyEventWaterwayTrafficByEventId" parameterType="String">
        delete from emergency_event_waterway_traffic where event_id = #{eventId}
    </delete>

    <delete id="deleteEmergencyEventWaterwayTrafficByIds" parameterType="String">
        delete from emergency_event_waterway_traffic where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
