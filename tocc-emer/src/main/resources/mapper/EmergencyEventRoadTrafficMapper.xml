<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.EmergencyEventRoadTrafficMapper">

    <resultMap type="com.tocc.domain.dto.EmergencyEventRoadTrafficDTO" id="EmergencyEventRoadTrafficResult">
        <result property="id"                     column="id"                      />
        <result property="eventId"                column="event_id"                />
        <result property="roadSectionCode"        column="road_section_code"       />
        <result property="startStakeNumber"       column="start_stake_number"      />
        <result property="endStakeNumber"         column="end_stake_number"        />
        <result property="direction"              column="direction"               />
        <result property="trafficAffected"        column="traffic_affected"        />
        <result property="vehicleType"            column="vehicle_type"            />
        <result property="estimatedRecoveryTime"  column="estimated_recovery_time" />
        <result property="casualtySituation"      column="casualty_situation"      />
        <result property="impactTrend"            column="impact_trend"            />
        <result property="createrId"              column="creater_id"              />
        <result property="createTime"             column="create_time"             />
        <result property="updaterId"              column="updater_id"              />
        <result property="updateTime"             column="update_time"             />
    </resultMap>

    <sql id="selectEmergencyEventRoadTrafficVo">
        select id, event_id, road_section_code, start_stake_number, end_stake_number, direction, traffic_affected, vehicle_type, estimated_recovery_time, casualty_situation, impact_trend, creater_id, create_time, updater_id, update_time from emergency_event_road_traffic
    </sql>

    <select id="selectEmergencyEventRoadTrafficList" parameterType="com.tocc.domain.dto.EmergencyEventRoadTrafficDTO" resultMap="EmergencyEventRoadTrafficResult">
        <include refid="selectEmergencyEventRoadTrafficVo"/>
        <where>  
            <if test="eventId != null  and eventId != ''"> and event_id = #{eventId}</if>
            <if test="roadSectionCode != null  and roadSectionCode != ''"> and road_section_code like concat('%', #{roadSectionCode}, '%')</if>
            <if test="direction != null  and direction != ''"> and direction = #{direction}</if>
            <if test="trafficAffected != null  and trafficAffected != ''"> and traffic_affected = #{trafficAffected}</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type like concat('%', #{vehicleType}, '%')</if>
        </where>
    </select>
    
    <select id="selectEmergencyEventRoadTrafficById" parameterType="String" resultMap="EmergencyEventRoadTrafficResult">
        <include refid="selectEmergencyEventRoadTrafficVo"/>
        where id = #{id}
    </select>

    <select id="selectEmergencyEventRoadTrafficByEventId" parameterType="String" resultMap="EmergencyEventRoadTrafficResult">
        <include refid="selectEmergencyEventRoadTrafficVo"/>
        where event_id = #{eventId}
    </select>
        
    <insert id="insertEmergencyEventRoadTraffic" parameterType="com.tocc.domain.dto.EmergencyEventRoadTrafficDTO">
        insert into emergency_event_road_traffic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="eventId != null and eventId != ''">event_id,</if>
            <if test="roadSectionCode != null">road_section_code,</if>
            <if test="startStakeNumber != null">start_stake_number,</if>
            <if test="endStakeNumber != null">end_stake_number,</if>
            <if test="direction != null">direction,</if>
            <if test="trafficAffected != null">traffic_affected,</if>
            <if test="vehicleType != null">vehicle_type,</if>
            <if test="estimatedRecoveryTime != null">estimated_recovery_time,</if>
            <if test="casualtySituation != null">casualty_situation,</if>
            <if test="impactTrend != null">impact_trend,</if>
            <if test="createrId != null">creater_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updaterId != null">updater_id,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="eventId != null and eventId != ''">#{eventId},</if>
            <if test="roadSectionCode != null">#{roadSectionCode},</if>
            <if test="startStakeNumber != null">#{startStakeNumber},</if>
            <if test="endStakeNumber != null">#{endStakeNumber},</if>
            <if test="direction != null">#{direction},</if>
            <if test="trafficAffected != null">#{trafficAffected},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="estimatedRecoveryTime != null">#{estimatedRecoveryTime},</if>
            <if test="casualtySituation != null">#{casualtySituation},</if>
            <if test="impactTrend != null">#{impactTrend},</if>
            <if test="createrId != null">#{createrId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateEmergencyEventRoadTraffic" parameterType="com.tocc.domain.dto.EmergencyEventRoadTrafficDTO">
        update emergency_event_road_traffic
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventId != null and eventId != ''">event_id = #{eventId},</if>
            <if test="roadSectionCode != null">road_section_code = #{roadSectionCode},</if>
            <if test="startStakeNumber != null">start_stake_number = #{startStakeNumber},</if>
            <if test="endStakeNumber != null">end_stake_number = #{endStakeNumber},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="trafficAffected != null">traffic_affected = #{trafficAffected},</if>
            <if test="vehicleType != null">vehicle_type = #{vehicleType},</if>
            <if test="estimatedRecoveryTime != null">estimated_recovery_time = #{estimatedRecoveryTime},</if>
            <if test="casualtySituation != null">casualty_situation = #{casualtySituation},</if>
            <if test="impactTrend != null">impact_trend = #{impactTrend},</if>
            <if test="updaterId != null">updater_id = #{updaterId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmergencyEventRoadTrafficById" parameterType="String">
        delete from emergency_event_road_traffic where id = #{id}
    </delete>

    <delete id="deleteEmergencyEventRoadTrafficByEventId" parameterType="String">
        delete from emergency_event_road_traffic where event_id = #{eventId}
    </delete>

    <delete id="deleteEmergencyEventRoadTrafficByIds" parameterType="String">
        delete from emergency_event_road_traffic where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
