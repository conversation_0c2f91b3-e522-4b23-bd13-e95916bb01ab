<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.EmergencyEventMapper">

    <resultMap type="com.tocc.domain.dto.EmergencyEventDTO" id="EmergencyEventResult">
        <result property="eventId"                column="event_id"                />
        <result property="eventTitle"             column="event_title"             />
        <result property="eventType"              column="event_type"              />
        <result property="accidentType"           column="accident_type"           />
        <result property="occurTime"              column="occur_time"              />
        <result property="administrativeArea"     column="administrative_area"     />
        <result property="administrativeAreaId"   column="administrative_area_id"  />
        <result property="eventLevel"             column="event_level"             />
        <result property="reporterId"             column="reporter_id"             />
        <result property="submitterId"            column="submitter_id"            />
        <result property="roadManagerUnitId"      column="road_manager_unit_id"    />
        <result property="roadManagerLeaderId"    column="road_manager_leader_id"  />
        <result property="detailedAddress"        column="detailed_address"        />
        <result property="longitude"              column="longitude"               />
        <result property="latitude"               column="latitude"                />
        <result property="impactScope"            column="impact_scope"            />
        <result property="eventDescription"       column="event_description"       />
        <result property="eventCause"             column="event_cause"             />
        <result property="emergencyMeasures"      column="emergency_measures"      />
        <result property="emergencyForces"        column="emergency_forces"        />
        <result property="supportNeeded"          column="support_needed"          />
        <result property="status"                 column="status"                  />
        <result property="createrId"              column="creater_id"              />
        <result property="createTime"             column="create_time"             />
        <result property="updaterId"              column="updater_id"              />
        <result property="updateTime"             column="update_time"             />
        <result property="remark"                 column="remark"                  />
        <result property="planLevelJudgment"      column="plan_level_judgment"     />
        <result property="decisionSupport"        column="decision_support"        />
        <result property="emerPlanId"             column="emer_plan_id"            />
    </resultMap>

    <sql id="selectEmergencyEventVo">
        select event_id, event_title, event_type, accident_type, occur_time, administrative_area, administrative_area_id, event_level, reporter_id, submitter_id, road_manager_unit_id, road_manager_leader_id, detailed_address, longitude, latitude, impact_scope, event_description, event_cause, emergency_measures, emergency_forces, support_needed, status, creater_id, create_time, updater_id, update_time, remark, plan_level_judgment, decision_support, emer_plan_id from emergency_event
    </sql>

    <select id="selectEmergencyEventList" parameterType="com.tocc.domain.dto.EmergencyEventDTO" resultMap="EmergencyEventResult">
        <include refid="selectEmergencyEventVo"/>
        <where>
            <if test="eventTitle != null  and eventTitle != ''"> and event_title like concat('%', #{eventTitle}, '%')</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
            <if test="accidentType != null  and accidentType != ''"> and accident_type = #{accidentType}</if>
            <if test="occurTime != null "> and occur_time = #{occurTime}</if>
            <if test="administrativeArea != null  and administrativeArea != ''"> and administrative_area like concat('%', #{administrativeArea}, '%')</if>
            <if test="administrativeAreaId != null  and administrativeAreaId != ''"> and administrative_area_id = #{administrativeAreaId}</if>
            <if test="eventLevel != null  and eventLevel != ''"> and event_level = #{eventLevel}</if>
            <if test="reporterId != null  and reporterId != ''"> and reporter_id = #{reporterId}</if>
            <if test="submitterId != null  and submitterId != ''"> and submitter_id = #{submitterId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <resultMap type="com.tocc.domain.vo.EmergencyEventVO" id="EmergencyEventVOResult">
        <result property="eventId"                column="event_id"                />
        <result property="eventTitle"             column="event_title"             />
        <result property="eventType"              column="event_type"              />
        <result property="accidentType"           column="accident_type"           />
        <result property="occurTime"              column="occur_time"              />
        <result property="administrativeArea"     column="administrative_area"     />
        <result property="administrativeAreaId"   column="administrative_area_id"  />
        <result property="eventLevel"             column="event_level"             />
        <result property="reporterId"             column="reporter_id"             />
        <result property="submitterId"            column="submitter_id"            />
        <result property="roadManagerUnitId"      column="road_manager_unit_id"    />
        <result property="roadManagerLeaderId"    column="road_manager_leader_id"  />
        <result property="detailedAddress"        column="detailed_address"        />
        <result property="longitude"              column="longitude"               />
        <result property="latitude"               column="latitude"                />
        <result property="impactScope"            column="impact_scope"            />
        <result property="eventDescription"       column="event_description"       />
        <result property="eventCause"             column="event_cause"             />
        <result property="emergencyMeasures"      column="emergency_measures"      />
        <result property="emergencyForces"        column="emergency_forces"        />
        <result property="supportNeeded"          column="support_needed"          />
        <result property="status"                 column="status"                  />
        <result property="createrId"              column="creater_id"              />
        <result property="createTime"             column="create_time"             />
        <result property="updaterId"              column="updater_id"              />
        <result property="updateTime"             column="update_time"             />
        <result property="remark"                 column="remark"                  />
        <result property="planLevelJudgment"      column="plan_level_judgment"     />
        <result property="decisionSupport"        column="decision_support"        />
        <result property="emerPlanId"             column="emer_plan_id"            />
        <!-- 填报人信息 -->
        <result property="reporterName"           column="reporter_name"           />
        <result property="reporterDeptName"       column="reporter_dept_name"      />
        <result property="reporterPhone"          column="reporter_phone"          />
        <!-- 上报人信息 -->
        <result property="submitterName"          column="submitter_name"          />
        <result property="submitterDeptName"      column="submitter_dept_name"     />
        <result property="submitterPhone"         column="submitter_phone"         />
        <!-- 路段管辖单位负责人信息 -->
        <result property="roadManagerLeaderName"     column="road_manager_leader_name"      />
        <result property="roadManagerLeaderDeptName" column="road_manager_leader_dept_name" />
        <result property="roadManagerLeaderPhone"    column="road_manager_leader_phone"     />
    </resultMap>

    <select id="selectEmergencyEventListByUser" parameterType="com.tocc.domain.dto.EmergencyEventDTO" resultMap="EmergencyEventVOResult">
        select
            e.event_id, e.event_title, e.event_type, e.accident_type, e.occur_time,
            e.administrative_area, e.administrative_area_id, e.event_level,
            e.reporter_id, e.submitter_id, e.road_manager_unit_id, e.road_manager_leader_id,
            e.detailed_address, e.longitude, e.latitude, e.impact_scope,
            e.event_description, e.event_cause, e.emergency_measures, e.emergency_forces,
            e.support_needed, e.status, e.creater_id, e.create_time, e.updater_id, e.update_time,
            e.remark, e.plan_level_judgment, e.decision_support, e.emer_plan_id,
            <!-- 填报人信息 -->
            reporter.nick_name as reporter_name,
            reporter_dept.dept_name as reporter_dept_name,
            reporter.phonenumber as reporter_phone,
            <!-- 上报人信息 -->
            submitter.nick_name as submitter_name,
            submitter_dept.dept_name as submitter_dept_name,
            submitter.phonenumber as submitter_phone,
            <!-- 路段管辖单位负责人信息 -->
            road_leader.nick_name as road_manager_leader_name,
            road_leader_dept.dept_name as road_manager_leader_dept_name,
            road_leader.phonenumber as road_manager_leader_phone
        from emergency_event e
        <!-- 关联填报人信息 -->
        left join sys_user reporter on e.reporter_id = reporter.user_id
        left join sys_dept reporter_dept on reporter.dept_id = reporter_dept.dept_id
        <!-- 关联上报人信息 -->
        left join sys_user submitter on e.submitter_id = submitter.user_id
        left join sys_dept submitter_dept on submitter.dept_id = submitter_dept.dept_id
        <!-- 关联路段管辖单位负责人信息 -->
        left join sys_user road_leader on e.road_manager_leader_id = road_leader.user_id
        left join sys_dept road_leader_dept on road_leader.dept_id = road_leader_dept.dept_id
        <where>
            <!-- 只查询当前用户作为上报人或填报人的事件 -->
            <if test="reporterId != null and reporterId != ''">
                and (e.reporter_id = #{reporterId} or e.submitter_id = #{reporterId})
            </if>
            <!-- 其他查询条件 -->
            <if test="eventTitle != null  and eventTitle != ''"> and e.event_title like concat('%', #{eventTitle}, '%')</if>
            <if test="eventType != null  and eventType != ''"> and e.event_type = #{eventType}</if>
            <if test="accidentType != null  and accidentType != ''"> and e.accident_type = #{accidentType}</if>
            <if test="occurTime != null "> and e.occur_time = #{occurTime}</if>
            <if test="administrativeArea != null  and administrativeArea != ''"> and e.administrative_area like concat('%', #{administrativeArea}, '%')</if>
            <if test="administrativeAreaId != null  and administrativeAreaId != ''"> and e.administrative_area_id = #{administrativeAreaId}</if>
            <if test="eventLevel != null  and eventLevel != ''"> and e.event_level = #{eventLevel}</if>
            <if test="status != null  and status != ''"> and e.status = #{status}</if>
        </where>
        order by e.create_time desc
    </select>
    
    <select id="selectEmergencyEventByEventId" parameterType="String" resultMap="EmergencyEventResult">
        <include refid="selectEmergencyEventVo"/>
        where event_id = #{eventId}
    </select>

    <select id="selectEventsByTypeAndTime" resultMap="EmergencyEventResult">
        <include refid="selectEmergencyEventVo"/>
        <where>
            <if test="eventType != null and eventType != ''">
                and event_type = #{eventType}
            </if>
            <if test="startTime != null">
                and occur_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and occur_time &lt;= #{endTime}
            </if>
        </where>
        order by occur_time desc
    </select>

    <select id="selectEventsByStatus" parameterType="String" resultMap="EmergencyEventResult">
        <include refid="selectEmergencyEventVo"/>
        where status = #{status}
        order by create_time desc
    </select>

    <select id="countEmergencyEvent" parameterType="com.tocc.domain.dto.EmergencyEventDTO" resultType="int">
        select count(*) from emergency_event
        <where>  
            <if test="eventTitle != null  and eventTitle != ''"> and event_title like concat('%', #{eventTitle}, '%')</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
            <if test="accidentType != null  and accidentType != ''"> and accident_type = #{accidentType}</if>
            <if test="occurTime != null "> and occur_time = #{occurTime}</if>
            <if test="administrativeArea != null  and administrativeArea != ''"> and administrative_area like concat('%', #{administrativeArea}, '%')</if>
            <if test="administrativeAreaId != null  and administrativeAreaId != ''"> and administrative_area_id = #{administrativeAreaId}</if>
            <if test="eventLevel != null  and eventLevel != ''"> and event_level = #{eventLevel}</if>
            <if test="reporterId != null  and reporterId != ''"> and reporter_id = #{reporterId}</if>
            <if test="submitterId != null  and submitterId != ''"> and submitter_id = #{submitterId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
        
    <insert id="insertEmergencyEvent" parameterType="com.tocc.domain.dto.EmergencyEventDTO">
        insert into emergency_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventId != null">event_id,</if>
            <if test="eventTitle != null and eventTitle != ''">event_title,</if>
            <if test="eventType != null and eventType != ''">event_type,</if>
            <if test="accidentType != null and accidentType != ''">accident_type,</if>
            <if test="occurTime != null">occur_time,</if>
            <if test="administrativeArea != null">administrative_area,</if>
            <if test="administrativeAreaId != null">administrative_area_id,</if>
            <if test="eventLevel != null">event_level,</if>
            <if test="reporterId != null">reporter_id,</if>
            <if test="submitterId != null">submitter_id,</if>
            <if test="roadManagerUnitId != null">road_manager_unit_id,</if>
            <if test="roadManagerLeaderId != null">road_manager_leader_id,</if>
            <if test="detailedAddress != null">detailed_address,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="impactScope != null">impact_scope,</if>
            <if test="eventDescription != null">event_description,</if>
            <if test="eventCause != null">event_cause,</if>
            <if test="emergencyMeasures != null">emergency_measures,</if>
            <if test="emergencyForces != null">emergency_forces,</if>
            <if test="supportNeeded != null">support_needed,</if>
            <if test="status != null">status,</if>
            <if test="createrId != null">creater_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updaterId != null">updater_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="planLevelJudgment != null">plan_level_judgment,</if>
            <if test="decisionSupport != null">decision_support,</if>
            <if test="emerPlanId != null">emer_plan_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventId != null">#{eventId},</if>
            <if test="eventTitle != null and eventTitle != ''">#{eventTitle},</if>
            <if test="eventType != null and eventType != ''">#{eventType},</if>
            <if test="accidentType != null and accidentType != ''">#{accidentType},</if>
            <if test="occurTime != null">#{occurTime},</if>
            <if test="administrativeArea != null">#{administrativeArea},</if>
            <if test="administrativeAreaId != null">#{administrativeAreaId},</if>
            <if test="eventLevel != null">#{eventLevel},</if>
            <if test="reporterId != null">#{reporterId},</if>
            <if test="submitterId != null">#{submitterId},</if>
            <if test="roadManagerUnitId != null">#{roadManagerUnitId},</if>
            <if test="roadManagerLeaderId != null">#{roadManagerLeaderId},</if>
            <if test="detailedAddress != null">#{detailedAddress},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="impactScope != null">#{impactScope},</if>
            <if test="eventDescription != null">#{eventDescription},</if>
            <if test="eventCause != null">#{eventCause},</if>
            <if test="emergencyMeasures != null">#{emergencyMeasures},</if>
            <if test="emergencyForces != null">#{emergencyForces},</if>
            <if test="supportNeeded != null">#{supportNeeded},</if>
            <if test="status != null">#{status},</if>
            <if test="createrId != null">#{createrId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updaterId != null">#{updaterId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="planLevelJudgment != null">#{planLevelJudgment},</if>
            <if test="decisionSupport != null">#{decisionSupport},</if>
            <if test="emerPlanId != null">#{emerPlanId},</if>
        </trim>
    </insert>

    <update id="updateEmergencyEvent" parameterType="com.tocc.domain.dto.EmergencyEventDTO">
        update emergency_event
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventTitle != null and eventTitle != ''">event_title = #{eventTitle},</if>
            <if test="eventType != null and eventType != ''">event_type = #{eventType},</if>
            <if test="accidentType != null and accidentType != ''">accident_type = #{accidentType},</if>
            <if test="occurTime != null">occur_time = #{occurTime},</if>
            <if test="administrativeArea != null">administrative_area = #{administrativeArea},</if>
            <if test="administrativeAreaId != null">administrative_area_id = #{administrativeAreaId},</if>
            <if test="eventLevel != null">event_level = #{eventLevel},</if>
            <if test="reporterId != null">reporter_id = #{reporterId},</if>
            <if test="submitterId != null">submitter_id = #{submitterId},</if>
            <if test="roadManagerUnitId != null">road_manager_unit_id = #{roadManagerUnitId},</if>
            <if test="roadManagerLeaderId != null">road_manager_leader_id = #{roadManagerLeaderId},</if>
            <if test="detailedAddress != null">detailed_address = #{detailedAddress},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="impactScope != null">impact_scope = #{impactScope},</if>
            <if test="eventDescription != null">event_description = #{eventDescription},</if>
            <if test="eventCause != null">event_cause = #{eventCause},</if>
            <if test="emergencyMeasures != null">emergency_measures = #{emergencyMeasures},</if>
            <if test="emergencyForces != null">emergency_forces = #{emergencyForces},</if>
            <if test="supportNeeded != null">support_needed = #{supportNeeded},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updaterId != null">updater_id = #{updaterId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="planLevelJudgment != null">plan_level_judgment = #{planLevelJudgment},</if>
            <if test="decisionSupport != null">decision_support = #{decisionSupport},</if>
            <if test="emerPlanId != null">emer_plan_id = #{emerPlanId},</if>
        </trim>
        where event_id = #{eventId}
    </update>

    <update id="updateEventStatus">
        update emergency_event 
        set status = #{status}, updater_id = #{updaterId}, update_time = #{updateTime}
        where event_id = #{eventId}
    </update>

    <delete id="deleteEmergencyEventByEventId" parameterType="String">
        delete from emergency_event where event_id = #{eventId}
    </delete>

    <delete id="deleteEmergencyEventByEventIds" parameterType="String">
        delete from emergency_event where event_id in 
        <foreach item="eventId" collection="array" open="(" separator="," close=")">
            #{eventId}
        </foreach>
    </delete>

</mapper>
