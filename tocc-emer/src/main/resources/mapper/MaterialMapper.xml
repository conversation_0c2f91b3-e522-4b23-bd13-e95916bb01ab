<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.MaterialMapper">

    <resultMap type="com.tocc.domain.vo.MaterialVO" id="MaterialResult">
        <result property="id"                column="id"                />
        <result property="materialName"      column="material_name"     />
        <result property="materialType"      column="material_type"     />
        <result property="materialTypeName"  column="material_type_name" />
        <result property="specModel"         column="spec_model"        />
        <result property="warehouseId"       column="warehouse_id"      />
        <result property="warehouseName"     column="warehouse_name"    />
        <result property="quantity"          column="quantity"          />
        <result property="unit"              column="unit"              />
        <result property="status"            column="status"            />
        <result property="statusName"        column="status_name"       />
        <result property="expiryDate"        column="expiry_date"       />
        <result property="remark"            column="remark"            />
        <result property="teamId"            column="team_id"           />
        <result property="teamName"          column="team_name"         />
        <result property="createTime"        column="create_time"       />
        <result property="creator"           column="creator"           />
        <result property="updateTime"        column="update_time"       />
        <result property="updater"           column="updater"           />
    </resultMap>

    <sql id="selectMaterialVo">
        select
            m.id, m.material_name, m.material_type, m.spec_model,
            m.warehouse_id, m.quantity, m.unit, m.status, m.expiry_date, m.remark,
            m.team_id, m.create_time, m.creator, m.update_time, m.updater,
            -- 字典值转换
            case m.material_type
                when '0' then '应急物资'
                when '1' then '应急装备'
                else '未知'
            end as material_type_name,
            case m.status
                when 1 then '正常'
                when 2 then '待检修'
                when 3 then '报废'
                else '未知'
            end as status_name,
            -- 关联仓库信息
            w.warehouse_name,
            -- 关联救援队伍信息
            rt.team_name
        from em_material m
        left join em_warehouse w on m.warehouse_id = w.id and w.del_flag = 0
        left join rescue_team rt on m.team_id = rt.id and rt.del_flag = 0
    </sql>

    <select id="selectMaterialList" parameterType="com.tocc.domain.dto.MaterialDTO" resultMap="MaterialResult">
        <include refid="selectMaterialVo"/>
        <where>
            m.del_flag = 0
            <if test="materialName != null and materialName != ''">
                AND m.material_name like concat('%', #{materialName}, '%')
            </if>
            <if test="materialType != null and materialType != ''">
                AND m.material_type = #{materialType}
            </if>
            <if test="warehouseId != null and warehouseId != ''">
                AND m.warehouse_id = #{warehouseId}
            </if>
            <if test="teamId != null and teamId != ''">
                AND m.team_id = #{teamId}
            </if>
        </where>
        order by m.material_type, m.create_time desc
    </select>

    <insert id="insertMaterial" parameterType="com.tocc.domain.dto.MaterialDTO">
        insert into em_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="materialName != null">material_name,</if>
            <if test="materialType != null">material_type,</if>
            <if test="specModel != null">spec_model,</if>
            <if test="warehouseId != null">warehouse_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="unit != null">unit,</if>
            <if test="status != null">status,</if>
            <if test="expiryDate != null">expiry_date,</if>
            <if test="remark != null">remark,</if>
            <if test="teamId != null">team_id,</if>
            <if test="creator != null">creator,</if>
            <if test="delFlag != null">del_flag,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="materialType != null">#{materialType},</if>
            <if test="specModel != null">#{specModel},</if>
            <if test="warehouseId != null">#{warehouseId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="unit != null">#{unit},</if>
            <if test="status != null">#{status},</if>
            <if test="expiryDate != null">#{expiryDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="creator != null">#{creator},</if>
            <if test="delFlag != null">#{delFlag},</if>
            now(),
         </trim>
    </insert>

    <update id="updateMaterial" parameterType="com.tocc.domain.dto.MaterialDTO">
        update em_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialName != null">material_name = #{materialName},</if>
            <if test="materialType != null">material_type = #{materialType},</if>
            <if test="specModel != null">spec_model = #{specModel},</if>
            <if test="warehouseId != null">warehouse_id = #{warehouseId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="status != null">status = #{status},</if>
            <if test="expiryDate != null">expiry_date = #{expiryDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="updater != null">updater = #{updater},</if>
            update_time = now(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialByIds" parameterType="String">
        update em_material set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteMaterialById" parameterType="String">
        update em_material set del_flag = 1 where id = #{id}
    </delete>

</mapper>
