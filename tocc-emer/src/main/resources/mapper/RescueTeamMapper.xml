<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.RescueTeamMapper">
    
    <resultMap type="com.tocc.domain.vo.RescueTeamVO" id="RescueTeamResult">
        <result property="id"                column="id"                />
        <result property="teamName"          column="team_name"         />
        <result property="teamCode"          column="team_code"         />
        <result property="address"           column="address"           />
        <result property="longitude"         column="longitude"         />
        <result property="latitude"          column="latitude"          />
        <result property="teamSize"          column="team_size"         />
        <result property="leaderName"        column="leader_name"       />
        <result property="leaderPhone"       column="leader_phone"      />
        <result property="jurisdictionUnit"  column="jurisdiction_unit" />
        <result property="jurisdictionLeader" column="jurisdiction_leader" />
        <result property="jurisdictionPhone" column="jurisdiction_phone" />
        <result property="teamType"          column="team_type"         />
        <result property="teamTypeName"      column="team_type_name"    />
        <result property="specialties"       column="specialties"       />
        <result property="status"            column="status"            />
        <result property="statusName"        column="status_name"       />
        <result property="remark"            column="remark"            />
        <result property="createTime"        column="create_time"       />
        <result property="creator"           column="creator"           />
        <result property="updateTime"        column="update_time"       />
        <result property="updater"           column="updater"           />
    </resultMap>

    <sql id="selectRescueTeamVo">
        select 
            rt.id, rt.team_name, rt.team_code, rt.address, rt.longitude, rt.latitude,
            rt.team_size, rt.leader_name, rt.leader_phone, rt.jurisdiction_unit,
            rt.jurisdiction_leader, rt.jurisdiction_phone, rt.team_type, rt.specialties,
            rt.status, rt.remark, rt.create_time, rt.creator, rt.update_time, rt.updater,
            -- 字典值转换
            dt1.dict_label as team_type_name,
            case rt.status 
                when 1 then '正常'
                when 2 then '停用'
                else '未知'
            end as status_name
        from rescue_team rt
        left join sys_dict_data dt1 on dt1.dict_value = rt.team_type and dt1.dict_type = 'rescue_team_type'
    </sql>

    <select id="selectRescueTeamList" parameterType="com.tocc.domain.dto.RescueTeamDTO" resultMap="RescueTeamResult">
        <include refid="selectRescueTeamVo"/>
        <where>
            rt.del_flag = 0
            <if test="teamName != null and teamName != ''"> and rt.team_name like concat('%', #{teamName}, '%')</if>
            <if test="teamCode != null and teamCode != ''"> and rt.team_code = #{teamCode}</if>
            <if test="teamType != null and teamType != ''"> and rt.team_type = #{teamType}</if>
            <if test="jurisdictionUnit != null and jurisdictionUnit != ''"> and rt.jurisdiction_unit like concat('%', #{jurisdictionUnit}, '%')</if>
            <if test="status != null"> and rt.status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(rt.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(rt.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by rt.create_time desc
    </select>
    
    <select id="selectRescueTeamById" parameterType="String" resultMap="RescueTeamResult">
        <include refid="selectRescueTeamVo"/>
        where rt.id = #{id} and rt.del_flag = 0
    </select>

    <select id="checkTeamCodeUnique" resultType="int">
        select count(1) from rescue_team 
        where team_code = #{teamCode} and del_flag = 0
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>
        
    <insert id="insertRescueTeam" parameterType="com.tocc.domain.dto.RescueTeamDTO">
        insert into rescue_team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="teamName != null">team_name,</if>
            <if test="teamCode != null">team_code,</if>
            <if test="address != null">address,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="teamSize != null">team_size,</if>
            <if test="leaderName != null">leader_name,</if>
            <if test="leaderPhone != null">leader_phone,</if>
            <if test="jurisdictionUnit != null">jurisdiction_unit,</if>
            <if test="jurisdictionLeader != null">jurisdiction_leader,</if>
            <if test="jurisdictionPhone != null">jurisdiction_phone,</if>
            <if test="teamType != null">team_type,</if>
            <if test="specialties != null">specialties,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="creator != null">creator,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="teamName != null">#{teamName},</if>
            <if test="teamCode != null">#{teamCode},</if>
            <if test="address != null">#{address},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="teamSize != null">#{teamSize},</if>
            <if test="leaderName != null">#{leaderName},</if>
            <if test="leaderPhone != null">#{leaderPhone},</if>
            <if test="jurisdictionUnit != null">#{jurisdictionUnit},</if>
            <if test="jurisdictionLeader != null">#{jurisdictionLeader},</if>
            <if test="jurisdictionPhone != null">#{jurisdictionPhone},</if>
            <if test="teamType != null">#{teamType},</if>
            <if test="specialties != null">#{specialties},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="creator != null">#{creator},</if>
            now(),
         </trim>
    </insert>

    <update id="updateRescueTeam" parameterType="com.tocc.domain.dto.RescueTeamDTO">
        update rescue_team
        <trim prefix="SET" suffixOverrides=",">
            <if test="teamName != null">team_name = #{teamName},</if>
            <if test="teamCode != null">team_code = #{teamCode},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="teamSize != null">team_size = #{teamSize},</if>
            <if test="leaderName != null">leader_name = #{leaderName},</if>
            <if test="leaderPhone != null">leader_phone = #{leaderPhone},</if>
            <if test="jurisdictionUnit != null">jurisdiction_unit = #{jurisdictionUnit},</if>
            <if test="jurisdictionLeader != null">jurisdiction_leader = #{jurisdictionLeader},</if>
            <if test="jurisdictionPhone != null">jurisdiction_phone = #{jurisdictionPhone},</if>
            <if test="teamType != null">team_type = #{teamType},</if>
            <if test="specialties != null">specialties = #{specialties},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updater != null">updater = #{updater},</if>
            update_time = now(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRescueTeamById" parameterType="String">
        update rescue_team set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteRescueTeamByIds" parameterType="String">
        update rescue_team set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTimeoutTeams" resultMap="RescueTeamResult">
        <include refid="selectRescueTeamVo"/>
        where rt.del_flag = 0
          and rt.status = 1
          and rt.update_time &lt; #{timeoutTime}
        order by rt.update_time asc
    </select>
</mapper>
