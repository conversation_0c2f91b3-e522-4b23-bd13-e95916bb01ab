package com.tocc.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 救援队伍VO
 * 
 * <AUTHOR>
 */
public class RescueTeamVO {
    
    /** 主键ID */
    private String id;

    /** 队伍名称 */
    private String teamName;

    /** 队伍编号 */
    private String teamCode;

    /** 队伍地址 */
    private String address;

    /** 经度 */
    private BigDecimal longitude;

    /** 纬度 */
    private BigDecimal latitude;

    /** 队伍人数 */
    private Integer teamSize;

    /** 负责人姓名 */
    private String leaderName;

    /** 负责人联系方式 */
    private String leaderPhone;

    /** 所属管辖单位 */
    private String jurisdictionUnit;

    /** 管辖单位负责人 */
    private String jurisdictionLeader;

    /** 管辖单位负责人联系方式 */
    private String jurisdictionPhone;

    /** 队伍类型 */
    private String teamType;

    /** 队伍类型名称 */
    private String teamTypeName;

    /** 专业特长 */
    private String specialties;

    /** 状态 */
    private Integer status;

    /** 状态名称 */
    private String statusName;

    /** 备注信息 */
    private String remark;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 创建人 */
    private String creator;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 更新人 */
    private String updater;

    /** 物资列表 */
    private List<MaterialVO> materials;

    /** 装备列表 */
    private List<MaterialVO> equipments;

    /** 距离事件点的距离（公里，用于救援圈功能） */
    private Double distance;

    // Getter and Setter methods
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getTeamCode() {
        return teamCode;
    }

    public void setTeamCode(String teamCode) {
        this.teamCode = teamCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public Integer getTeamSize() {
        return teamSize;
    }

    public void setTeamSize(Integer teamSize) {
        this.teamSize = teamSize;
    }

    public String getLeaderName() {
        return leaderName;
    }

    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName;
    }

    public String getLeaderPhone() {
        return leaderPhone;
    }

    public void setLeaderPhone(String leaderPhone) {
        this.leaderPhone = leaderPhone;
    }

    public String getJurisdictionUnit() {
        return jurisdictionUnit;
    }

    public void setJurisdictionUnit(String jurisdictionUnit) {
        this.jurisdictionUnit = jurisdictionUnit;
    }

    public String getJurisdictionLeader() {
        return jurisdictionLeader;
    }

    public void setJurisdictionLeader(String jurisdictionLeader) {
        this.jurisdictionLeader = jurisdictionLeader;
    }

    public String getJurisdictionPhone() {
        return jurisdictionPhone;
    }

    public void setJurisdictionPhone(String jurisdictionPhone) {
        this.jurisdictionPhone = jurisdictionPhone;
    }

    public String getTeamType() {
        return teamType;
    }

    public void setTeamType(String teamType) {
        this.teamType = teamType;
    }

    public String getTeamTypeName() {
        return teamTypeName;
    }

    public void setTeamTypeName(String teamTypeName) {
        this.teamTypeName = teamTypeName;
    }

    public String getSpecialties() {
        return specialties;
    }

    public void setSpecialties(String specialties) {
        this.specialties = specialties;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public List<MaterialVO> getMaterials() {
        return materials;
    }

    public void setMaterials(List<MaterialVO> materials) {
        this.materials = materials;
    }

    public List<MaterialVO> getEquipments() {
        return equipments;
    }

    public void setEquipments(List<MaterialVO> equipments) {
        this.equipments = equipments;
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    @Override
    public String toString() {
        return "RescueTeamVO{" +
                "id='" + id + '\'' +
                ", teamName='" + teamName + '\'' +
                ", teamCode='" + teamCode + '\'' +
                ", address='" + address + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", teamSize=" + teamSize +
                ", leaderName='" + leaderName + '\'' +
                ", leaderPhone='" + leaderPhone + '\'' +
                ", jurisdictionUnit='" + jurisdictionUnit + '\'' +
                ", jurisdictionLeader='" + jurisdictionLeader + '\'' +
                ", jurisdictionPhone='" + jurisdictionPhone + '\'' +
                ", teamType='" + teamType + '\'' +
                ", teamTypeName='" + teamTypeName + '\'' +
                ", specialties='" + specialties + '\'' +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", remark='" + remark + '\'' +
                ", createTime=" + createTime +
                ", creator='" + creator + '\'' +
                ", updateTime=" + updateTime +
                ", updater='" + updater + '\'' +
                ", materials=" + materials +
                ", equipments=" + equipments +
                '}';
    }
}
