package com.tocc.domain.vo;

import java.util.List;

/**
 * 仓库救援圈VO
 * 
 * <AUTHOR>
 */
public class WarehouseCircleVO {
    
    /** 事件ID */
    private String eventId;
    
    /** 事件标题 */
    private String eventTitle;
    
    /** 事件经度 */
    private String longitude;
    
    /** 事件纬度 */
    private String latitude;
    
    /** 20km范围内的仓库列表 */
    private List<WarehouseVO> warehouses20km;
    
    /** 40km范围内的仓库列表 */
    private List<WarehouseVO> warehouses40km;
    
    /** 20km范围内仓库数量 */
    private Integer warehouseCount20km;
    
    /** 40km范围内仓库数量 */
    private Integer warehouseCount40km;

    // Getter and Setter methods
    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getEventTitle() {
        return eventTitle;
    }

    public void setEventTitle(String eventTitle) {
        this.eventTitle = eventTitle;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public List<WarehouseVO> getWarehouses20km() {
        return warehouses20km;
    }

    public void setWarehouses20km(List<WarehouseVO> warehouses20km) {
        this.warehouses20km = warehouses20km;
    }

    public List<WarehouseVO> getWarehouses40km() {
        return warehouses40km;
    }

    public void setWarehouses40km(List<WarehouseVO> warehouses40km) {
        this.warehouses40km = warehouses40km;
    }

    public Integer getWarehouseCount20km() {
        return warehouseCount20km;
    }

    public void setWarehouseCount20km(Integer warehouseCount20km) {
        this.warehouseCount20km = warehouseCount20km;
    }

    public Integer getWarehouseCount40km() {
        return warehouseCount40km;
    }

    public void setWarehouseCount40km(Integer warehouseCount40km) {
        this.warehouseCount40km = warehouseCount40km;
    }
}
