package com.tocc.domain.vo;

import java.math.BigDecimal;

/**
 * 应急事件VO对象，用于返回给前端
 * 
 * <AUTHOR>
 */
public class EmergencyEventVO {

    /** 事件ID */
    private String eventId;

    /** 事件标题 */
    private String eventTitle;

    /** 事件类型（字典值） */
    private String eventType;

    /** 事件类型名称 */
    private String eventTypeName;

    /** 事故类型（字典值） */
    private String accidentType;

    /** 事故类型名称 */
    private String accidentTypeName;

    /** 发生时间 */
    private Long occurTime;

    /** 行政区名称 */
    private String administrativeArea;

    /** 行政区ID */
    private String administrativeAreaId;

    /** 事件等级（字典值） */
    private String eventLevel;

    /** 事件等级名称 */
    private String eventLevelName;

    /** 填报人ID */
    private String reporterId;

    /** 填报人姓名 */
    private String reporterName;

    /** 填报人单位 */
    private String reporterDeptName;

    /** 填报人联系方式 */
    private String reporterPhone;

    /** 填报人职务 */
    private String reporterPostName;

    /** 上报人ID */
    private String submitterId;

    /** 上报人姓名 */
    private String submitterName;

    /** 上报人单位 */
    private String submitterDeptName;

    /** 上报人联系方式 */
    private String submitterPhone;

    /** 上报人职务 */
    private String submitterPostName;

    /** 路段管辖单位ID */
    private String roadManagerUnitId;

    /** 路段管辖单位名称 */
    private String roadManagerUnitName;

    /** 路段管辖单位负责人ID */
    private String roadManagerLeaderId;

    /** 路段管辖单位负责人姓名 */
    private String roadManagerLeaderName;

    /** 路段管辖单位负责人单位 */
    private String roadManagerLeaderDeptName;

    /** 路段管辖单位负责人联系方式 */
    private String roadManagerLeaderPhone;

    /** 事故详细地址 */
    private String detailedAddress;

    /** 经度 */
    private BigDecimal longitude;

    /** 纬度 */
    private BigDecimal latitude;

    /** 影响范围 */
    private String impactScope;

    /** 事件描述 */
    private String eventDescription;

    /** 事件原因 */
    private String eventCause;

    /** 已采取的应急处置措施 */
    private String emergencyMeasures;

    /** 投入的应急力量 */
    private String emergencyForces;

    /** 需上级应急指挥机构支持事项 */
    private String supportNeeded;

    /** 状态（0-已上报，1-已确认，2-已完结，3-删除） */
    private String status;

    /** 状态名称 */
    private String statusName;

    /** 创建者ID */
    private String createrId;

    /** 创建者姓名 */
    private String createrName;

    /** 创建时间 */
    private Long createTime;

    /** 更新者ID */
    private String updaterId;

    /** 更新者姓名 */
    private String updaterName;

    /** 更新时间 */
    private Long updateTime;

    /** 备注 */
    private String remark;

    /** 事件预案与等级判别说明 */
    private String planLevelJudgment;

    /** 辅助决策 */
    private String decisionSupport;

    /** 关联预案ID */
    private String emerPlanId;

    // Getter and Setter methods
    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getEventTitle() {
        return eventTitle;
    }

    public void setEventTitle(String eventTitle) {
        this.eventTitle = eventTitle;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventTypeName() {
        return eventTypeName;
    }

    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = eventTypeName;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public String getAccidentTypeName() {
        return accidentTypeName;
    }

    public void setAccidentTypeName(String accidentTypeName) {
        this.accidentTypeName = accidentTypeName;
    }

    public Long getOccurTime() {
        return occurTime;
    }

    public void setOccurTime(Long occurTime) {
        this.occurTime = occurTime;
    }

    public String getAdministrativeArea() {
        return administrativeArea;
    }

    public void setAdministrativeArea(String administrativeArea) {
        this.administrativeArea = administrativeArea;
    }

    public String getAdministrativeAreaId() {
        return administrativeAreaId;
    }

    public void setAdministrativeAreaId(String administrativeAreaId) {
        this.administrativeAreaId = administrativeAreaId;
    }

    public String getEventLevel() {
        return eventLevel;
    }

    public void setEventLevel(String eventLevel) {
        this.eventLevel = eventLevel;
    }

    public String getEventLevelName() {
        return eventLevelName;
    }

    public void setEventLevelName(String eventLevelName) {
        this.eventLevelName = eventLevelName;
    }

    public String getReporterId() {
        return reporterId;
    }

    public void setReporterId(String reporterId) {
        this.reporterId = reporterId;
    }

    public String getReporterName() {
        return reporterName;
    }

    public void setReporterName(String reporterName) {
        this.reporterName = reporterName;
    }

    public String getReporterDeptName() {
        return reporterDeptName;
    }

    public void setReporterDeptName(String reporterDeptName) {
        this.reporterDeptName = reporterDeptName;
    }

    public String getReporterPhone() {
        return reporterPhone;
    }

    public void setReporterPhone(String reporterPhone) {
        this.reporterPhone = reporterPhone;
    }

    public String getSubmitterId() {
        return submitterId;
    }

    public void setSubmitterId(String submitterId) {
        this.submitterId = submitterId;
    }

    public String getSubmitterName() {
        return submitterName;
    }

    public void setSubmitterName(String submitterName) {
        this.submitterName = submitterName;
    }

    public String getSubmitterDeptName() {
        return submitterDeptName;
    }

    public void setSubmitterDeptName(String submitterDeptName) {
        this.submitterDeptName = submitterDeptName;
    }

    public String getSubmitterPhone() {
        return submitterPhone;
    }

    public void setSubmitterPhone(String submitterPhone) {
        this.submitterPhone = submitterPhone;
    }

    public String getRoadManagerUnitId() {
        return roadManagerUnitId;
    }

    public void setRoadManagerUnitId(String roadManagerUnitId) {
        this.roadManagerUnitId = roadManagerUnitId;
    }

    public String getRoadManagerUnitName() {
        return roadManagerUnitName;
    }

    public void setRoadManagerUnitName(String roadManagerUnitName) {
        this.roadManagerUnitName = roadManagerUnitName;
    }

    public String getRoadManagerLeaderId() {
        return roadManagerLeaderId;
    }

    public void setRoadManagerLeaderId(String roadManagerLeaderId) {
        this.roadManagerLeaderId = roadManagerLeaderId;
    }

    public String getRoadManagerLeaderName() {
        return roadManagerLeaderName;
    }

    public void setRoadManagerLeaderName(String roadManagerLeaderName) {
        this.roadManagerLeaderName = roadManagerLeaderName;
    }

    public String getRoadManagerLeaderDeptName() {
        return roadManagerLeaderDeptName;
    }

    public void setRoadManagerLeaderDeptName(String roadManagerLeaderDeptName) {
        this.roadManagerLeaderDeptName = roadManagerLeaderDeptName;
    }

    public String getRoadManagerLeaderPhone() {
        return roadManagerLeaderPhone;
    }

    public void setRoadManagerLeaderPhone(String roadManagerLeaderPhone) {
        this.roadManagerLeaderPhone = roadManagerLeaderPhone;
    }

    public String getDetailedAddress() {
        return detailedAddress;
    }

    public void setDetailedAddress(String detailedAddress) {
        this.detailedAddress = detailedAddress;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public String getImpactScope() {
        return impactScope;
    }

    public void setImpactScope(String impactScope) {
        this.impactScope = impactScope;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getEventCause() {
        return eventCause;
    }

    public void setEventCause(String eventCause) {
        this.eventCause = eventCause;
    }

    public String getEmergencyMeasures() {
        return emergencyMeasures;
    }

    public void setEmergencyMeasures(String emergencyMeasures) {
        this.emergencyMeasures = emergencyMeasures;
    }

    public String getEmergencyForces() {
        return emergencyForces;
    }

    public void setEmergencyForces(String emergencyForces) {
        this.emergencyForces = emergencyForces;
    }

    public String getSupportNeeded() {
        return supportNeeded;
    }

    public void setSupportNeeded(String supportNeeded) {
        this.supportNeeded = supportNeeded;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getCreaterId() {
        return createrId;
    }

    public void setCreaterId(String createrId) {
        this.createrId = createrId;
    }

    public String getCreaterName() {
        return createrName;
    }

    public void setCreaterName(String createrName) {
        this.createrName = createrName;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getUpdaterId() {
        return updaterId;
    }

    public void setUpdaterId(String updaterId) {
        this.updaterId = updaterId;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPlanLevelJudgment() {
        return planLevelJudgment;
    }

    public void setPlanLevelJudgment(String planLevelJudgment) {
        this.planLevelJudgment = planLevelJudgment;
    }

    public String getDecisionSupport() {
        return decisionSupport;
    }

    public void setDecisionSupport(String decisionSupport) {
        this.decisionSupport = decisionSupport;
    }

    public String getEmerPlanId() {
        return emerPlanId;
    }

    public void setEmerPlanId(String emerPlanId) {
        this.emerPlanId = emerPlanId;
    }
}
