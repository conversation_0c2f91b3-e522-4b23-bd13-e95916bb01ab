package com.tocc.domain.dto;

import com.tocc.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 仓库DTO
 * 
 * <AUTHOR>
 */
public class WarehouseDTO extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 仓库名称 */
    private String warehouseName;

    /** 仓库类型 */
    private String warehouseType;

    /** 所属单位code */
    private String belongOrgCode;

    /** 所属单位名称 */
    private String belongOrgName;

    /** 详细地址 */
    private String address;

    /** 负责人姓名 */
    private String principal;

    /** 联系电话 */
    private String contactPhone;

    /** 路段编号 */
    private String roadCode;

    /** 桩号 */
    private String stake;

    /** 纬度 */
    private String latitude;

    /** 经度 */
    private String longitude;

    /** 创建人 */
    private String creator;

    /** 更新人 */
    private String updater;

    /** 删除标志 */
    private Integer delFlag;

    /** 物资列表（用于新增时同时创建物资） */
    private List<MaterialDTO> materials;

    // Getter and Setter methods
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(String warehouseType) {
        this.warehouseType = warehouseType;
    }

    public String getBelongOrgCode() {
        return belongOrgCode;
    }

    public void setBelongOrgCode(String belongOrgCode) {
        this.belongOrgCode = belongOrgCode;
    }

    public String getBelongOrgName() {
        return belongOrgName;
    }

    public void setBelongOrgName(String belongOrgName) {
        this.belongOrgName = belongOrgName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getRoadCode() {
        return roadCode;
    }

    public void setRoadCode(String roadCode) {
        this.roadCode = roadCode;
    }

    public String getStake() {
        return stake;
    }

    public void setStake(String stake) {
        this.stake = stake;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public List<MaterialDTO> getMaterials() {
        return materials;
    }

    public void setMaterials(List<MaterialDTO> materials) {
        this.materials = materials;
    }

    @Override
    public String toString() {
        return "WarehouseDTO{" +
                "id='" + id + '\'' +
                ", warehouseName='" + warehouseName + '\'' +
                ", warehouseType='" + warehouseType + '\'' +
                ", belongOrgCode='" + belongOrgCode + '\'' +
                ", belongOrgName='" + belongOrgName + '\'' +
                ", address='" + address + '\'' +
                ", principal='" + principal + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", roadCode='" + roadCode + '\'' +
                ", stake='" + stake + '\'' +
                ", latitude='" + latitude + '\'' +
                ", longitude='" + longitude + '\'' +
                ", creator='" + creator + '\'' +
                ", updater='" + updater + '\'' +
                ", delFlag=" + delFlag +
                ", materials=" + materials +
                '}';
    }
}
