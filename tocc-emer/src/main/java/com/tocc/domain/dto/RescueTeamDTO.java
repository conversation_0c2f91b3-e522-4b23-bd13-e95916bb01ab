package com.tocc.domain.dto;

import com.tocc.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 救援队伍DTO
 * 
 * <AUTHOR>
 */
public class RescueTeamDTO extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 队伍名称 */
    private String teamName;

    /** 队伍编号 */
    private String teamCode;

    /** 队伍地址 */
    private String address;

    /** 经度 */
    private BigDecimal longitude;

    /** 纬度 */
    private BigDecimal latitude;

    /** 队伍人数 */
    private Integer teamSize;

    /** 负责人姓名 */
    private String leaderName;

    /** 负责人联系方式 */
    private String leaderPhone;

    /** 所属管辖单位 */
    private String jurisdictionUnit;

    /** 管辖单位负责人 */
    private String jurisdictionLeader;

    /** 管辖单位负责人联系方式 */
    private String jurisdictionPhone;

    /** 队伍类型 */
    private String teamType;

    /** 专业特长 */
    private String specialties;

    /** 状态 */
    private Integer status;

    /** 创建人 */
    private String creator;

    /** 更新人 */
    private String updater;

    /** 删除标志 */
    private Integer delFlag;

    /** 物资列表（用于新增时同时创建物资） */
    private List<MaterialDTO> materials;

    /** 装备列表（用于新增时同时创建装备） */
    private List<MaterialDTO> equipments;

    // Getter and Setter methods
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getTeamCode() {
        return teamCode;
    }

    public void setTeamCode(String teamCode) {
        this.teamCode = teamCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public Integer getTeamSize() {
        return teamSize;
    }

    public void setTeamSize(Integer teamSize) {
        this.teamSize = teamSize;
    }

    public String getLeaderName() {
        return leaderName;
    }

    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName;
    }

    public String getLeaderPhone() {
        return leaderPhone;
    }

    public void setLeaderPhone(String leaderPhone) {
        this.leaderPhone = leaderPhone;
    }

    public String getJurisdictionUnit() {
        return jurisdictionUnit;
    }

    public void setJurisdictionUnit(String jurisdictionUnit) {
        this.jurisdictionUnit = jurisdictionUnit;
    }

    public String getJurisdictionLeader() {
        return jurisdictionLeader;
    }

    public void setJurisdictionLeader(String jurisdictionLeader) {
        this.jurisdictionLeader = jurisdictionLeader;
    }

    public String getJurisdictionPhone() {
        return jurisdictionPhone;
    }

    public void setJurisdictionPhone(String jurisdictionPhone) {
        this.jurisdictionPhone = jurisdictionPhone;
    }

    public String getTeamType() {
        return teamType;
    }

    public void setTeamType(String teamType) {
        this.teamType = teamType;
    }

    public String getSpecialties() {
        return specialties;
    }

    public void setSpecialties(String specialties) {
        this.specialties = specialties;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdater() {
        return updater;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public List<MaterialDTO> getMaterials() {
        return materials;
    }

    public void setMaterials(List<MaterialDTO> materials) {
        this.materials = materials;
    }

    public List<MaterialDTO> getEquipments() {
        return equipments;
    }

    public void setEquipments(List<MaterialDTO> equipments) {
        this.equipments = equipments;
    }

    @Override
    public String toString() {
        return "RescueTeamDTO{" +
                "id='" + id + '\'' +
                ", teamName='" + teamName + '\'' +
                ", teamCode='" + teamCode + '\'' +
                ", address='" + address + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", teamSize=" + teamSize +
                ", leaderName='" + leaderName + '\'' +
                ", leaderPhone='" + leaderPhone + '\'' +
                ", jurisdictionUnit='" + jurisdictionUnit + '\'' +
                ", jurisdictionLeader='" + jurisdictionLeader + '\'' +
                ", jurisdictionPhone='" + jurisdictionPhone + '\'' +
                ", teamType='" + teamType + '\'' +
                ", specialties='" + specialties + '\'' +
                ", status=" + status +
                ", creator='" + creator + '\'' +
                ", updater='" + updater + '\'' +
                ", delFlag=" + delFlag +
                ", materials=" + materials +
                ", equipments=" + equipments +
                '}';
    }
}
