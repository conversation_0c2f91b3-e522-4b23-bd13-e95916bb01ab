package com.tocc.domain.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 应急事件DTO对象 emergency_event
 * 
 * <AUTHOR>
 */
public class EmergencyEventDTO {
    private static final long serialVersionUID = 1L;

    /** 事件ID */
    private String eventId;

    /** 事件标题 */
    @NotBlank(message = "事件标题不能为空")
    @Size(min = 0, max = 200, message = "事件标题长度不能超过200个字符")
    private String eventTitle;

    /** 事件类型（字典值） */
    @NotBlank(message = "事件类型不能为空")
    private String eventType;

    /** 事故类型（字典值） */
    @NotBlank(message = "事故类型不能为空")
    private String accidentType;

    /** 发生时间 */
    @NotNull(message = "发生时间不能为空")
    private Long occurTime;

    /** 行政区名称 */
    private String administrativeArea;

    /** 行政区ID */
    private String administrativeAreaId;

    /** 事件等级（字典值） */
    private String eventLevel;

    /** 填报人ID */
    private String reporterId;

    /** 上报人ID */
    private String submitterId;

    /** 路段管辖单位ID */
    private String roadManagerUnitId;

    /** 路段管辖单位负责人ID */
    private String roadManagerLeaderId;

    /** 事故详细地址 */
    private String detailedAddress;

    /** 经度 */
    private BigDecimal longitude;

    /** 纬度 */
    private BigDecimal latitude;

    /** 影响范围 */
    private String impactScope;

    /** 事件描述 */
    private String eventDescription;

    /** 事件原因 */
    private String eventCause;

    /** 已采取的应急处置措施 */
    private String emergencyMeasures;

    /** 投入的应急力量 */
    private String emergencyForces;

    /** 需上级应急指挥机构支持事项 */
    private String supportNeeded;

    /** 状态（0-已上报，1-已确认，2-已完结，3-删除） */
    private String status;

    /** 创建者ID */
    private String createrId;

    /** 创建时间 */
    private Long createTime;

    /** 更新者ID */
    private String updaterId;

    /** 更新时间 */
    private Long updateTime;

    /** 备注 */
    private String remark;

    /** 辅助决策说明 */
    private String decisionSupport;

    /** 事件等级与响应说明 */
    private String planLevelJudgment;

    /** 关联预案ID */
    private String emerPlanId;

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getEventTitle() {
        return eventTitle;
    }

    public void setEventTitle(String eventTitle) {
        this.eventTitle = eventTitle;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public Long getOccurTime() {
        return occurTime;
    }

    public void setOccurTime(Long occurTime) {
        this.occurTime = occurTime;
    }

    public String getAdministrativeArea() {
        return administrativeArea;
    }

    public void setAdministrativeArea(String administrativeArea) {
        this.administrativeArea = administrativeArea;
    }

    public String getAdministrativeAreaId() {
        return administrativeAreaId;
    }

    public void setAdministrativeAreaId(String administrativeAreaId) {
        this.administrativeAreaId = administrativeAreaId;
    }

    public String getEventLevel() {
        return eventLevel;
    }

    public void setEventLevel(String eventLevel) {
        this.eventLevel = eventLevel;
    }

    public String getReporterId() {
        return reporterId;
    }

    public void setReporterId(String reporterId) {
        this.reporterId = reporterId;
    }

    public String getSubmitterId() {
        return submitterId;
    }

    public void setSubmitterId(String submitterId) {
        this.submitterId = submitterId;
    }

    public String getRoadManagerUnitId() {
        return roadManagerUnitId;
    }

    public void setRoadManagerUnitId(String roadManagerUnitId) {
        this.roadManagerUnitId = roadManagerUnitId;
    }

    public String getRoadManagerLeaderId() {
        return roadManagerLeaderId;
    }

    public void setRoadManagerLeaderId(String roadManagerLeaderId) {
        this.roadManagerLeaderId = roadManagerLeaderId;
    }

    public String getDetailedAddress() {
        return detailedAddress;
    }

    public void setDetailedAddress(String detailedAddress) {
        this.detailedAddress = detailedAddress;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public String getImpactScope() {
        return impactScope;
    }

    public void setImpactScope(String impactScope) {
        this.impactScope = impactScope;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public String getEventCause() {
        return eventCause;
    }

    public void setEventCause(String eventCause) {
        this.eventCause = eventCause;
    }

    public String getEmergencyMeasures() {
        return emergencyMeasures;
    }

    public void setEmergencyMeasures(String emergencyMeasures) {
        this.emergencyMeasures = emergencyMeasures;
    }

    public String getEmergencyForces() {
        return emergencyForces;
    }

    public void setEmergencyForces(String emergencyForces) {
        this.emergencyForces = emergencyForces;
    }

    public String getSupportNeeded() {
        return supportNeeded;
    }

    public void setSupportNeeded(String supportNeeded) {
        this.supportNeeded = supportNeeded;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreaterId() {
        return createrId;
    }

    public void setCreaterId(String createrId) {
        this.createrId = createrId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getUpdaterId() {
        return updaterId;
    }

    public void setUpdaterId(String updaterId) {
        this.updaterId = updaterId;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDecisionSupport() {
        return decisionSupport;
    }

    public void setDecisionSupport(String decisionSupport) {
        this.decisionSupport = decisionSupport;
    }

    public String getPlanLevelJudgment() {
        return planLevelJudgment;
    }

    public void setPlanLevelJudgment(String planLevelJudgment) {
        this.planLevelJudgment = planLevelJudgment;
    }

    public String getEmerPlanId() {
        return emerPlanId;
    }
    public void setEmerPlanId(String emerPlanId) {
        this.emerPlanId = emerPlanId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("eventId", getEventId())
            .append("eventTitle", getEventTitle())
            .append("eventType", getEventType())
            .append("accidentType", getAccidentType())
            .append("occurTime", getOccurTime())
            .append("administrativeArea", getAdministrativeArea())
            .append("administrativeAreaId", getAdministrativeAreaId())
            .append("eventLevel", getEventLevel())
            .append("reporterId", getReporterId())
            .append("submitterId", getSubmitterId())
            .append("roadManagerUnitId", getRoadManagerUnitId())
            .append("roadManagerLeaderId", getRoadManagerLeaderId())
            .append("detailedAddress", getDetailedAddress())
            .append("longitude", getLongitude())
            .append("latitude", getLatitude())
            .append("impactScope", getImpactScope())
            .append("eventDescription", getEventDescription())
            .append("eventCause", getEventCause())
            .append("emergencyMeasures", getEmergencyMeasures())
            .append("emergencyForces", getEmergencyForces())
            .append("supportNeeded", getSupportNeeded())
            .append("status", getStatus())
            .append("createrId", getCreaterId())
            .append("createTime", getCreateTime())
            .append("updaterId", getUpdaterId())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("decisionSupport", getDecisionSupport())
            .append("planLevelJudgment", getPlanLevelJudgment())
            .append("emerPlanId", getEmerPlanId())
            .toString();
    }
}
