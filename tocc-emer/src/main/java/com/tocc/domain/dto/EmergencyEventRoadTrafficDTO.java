package com.tocc.domain.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 道路交通事故扩展DTO对象 emergency_event_road_traffic
 * 
 * <AUTHOR>
 */
public class EmergencyEventRoadTrafficDTO {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 事件ID */
    private String eventId;

    /** 路段编号 */
    private String roadSectionCode;

    /** 开始桩号 */
    private String startStakeNumber;

    /** 结束桩号 */
    private String endStakeNumber;

    /** 方向（字典值） */
    private String direction;

    /** 是否影响通行（Y是 N否） */
    private String trafficAffected;

    /** 事故车型 */
    private String vehicleType;

    /** 预计恢复时间 */
    private Long estimatedRecoveryTime;

    /** 人员伤亡情况 */
    private String casualtySituation;

    /** 影响范围及事态发展趋势 */
    private String impactTrend;

    /** 创建者ID */
    private String createrId;

    /** 创建时间 */
    private Long createTime;

    /** 更新者ID */
    private String updaterId;

    /** 更新时间 */
    private Long updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getRoadSectionCode() {
        return roadSectionCode;
    }

    public void setRoadSectionCode(String roadSectionCode) {
        this.roadSectionCode = roadSectionCode;
    }

    public String getStartStakeNumber() {
        return startStakeNumber;
    }

    public void setStartStakeNumber(String startStakeNumber) {
        this.startStakeNumber = startStakeNumber;
    }

    public String getEndStakeNumber() {
        return endStakeNumber;
    }

    public void setEndStakeNumber(String endStakeNumber) {
        this.endStakeNumber = endStakeNumber;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getTrafficAffected() {
        return trafficAffected;
    }

    public void setTrafficAffected(String trafficAffected) {
        this.trafficAffected = trafficAffected;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public Long getEstimatedRecoveryTime() {
        return estimatedRecoveryTime;
    }

    public void setEstimatedRecoveryTime(Long estimatedRecoveryTime) {
        this.estimatedRecoveryTime = estimatedRecoveryTime;
    }

    public String getCasualtySituation() {
        return casualtySituation;
    }

    public void setCasualtySituation(String casualtySituation) {
        this.casualtySituation = casualtySituation;
    }

    public String getImpactTrend() {
        return impactTrend;
    }

    public void setImpactTrend(String impactTrend) {
        this.impactTrend = impactTrend;
    }

    public String getCreaterId() {
        return createrId;
    }

    public void setCreaterId(String createrId) {
        this.createrId = createrId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getUpdaterId() {
        return updaterId;
    }

    public void setUpdaterId(String updaterId) {
        this.updaterId = updaterId;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("eventId", getEventId())
            .append("roadSectionCode", getRoadSectionCode())
            .append("startStakeNumber", getStartStakeNumber())
            .append("endStakeNumber", getEndStakeNumber())
            .append("direction", getDirection())
            .append("trafficAffected", getTrafficAffected())
            .append("vehicleType", getVehicleType())
            .append("estimatedRecoveryTime", getEstimatedRecoveryTime())
            .append("casualtySituation", getCasualtySituation())
            .append("impactTrend", getImpactTrend())
            .append("createrId", getCreaterId())
            .append("createTime", getCreateTime())
            .append("updaterId", getUpdaterId())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
