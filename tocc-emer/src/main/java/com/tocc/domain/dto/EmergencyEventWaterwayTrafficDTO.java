package com.tocc.domain.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.math.BigDecimal;

/**
 * 水路交通事故扩展DTO对象 emergency_event_waterway_traffic
 * 
 * <AUTHOR>
 */
public class EmergencyEventWaterwayTrafficDTO {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 事件ID */
    private String eventId;

    /** 航道名称 */
    private String waterwayName;

    /** 船舶名称 */
    private String shipName;

    /** 船舶类型（字典值） */
    private String shipType;

    /** 船舶吨位 */
    private BigDecimal shipTonnage;

    /** 人员伤亡情况 */
    private String casualtySituation;

    /** 货物信息 */
    private String cargoInfo;

    /** 环境影响 */
    private String environmentalImpact;

    /** 创建者ID */
    private String createrId;

    /** 创建时间 */
    private Long createTime;

    /** 更新者ID */
    private String updaterId;

    /** 更新时间 */
    private Long updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getWaterwayName() {
        return waterwayName;
    }

    public void setWaterwayName(String waterwayName) {
        this.waterwayName = waterwayName;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getShipType() {
        return shipType;
    }

    public void setShipType(String shipType) {
        this.shipType = shipType;
    }

    public BigDecimal getShipTonnage() {
        return shipTonnage;
    }

    public void setShipTonnage(BigDecimal shipTonnage) {
        this.shipTonnage = shipTonnage;
    }

    public String getCasualtySituation() {
        return casualtySituation;
    }

    public void setCasualtySituation(String casualtySituation) {
        this.casualtySituation = casualtySituation;
    }

    public String getCargoInfo() {
        return cargoInfo;
    }

    public void setCargoInfo(String cargoInfo) {
        this.cargoInfo = cargoInfo;
    }

    public String getEnvironmentalImpact() {
        return environmentalImpact;
    }

    public void setEnvironmentalImpact(String environmentalImpact) {
        this.environmentalImpact = environmentalImpact;
    }

    public String getCreaterId() {
        return createrId;
    }

    public void setCreaterId(String createrId) {
        this.createrId = createrId;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getUpdaterId() {
        return updaterId;
    }

    public void setUpdaterId(String updaterId) {
        this.updaterId = updaterId;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("eventId", getEventId())
            .append("waterwayName", getWaterwayName())
            .append("shipName", getShipName())
            .append("shipType", getShipType())
            .append("shipTonnage", getShipTonnage())
            .append("casualtySituation", getCasualtySituation())
            .append("cargoInfo", getCargoInfo())
            .append("environmentalImpact", getEnvironmentalImpact())
            .append("createrId", getCreaterId())
            .append("createTime", getCreateTime())
            .append("updaterId", getUpdaterId())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
