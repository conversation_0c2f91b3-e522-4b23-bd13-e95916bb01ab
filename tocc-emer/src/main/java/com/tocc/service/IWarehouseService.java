package com.tocc.service;

import com.tocc.domain.dto.WarehouseDTO;
import com.tocc.domain.vo.WarehouseVO;
import com.tocc.domain.vo.MaterialVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 仓库Service接口
 * 
 * <AUTHOR>
 */
public interface IWarehouseService {
    
    /**
     * 查询仓库
     * 
     * @param id 仓库主键
     * @return 仓库
     */
    WarehouseVO selectWarehouseById(String id);

    /**
     * 查询仓库列表
     * 
     * @param warehouse 仓库
     * @return 仓库集合
     */
    List<WarehouseVO> selectWarehouseList(WarehouseDTO warehouse);

    /**
     * 新增仓库
     * 
     * @param warehouse 仓库
     * @return 结果
     */
    int insertWarehouse(WarehouseDTO warehouse);

    /**
     * 修改仓库
     * 
     * @param warehouse 仓库
     * @return 结果
     */
    int updateWarehouse(WarehouseDTO warehouse);

    /**
     * 批量删除仓库
     * 
     * @param ids 需要删除的仓库主键集合
     * @return 结果
     */
    int deleteWarehouseByIds(String[] ids);

    /**
     * 删除仓库信息
     * 
     * @param id 仓库主键
     * @return 结果
     */
    int deleteWarehouseById(String id);

    /**
     * 查询仓库的物资列表
     * 
     * @param warehouseId 仓库ID
     * @return 物资列表
     */
    List<MaterialVO> selectWarehouseMaterials(String warehouseId);

    /**
     * 更新仓库的物资配置
     * 
     * @param warehouseId 仓库ID
     * @param materialIds 物资ID列表
     * @return 结果
     */
    int updateWarehouseMaterials(String warehouseId, List<String> materialIds);

    /**
     * 添加仓库物资
     * 
     * @param warehouseId 仓库ID
     * @param materialId 物资ID
     * @return 结果
     */
    int addWarehouseMaterial(String warehouseId, String materialId);

    /**
     * 移除仓库物资
     *
     * @param warehouseId 仓库ID
     * @param materialId 物资ID
     * @return 结果
     */
    int removeWarehouseMaterial(String warehouseId, String materialId);

    /**
     * 查询更新超时的物资仓库
     *
     * @param timeoutTime 超时时间点
     * @return 超时的物资仓库集合
     */
    List<WarehouseVO> selectTimeoutWarehouses(LocalDateTime timeoutTime);
}
