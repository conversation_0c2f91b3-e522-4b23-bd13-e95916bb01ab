package com.tocc.service.impl;

import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.RescueTeamDTO;
import com.tocc.domain.dto.RescueTeamMaterialDTO;
import com.tocc.domain.dto.MaterialDTO;
import com.tocc.domain.vo.RescueTeamVO;
import com.tocc.domain.vo.MaterialVO;
import com.tocc.mapper.RescueTeamMapper;
import com.tocc.mapper.RescueTeamMaterialMapper;
import com.tocc.mapper.MaterialMapper;
import com.tocc.service.IRescueTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 救援队伍Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class RescueTeamServiceImpl implements IRescueTeamService {
    
    @Autowired
    private RescueTeamMapper rescueTeamMapper;

    @Autowired
    private RescueTeamMaterialMapper rescueTeamMaterialMapper;

    @Autowired
    private MaterialMapper materialMapper;

    /**
     * 查询救援队伍
     * 
     * @param id 救援队伍主键
     * @return 救援队伍
     */
    @Override
    public RescueTeamVO selectRescueTeamById(String id) {
        RescueTeamVO rescueTeam = rescueTeamMapper.selectRescueTeamById(id);
        if (rescueTeam != null) {
            // 查询关联的物资和装备
            List<MaterialVO> materials = rescueTeamMaterialMapper.selectMaterialsByTeamIdAndType(id, "0");
            List<MaterialVO> equipments = rescueTeamMaterialMapper.selectMaterialsByTeamIdAndType(id, "1");
            rescueTeam.setMaterials(materials);
            rescueTeam.setEquipments(equipments);
        }
        return rescueTeam;
    }

    /**
     * 查询救援队伍列表
     *
     * @param rescueTeam 救援队伍
     * @return 救援队伍
     */
    @Override
    public List<RescueTeamVO> selectRescueTeamList(RescueTeamDTO rescueTeam) {
        List<RescueTeamVO> teamList = rescueTeamMapper.selectRescueTeamList(rescueTeam);

        // 为每个队伍加载关联的物资和装备
        for (RescueTeamVO team : teamList) {
            // 查询关联的物资和装备
            List<MaterialVO> materials = rescueTeamMaterialMapper.selectMaterialsByTeamIdAndType(team.getId(), "0");
            List<MaterialVO> equipments = rescueTeamMaterialMapper.selectMaterialsByTeamIdAndType(team.getId(), "1");
            team.setMaterials(materials);
            team.setEquipments(equipments);
        }

        return teamList;
    }

    /**
     * 新增救援队伍
     *
     * @param rescueTeam 救援队伍
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRescueTeam(RescueTeamDTO rescueTeam) {
        // 生成队伍ID
        String teamId = IdUtils.simpleUUID();
        rescueTeam.setId(teamId);
        rescueTeam.setCreator(getUsername());
        rescueTeam.setDelFlag(0);

        // 插入救援队伍基础信息
        int result = rescueTeamMapper.insertRescueTeam(rescueTeam);

        if (result > 0) {
            // 创建队伍专属的物资
            if (rescueTeam.getMaterials() != null && !rescueTeam.getMaterials().isEmpty()) {
                createTeamMaterials(teamId, rescueTeam.getMaterials());
            }

            // 创建队伍专属的装备
            if (rescueTeam.getEquipments() != null && !rescueTeam.getEquipments().isEmpty()) {
                createTeamEquipments(teamId, rescueTeam.getEquipments());
            }
        }

        return result;
    }

    /**
     * 修改救援队伍
     *
     * @param rescueTeam 救援队伍
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRescueTeam(RescueTeamDTO rescueTeam) {
        rescueTeam.setUpdater(getUsername());

        // 更新救援队伍基础信息
        int result = rescueTeamMapper.updateRescueTeam(rescueTeam);

        // 注意：修改时不处理物资和装备，物资装备的管理通过专门的接口进行

        return result;
    }

    /**
     * 批量删除救援队伍
     * 
     * @param ids 需要删除的救援队伍主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRescueTeamByIds(String[] ids) {
        // 删除物资关联关系
        for (String id : ids) {
            rescueTeamMaterialMapper.deleteTeamMaterialsByTeamId(id);
        }
        
        // 软删除救援队伍
        return rescueTeamMapper.deleteRescueTeamByIds(ids);
    }

    /**
     * 删除救援队伍信息
     * 
     * @param id 救援队伍主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRescueTeamById(String id) {
        // 删除物资关联关系
        rescueTeamMaterialMapper.deleteTeamMaterialsByTeamId(id);
        
        // 软删除救援队伍
        return rescueTeamMapper.deleteRescueTeamById(id);
    }

    /**
     * 检查队伍编号是否唯一
     * 
     * @param teamCode 队伍编号
     * @param id 队伍ID（修改时排除自己）
     * @return 结果
     */
    @Override
    public boolean checkTeamCodeUnique(String teamCode, String id) {
        int count = rescueTeamMapper.checkTeamCodeUnique(teamCode, id);
        return count == 0;
    }

    /**
     * 查询救援队伍的物资列表
     * 
     * @param teamId 队伍ID
     * @return 物资列表
     */
    @Override
    public List<MaterialVO> selectTeamMaterials(String teamId) {
        return rescueTeamMaterialMapper.selectMaterialsByTeamIdAndType(teamId, "0");
    }

    /**
     * 查询救援队伍的装备列表
     * 
     * @param teamId 队伍ID
     * @return 装备列表
     */
    @Override
    public List<MaterialVO> selectTeamEquipments(String teamId) {
        return rescueTeamMaterialMapper.selectMaterialsByTeamIdAndType(teamId, "1");
    }

    /**
     * 更新救援队伍的物资配置
     * 
     * @param teamId 队伍ID
     * @param materialIds 物资ID列表
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTeamMaterials(String teamId, List<String> materialIds) {
        // 先删除原有关联
        rescueTeamMaterialMapper.deleteTeamMaterialsByTeamId(teamId);
        
        // 插入新的关联
        if (materialIds != null && !materialIds.isEmpty()) {
            return insertTeamMaterials(teamId, materialIds);
        }
        
        return 1;
    }

    /**
     * 添加救援队伍物资
     * 
     * @param teamId 队伍ID
     * @param materialId 物资ID
     * @return 结果
     */
    @Override
    public int addTeamMaterial(String teamId, String materialId) {
        RescueTeamMaterialDTO teamMaterial = new RescueTeamMaterialDTO();
        teamMaterial.setTeamId(teamId);
        teamMaterial.setMaterialId(materialId);
        return rescueTeamMaterialMapper.insertTeamMaterial(teamMaterial);
    }

    /**
     * 移除救援队伍物资
     * 
     * @param teamId 队伍ID
     * @param materialId 物资ID
     * @return 结果
     */
    @Override
    public int removeTeamMaterial(String teamId, String materialId) {
        return rescueTeamMaterialMapper.deleteTeamMaterial(teamId, materialId);
    }

    /**
     * 创建队伍专属物资
     *
     * @param teamId 队伍ID
     * @param materials 物资列表
     */
    private void createTeamMaterials(String teamId, List<MaterialDTO> materials) {
        for (MaterialDTO material : materials) {
            // 创建物资记录
            String materialId = IdUtils.simpleUUID();
            material.setId(materialId);
            material.setMaterialType("0"); // 应急物资
            material.setTeamId(teamId); // 设置所属队伍
            material.setStatus(1); // 默认正常状态
            material.setCreator(getUsername());
            material.setDelFlag(0);

            // 插入em_material表
            materialMapper.insertMaterial(material);

            // 创建关联关系
            RescueTeamMaterialDTO teamMaterial = new RescueTeamMaterialDTO();
            teamMaterial.setTeamId(teamId);
            teamMaterial.setMaterialId(materialId);
            rescueTeamMaterialMapper.insertTeamMaterial(teamMaterial);
        }
    }

    /**
     * 创建队伍专属装备
     *
     * @param teamId 队伍ID
     * @param equipments 装备列表
     */
    private void createTeamEquipments(String teamId, List<MaterialDTO> equipments) {
        for (MaterialDTO equipment : equipments) {
            // 创建装备记录
            String equipmentId = IdUtils.simpleUUID();
            equipment.setId(equipmentId);
            equipment.setMaterialType("1"); // 应急装备
            equipment.setTeamId(teamId); // 设置所属队伍
            equipment.setStatus(1); // 默认正常状态
            equipment.setCreator(getUsername());
            equipment.setDelFlag(0);

            // 插入em_material表
            materialMapper.insertMaterial(equipment);

            // 创建关联关系
            RescueTeamMaterialDTO teamMaterial = new RescueTeamMaterialDTO();
            teamMaterial.setTeamId(teamId);
            teamMaterial.setMaterialId(equipmentId);
            rescueTeamMaterialMapper.insertTeamMaterial(teamMaterial);
        }
    }

    /**
     * 批量插入队伍物资关联（用于现有物资的关联）
     *
     * @param teamId 队伍ID
     * @param materialIds 物资ID列表
     * @return 结果
     */
    private int insertTeamMaterials(String teamId, List<String> materialIds) {
        List<RescueTeamMaterialDTO> teamMaterials = new ArrayList<>();
        for (String materialId : materialIds) {
            RescueTeamMaterialDTO teamMaterial = new RescueTeamMaterialDTO();
            teamMaterial.setTeamId(teamId);
            teamMaterial.setMaterialId(materialId);
            teamMaterials.add(teamMaterial);
        }
        return rescueTeamMaterialMapper.batchInsertTeamMaterials(teamMaterials);
    }

    /**
     * 查询更新超时的救援队伍
     *
     * @param timeoutTime 超时时间点
     * @return 超时的救援队伍集合
     */
    @Override
    public List<RescueTeamVO> selectTimeoutTeams(LocalDateTime timeoutTime) {
        return rescueTeamMapper.selectTimeoutTeams(timeoutTime);
    }

    /**
     * 获取当前用户名
     *
     * @return 用户名
     */
    private String getUsername() {
        try {
            return SecurityUtils.getUsername();
        } catch (Exception e) {
            return "system";
        }
    }
}
