package com.tocc.mapper;

import com.tocc.domain.dto.RescueTeamMaterialDTO;
import com.tocc.domain.vo.MaterialVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 救援队伍物资关联Mapper接口
 * 
 * <AUTHOR>
 */
public interface RescueTeamMaterialMapper {
    
    /**
     * 根据队伍ID查询所有物资列表
     *
     * @param teamId 队伍ID
     * @return 物资列表
     */
    List<MaterialVO> selectAllMaterialsByTeamId(String teamId);

    /**
     * 根据队伍ID和类型查询物资列表
     *
     * @param teamId 队伍ID
     * @param materialType 物资类型
     * @return 物资列表
     */
    List<MaterialVO> selectMaterialsByTeamIdAndType(@Param("teamId") String teamId, @Param("materialType") String materialType);

    /**
     * 根据队伍ID查询应急装备列表
     * 
     * @param teamId 队伍ID
     * @return 应急装备列表
     */
    List<MaterialVO> selectEquipmentsByTeamId(String teamId);

    /**
     * 批量插入队伍物资关联
     * 
     * @param teamMaterials 队伍物资关联列表
     * @return 结果
     */
    int batchInsertTeamMaterials(List<RescueTeamMaterialDTO> teamMaterials);

    /**
     * 删除队伍的所有物资关联
     * 
     * @param teamId 队伍ID
     * @return 结果
     */
    int deleteTeamMaterialsByTeamId(String teamId);

    /**
     * 删除指定的队伍物资关联
     * 
     * @param teamId 队伍ID
     * @param materialId 物资ID
     * @return 结果
     */
    int deleteTeamMaterial(@Param("teamId") String teamId, @Param("materialId") String materialId);

    /**
     * 插入队伍物资关联
     *
     * @param teamMaterial 队伍物资关联
     * @return 结果
     */
    int insertTeamMaterial(RescueTeamMaterialDTO teamMaterial);

    /**
     * 根据物资ID删除队伍物资关联
     *
     * @param materialId 物资ID
     * @return 结果
     */
    int deleteTeamMaterialsByMaterialId(String materialId);
}
