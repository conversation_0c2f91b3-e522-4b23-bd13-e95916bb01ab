package com.tocc.mapper;

import com.tocc.domain.dto.EmergencyEventDTO;
import com.tocc.domain.vo.EmergencyEventVO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 应急事件Mapper接口
 * 
 * <AUTHOR>
 */
public interface EmergencyEventMapper {
    
    /**
     * 查询应急事件
     *
     * @param eventId 应急事件主键
     * @return 应急事件
     */
    EmergencyEventDTO selectEmergencyEventByEventId(String eventId);

    /**
     * 查询应急事件列表
     *
     * @param emergencyEvent 应急事件
     * @return 应急事件集合
     */
    List<EmergencyEventDTO> selectEmergencyEventList(EmergencyEventDTO emergencyEvent);

    /**
     * 查询当前用户相关的应急事件列表（上报人或填报人是当前用户）
     * 包含用户详细信息（姓名、单位、联系方式）
     *
     * @param emergencyEvent 应急事件（包含当前用户ID）
     * @return 应急事件VO集合（包含用户详细信息）
     */
    List<EmergencyEventVO> selectEmergencyEventListByUser(EmergencyEventDTO emergencyEvent);

    /**
     * 新增应急事件
     *
     * @param emergencyEvent 应急事件
     * @return 结果
     */
    int insertEmergencyEvent(EmergencyEventDTO emergencyEvent);

    /**
     * 修改应急事件
     *
     * @param emergencyEvent 应急事件
     * @return 结果
     */
    int updateEmergencyEvent(EmergencyEventDTO emergencyEvent);

    /**
     * 删除应急事件
     * 
     * @param eventId 应急事件主键
     * @return 结果
     */
    int deleteEmergencyEventByEventId(String eventId);

    /**
     * 批量删除应急事件
     * 
     * @param eventIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteEmergencyEventByEventIds(String[] eventIds);

    /**
     * 根据条件统计应急事件数量
     *
     * @param emergencyEvent 查询条件
     * @return 统计数量
     */
    int countEmergencyEvent(EmergencyEventDTO emergencyEvent);

    /**
     * 根据事件类型和时间范围查询事件列表
     *
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 事件列表
     */
    List<EmergencyEventDTO> selectEventsByTypeAndTime(@Param("eventType") String eventType,
                                                      @Param("startTime") Long startTime,
                                                      @Param("endTime") Long endTime);

    /**
     * 根据状态查询事件列表
     *
     * @param status 状态
     * @return 事件列表
     */
    List<EmergencyEventDTO> selectEventsByStatus(@Param("status") String status);

    /**
     * 更新事件状态
     * 
     * @param eventId 事件ID
     * @param status 新状态
     * @param updaterId 更新者ID
     * @param updateTime 更新时间
     * @return 结果
     */
    int updateEventStatus(@Param("eventId") String eventId, 
                         @Param("status") String status, 
                         @Param("updaterId") String updaterId, 
                         @Param("updateTime") Long updateTime);
}
