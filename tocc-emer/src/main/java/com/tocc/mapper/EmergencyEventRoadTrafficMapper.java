package com.tocc.mapper;

import com.tocc.domain.dto.EmergencyEventRoadTrafficDTO;
import java.util.List;

/**
 * 道路交通事故扩展Mapper接口
 * 
 * <AUTHOR>
 */
public interface EmergencyEventRoadTrafficMapper {
    
    /**
     * 查询道路交通事故扩展
     * 
     * @param id 道路交通事故扩展主键
     * @return 道路交通事故扩展
     */
    EmergencyEventRoadTrafficDTO selectEmergencyEventRoadTrafficById(String id);

    /**
     * 根据事件ID查询道路交通事故扩展
     * 
     * @param eventId 事件ID
     * @return 道路交通事故扩展
     */
    EmergencyEventRoadTrafficDTO selectEmergencyEventRoadTrafficByEventId(String eventId);

    /**
     * 查询道路交通事故扩展列表
     * 
     * @param emergencyEventRoadTraffic 道路交通事故扩展
     * @return 道路交通事故扩展集合
     */
    List<EmergencyEventRoadTrafficDTO> selectEmergencyEventRoadTrafficList(EmergencyEventRoadTrafficDTO emergencyEventRoadTraffic);

    /**
     * 新增道路交通事故扩展
     * 
     * @param emergencyEventRoadTraffic 道路交通事故扩展
     * @return 结果
     */
    int insertEmergencyEventRoadTraffic(EmergencyEventRoadTrafficDTO emergencyEventRoadTraffic);

    /**
     * 修改道路交通事故扩展
     * 
     * @param emergencyEventRoadTraffic 道路交通事故扩展
     * @return 结果
     */
    int updateEmergencyEventRoadTraffic(EmergencyEventRoadTrafficDTO emergencyEventRoadTraffic);

    /**
     * 删除道路交通事故扩展
     * 
     * @param id 道路交通事故扩展主键
     * @return 结果
     */
    int deleteEmergencyEventRoadTrafficById(String id);

    /**
     * 根据事件ID删除道路交通事故扩展
     * 
     * @param eventId 事件ID
     * @return 结果
     */
    int deleteEmergencyEventRoadTrafficByEventId(String eventId);

    /**
     * 批量删除道路交通事故扩展
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteEmergencyEventRoadTrafficByIds(String[] ids);
}
