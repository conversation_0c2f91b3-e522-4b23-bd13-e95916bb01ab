package com.tocc.mapper;

import com.tocc.domain.dto.EmergencyEventWaterwayTrafficDTO;
import java.util.List;

/**
 * 水路交通事故扩展Mapper接口
 * 
 * <AUTHOR>
 */
public interface EmergencyEventWaterwayTrafficMapper {
    
    /**
     * 查询水路交通事故扩展
     * 
     * @param id 水路交通事故扩展主键
     * @return 水路交通事故扩展
     */
    EmergencyEventWaterwayTrafficDTO selectEmergencyEventWaterwayTrafficById(String id);

    /**
     * 根据事件ID查询水路交通事故扩展
     * 
     * @param eventId 事件ID
     * @return 水路交通事故扩展
     */
    EmergencyEventWaterwayTrafficDTO selectEmergencyEventWaterwayTrafficByEventId(String eventId);

    /**
     * 查询水路交通事故扩展列表
     * 
     * @param emergencyEventWaterwayTraffic 水路交通事故扩展
     * @return 水路交通事故扩展集合
     */
    List<EmergencyEventWaterwayTrafficDTO> selectEmergencyEventWaterwayTrafficList(EmergencyEventWaterwayTrafficDTO emergencyEventWaterwayTraffic);

    /**
     * 新增水路交通事故扩展
     * 
     * @param emergencyEventWaterwayTraffic 水路交通事故扩展
     * @return 结果
     */
    int insertEmergencyEventWaterwayTraffic(EmergencyEventWaterwayTrafficDTO emergencyEventWaterwayTraffic);

    /**
     * 修改水路交通事故扩展
     * 
     * @param emergencyEventWaterwayTraffic 水路交通事故扩展
     * @return 结果
     */
    int updateEmergencyEventWaterwayTraffic(EmergencyEventWaterwayTrafficDTO emergencyEventWaterwayTraffic);

    /**
     * 删除水路交通事故扩展
     * 
     * @param id 水路交通事故扩展主键
     * @return 结果
     */
    int deleteEmergencyEventWaterwayTrafficById(String id);

    /**
     * 根据事件ID删除水路交通事故扩展
     * 
     * @param eventId 事件ID
     * @return 结果
     */
    int deleteEmergencyEventWaterwayTrafficByEventId(String eventId);

    /**
     * 批量删除水路交通事故扩展
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteEmergencyEventWaterwayTrafficByIds(String[] ids);
}
