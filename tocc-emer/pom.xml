<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tocc</groupId>
        <artifactId>tocc</artifactId>
        <version>3.8.9</version>
    </parent>

    <artifactId>tocc-emer</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-common</artifactId>
        </dependency>

        <!-- 告警模块-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-alarm</artifactId>
        </dependency>

        <!-- 系统模块-->
        <dependency>
            <groupId>com.tocc</groupId>
            <artifactId>tocc-system</artifactId>
        </dependency>

    </dependencies>

</project>