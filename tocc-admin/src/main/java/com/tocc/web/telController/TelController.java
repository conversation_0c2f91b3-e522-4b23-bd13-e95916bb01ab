package com.tocc.web.telController;

import com.tocc.common.annotation.Anonymous;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.service.ITelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("tel")
public class TelController extends BaseController {

    @Autowired
    private ITelService telService;

    // 云呼叫
    @PostMapping("/api")
    public Object api(HttpServletRequest httpServletRequest, @RequestParam String action, @RequestParam String request) {
        String userId = SecurityUtils.getUserId().toString();
        return telService.api(userId, action, request);
    }


}
