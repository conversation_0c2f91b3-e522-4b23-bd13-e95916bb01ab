package com.tocc.web.controller.external;

import com.alibaba.fastjson2.JSONObject;
import com.tocc.common.config.ExternalApiProperties;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.dto.ExternalRequestDTO;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.service.ExternalApiService;
import com.tocc.common.service.ExternalAuthManager;
import com.tocc.common.service.ExternalSystemService;
import com.tocc.common.utils.RSAEncryptUtils;
import com.tocc.common.utils.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.redis.core.RedisTemplate;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 外部数据控制器
 *
 * <AUTHOR>
 */
@Api("外部系统数据管理")
@RestController
@RequestMapping("/external/data")
public class ExternalDataController extends BaseController
{
        @Autowired
    private ExternalSystemService externalSystemService;
    
    @Autowired
    private ExternalApiService externalApiService;
    
    @Autowired
    private ExternalAuthManager authManager;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ExternalApiProperties apiProperties;
    /**
     * 收费站小时流量数据
     */
    @ApiOperation("小时流量-收费站")
    @GetMapping("/stationPreOneHour")
    public AjaxResult stationPreOneHour()
    {
        return callExternalApiGet(apiProperties.getTraffic().getStationPreOneHour());
    }

    /**
     * 路段小时流量数据
     */
    @ApiOperation("小时流量-路段")
    @GetMapping("/sectionPreOneHour")
    public AjaxResult sectionPreOneHour()
    {
        return callExternalApiGet(apiProperties.getTraffic().getSectionPreOneHour());
    }

    /**
     * 服务区小时流量数据
     */
    @ApiOperation("小时流量-服务区")
    @GetMapping("/getFlowServiceNew")
    public AjaxResult getFlowServiceNew()
    {
        return callExternalApiGet(apiProperties.getTraffic().getGetFlowServiceNew());
    }
    /**
     * 小时流量-互通
     */
    @ApiOperation("小时流量-互通")
    @GetMapping("/interFlowPreOneHour")
    public AjaxResult interFlowPreOneHour()
    {
        return callExternalApiGet(apiProperties.getTraffic().getInterFlowPreOneHour());
    }

    /**
     * 其他指标-收费站
     */
    @ApiOperation("其他指标-收费站")
    @GetMapping("/stationFlowDetail")
    public AjaxResult stationFlowDetail()
    {
        return callExternalApiGet(apiProperties.getTraffic().getStationFlowDetail());
    }

    /**
     * 数据排行-全区路网总里程
     */
    @ApiOperation("数据排行-全区路网总里程")
    @GetMapping("/dataHighwayLength")
    public AjaxResult dataHighwayLength()
    {
        return callExternalApiGet(apiProperties.getTraffic().getDataHighwayLength());
    }

    /**
     * 数据排行-车流量
     */
    @ApiOperation("数据排行-车流量")
    @GetMapping("/getScreenLeftData")
    public AjaxResult getScreenLeftData()
    {
        return callExternalApiPost(apiProperties.getTraffic().getGetScreenLeftData());
    }


    /**
     * 手动登录外部系统
     */
    @ApiOperation("手动登录外部系统")
    @PostMapping("/login")
    public AjaxResult manualLogin()
    {
        try
        {
            boolean success = authManager.login();
            if (success)
            {
                return success("外部系统登录成功");
            }
            else
            {
                return error("外部系统登录失败");
            }
        }
        catch (Exception e)
        {
            logger.error("手动登录外部系统失败", e);
            return error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 刷新外部系统Token
     */
    @ApiOperation("刷新外部系统Token")
    @PostMapping("/refresh-token")
    public AjaxResult refreshToken()
    {
        try
        {
            boolean success = authManager.refreshToken();
            if (success)
            {
                return success("Token刷新成功");
            }
            else
            {
                return error("Token刷新失败");
            }
        }
        catch (Exception e)
        {
            logger.error("刷新Token失败", e);
            return error("刷新失败: " + e.getMessage());
        }
    }

    /**
     * 清除外部系统Token缓存
     */
    @ApiOperation("清除外部系统Token缓存")
    @DeleteMapping("/clear-token")
    public AjaxResult clearToken()
    {
        try
        {
            authManager.clearToken();
            return success("Token缓存已清除");
        }
        catch (Exception e)
        {
            logger.error("清除Token缓存失败", e);
            return error("清除失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前Token状态
     */
    @ApiOperation("获取当前Token状态")
    @GetMapping("/token-status")
    public AjaxResult getTokenStatus()
    {
        try
        {
            String token = authManager.getValidToken();
            Map<String, Object> status = new HashMap<>();
            status.put("hasToken", StringUtils.isNotEmpty(token));
            status.put("tokenLength", token != null ? token.length() : 0);
            status.put("tokenPrefix", token != null && token.length() > 10 ? token.substring(0, 10) + "..." : token);

            // 添加过期时间信息
            if (StringUtils.isNotEmpty(token))
            {
                try
                {
                    // 直接从Redis获取过期时间
                    String expireTimeStr = redisTemplate.opsForValue().get("external:auth:expire_time");
                    if (StringUtils.isNotEmpty(expireTimeStr))
                    {
                        long expireTime = Long.parseLong(expireTimeStr);
                        long currentTime = System.currentTimeMillis();
                        long remainingTime = expireTime - currentTime;

                        status.put("expireTime", new java.util.Date(expireTime));
                        status.put("remainingMinutes", remainingTime / (1000 * 60));
                        status.put("remainingHours", remainingTime / (1000 * 60 * 60));
                        status.put("willExpireSoon", remainingTime <= 30 * 60 * 1000); // 30分钟内过期
                    }
                }
                catch (Exception ex)
                {
                    logger.warn("获取token过期时间失败: {}", ex.getMessage());
                }
            }

            return success(status);
        }
        catch (Exception e)
        {
            logger.error("获取Token状态失败", e);
            return error("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 测试RSA加密功能
     */
    @ApiOperation("测试RSA加密功能")
    @PostMapping("/test-rsa")
    public AjaxResult testRSAEncrypt(@RequestParam String plainText)
    {
        try
        {
            String encrypted = RSAEncryptUtils.encrypt(plainText);

            Map<String, Object> result = new HashMap<>();
            result.put("plainText", plainText);
            result.put("encrypted", encrypted);
            result.put("encryptSuccess", encrypted != null);

            if (encrypted != null)
            {
                return AjaxResult.success("RSA加密成功", result);
            }
            else
            {
                return AjaxResult.error("RSA加密失败", result);
            }
        }
        catch (Exception e)
        {
            logger.error("RSA加密测试失败", e);
            return error("加密测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 通用的外部API调用处理方法
     * 
     * @param endpoint API端点
     * @param method HTTP方法，默认为GET
     * @return 处理后的响应
     */
    private AjaxResult callExternalApi(String endpoint, String method) 
    {
        try 
        {
            String token = authManager.getValidToken();
            if (StringUtils.isEmpty(token))
            {
                throw new ServiceException("无法获取有效的认证token");
            }
            
            // 构建请求参数
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + token);
            
            ExternalRequestDTO requestDTO = new ExternalRequestDTO();
            requestDTO.setHeaders(headers);
            requestDTO.setEndpoint(endpoint);
            requestDTO.setMethod(method);
            
            // 调用外部系统接口
            JSONObject response = externalSystemService.callExternalWithHeaders(
                    requestDTO.getMethod(),
                    requestDTO.getEndpoint(),
                    requestDTO.getHeaders(),
                    requestDTO.getParams(),
                    requestDTO.getData(),
                    requestDTO.isUseAuth(),
                    requestDTO.isUseCache()
            );

            // 使用优化后的响应处理方法，避免重复包装
            Object result = externalSystemService.processExternalResponseData(response);
            
            // 检查是否是错误结果
            if (result instanceof Map && ((Map<?, ?>) result).containsKey("success") && 
                !((Boolean) ((Map<?, ?>) result).get("success")))
            {
                return error((String) ((Map<?, ?>) result).get("message"));
            }
            else
            {
                return success(result);
            }
        } 
        catch (Exception e) 
        {
            logger.error("调用外部API失败: {}", endpoint, e);
            return error("获取数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 通用的外部API调用处理方法（GET请求）
     * 
     * @param endpoint API端点
     * @return 处理后的响应
     */
    private AjaxResult callExternalApiGet(String endpoint) 
    {
        return callExternalApi(endpoint, "GET");
    }
    
    /**
     * 通用的外部API调用处理方法（POST请求）
     * 
     * @param endpoint API端点
     * @return 处理后的响应
     */
    private AjaxResult callExternalApiPost(String endpoint) 
    {
        return callExternalApi(endpoint, "POST");
    }
} 