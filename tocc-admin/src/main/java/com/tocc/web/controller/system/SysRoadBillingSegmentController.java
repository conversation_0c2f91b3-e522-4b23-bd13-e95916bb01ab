package com.tocc.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.system.domain.SysRoadBillingSegment;
import com.tocc.system.domain.vo.RoadSectionGeometryVO;
import com.tocc.system.service.ISysRoadBillingSegmentService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 高速公路计费路段信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Api(tags = "高速公路计费路段管理")
@RestController
@RequestMapping("/system/road/billing")
public class SysRoadBillingSegmentController extends BaseController
{
    @Autowired
    private ISysRoadBillingSegmentService sysRoadBillingSegmentService;

    /**
     * 查询高速公路计费路段信息列表
     */
    @ApiOperation("查询计费路段列表")
    @PreAuthorize("@ss.hasPermi('system:billing:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysRoadBillingSegment sysRoadBillingSegment)
    {
        startPage();
        List<SysRoadBillingSegment> list = sysRoadBillingSegmentService.selectSysRoadBillingSegmentList(sysRoadBillingSegment);
        return getDataTable(list);
    }

    /**
     * 导出高速公路计费路段信息列表
     */
    @ApiOperation("导出计费路段列表")
    @PreAuthorize("@ss.hasPermi('system:billing:export')")
    @Log(title = "高速公路计费路段信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRoadBillingSegment sysRoadBillingSegment)
    {
        List<SysRoadBillingSegment> list = sysRoadBillingSegmentService.selectSysRoadBillingSegmentList(sysRoadBillingSegment);
        ExcelUtil<SysRoadBillingSegment> util = new ExcelUtil<SysRoadBillingSegment>(SysRoadBillingSegment.class);
        util.exportExcel(response, list, "高速公路计费路段信息数据");
    }

    /**
     * 获取高速公路计费路段信息详细信息
     */
    @ApiOperation("获取计费路段详细信息")
    @PreAuthorize("@ss.hasPermi('system:billing:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysRoadBillingSegmentService.selectSysRoadBillingSegmentById(id));
    }

    /**
     * 新增高速公路计费路段信息
     */
    @ApiOperation("新增计费路段")
    @PreAuthorize("@ss.hasPermi('system:billing:add')")
    @Log(title = "高速公路计费路段信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysRoadBillingSegment sysRoadBillingSegment)
    {
        return toAjax(sysRoadBillingSegmentService.insertSysRoadBillingSegment(sysRoadBillingSegment));
    }

    /**
     * 修改高速公路计费路段信息
     */
    @ApiOperation("修改计费路段")
    @PreAuthorize("@ss.hasPermi('system:billing:edit')")
    @Log(title = "高速公路计费路段信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysRoadBillingSegment sysRoadBillingSegment)
    {
        return toAjax(sysRoadBillingSegmentService.updateSysRoadBillingSegment(sysRoadBillingSegment));
    }

    /**
     * 删除高速公路计费路段信息
     */
    @ApiOperation("删除计费路段")
    @PreAuthorize("@ss.hasPermi('system:billing:remove')")
    @Log(title = "高速公路计费路段信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysRoadBillingSegmentService.deleteSysRoadBillingSegmentByIds(ids));
    }

    /**
     * 获取路段完整几何信息
     * 如果不传参数，返回所有路段的几何信息
     * 如果传入code和sectionName，返回指定路段的几何信息
     */
    @ApiOperation("获取路段完整几何信息")
    @GetMapping("/geometry")
    public AjaxResult getRoadSectionGeometry(
            @RequestParam(required = false) @ApiParam("公路编码（可选）") String code,
            @RequestParam(required = false) @ApiParam("路段名称（可选）") String sectionName,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("是否启用坐标抽稀（默认true）") Boolean simplify) {

        if (code != null && sectionName != null) {
            // 获取指定路段的几何信息
            RoadSectionGeometryVO result = sysRoadBillingSegmentService
                .getRoadSectionGeometry(code, sectionName, simplify);
            return success(result);
        } else {
            // 获取所有路段的几何信息
            List<RoadSectionGeometryVO> results = sysRoadBillingSegmentService
                .getAllRoadSectionGeometry(simplify);
            return success(results);
        }
    }
    
    /**
     * 根据road_marker_id获取几何信息
     */
    @ApiOperation("根据路段ID获取几何信息")
    @GetMapping("/geometry/{roadMarkerId}")
    public AjaxResult getRoadSectionGeometryById(
            @PathVariable Long roadMarkerId,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("是否启用坐标抽稀（默认true）") Boolean simplify) {
        RoadSectionGeometryVO result = sysRoadBillingSegmentService
            .getRoadSectionGeometryById(roadMarkerId, simplify);
        return success(result);
    }

    /**
     * 根据road_marker_id获取计费路段列表
     */
    @ApiOperation("根据路段ID获取计费路段列表")
    @GetMapping("/segments/{roadMarkerId}")
    public AjaxResult getSegmentsByRoadMarkerId(@PathVariable Long roadMarkerId) {
        List<SysRoadBillingSegment> segments = sysRoadBillingSegmentService
            .selectByRoadMarkerId(roadMarkerId);
        return success(segments);
    }
}
