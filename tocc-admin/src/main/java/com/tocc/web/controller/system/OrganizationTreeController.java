package com.tocc.web.controller.system;

import com.tocc.common.annotation.Anonymous;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.system.domain.vo.OrganizationTreeVO;
import com.tocc.system.service.IOrganizationTreeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 组织架构树形结构 Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "组织架构管理")
@RestController
@RequestMapping("/system/organization")
public class OrganizationTreeController extends BaseController {
    
    @Autowired
    private IOrganizationTreeService organizationTreeService;
    
    /**
     * 获取完整的组织架构树形结构
     * 包含部门、岗位、人员信息（名字、联系方式）
     */
    @ApiOperation("获取完整的组织架构树形结构")
    @Anonymous
    @GetMapping("/tree")
    public AjaxResult getOrganizationTree(
            @ApiParam(value = "根部门ID，不传则从顶级部门开始", required = false)
            @RequestParam(value = "deptId", required = false) Long deptId) {
        List<OrganizationTreeVO> tree = organizationTreeService.getOrganizationTree(deptId);
        return success(tree);
    }

    /**
     * 获取指定部门的组织架构树形结构
     */
    @ApiOperation("获取指定部门的组织架构树形结构")
    @GetMapping("/dept/{deptId}")
    public AjaxResult getDepartmentTree(
            @ApiParam(value = "部门ID", required = true)
            @PathVariable Long deptId) {
        List<OrganizationTreeVO> tree = organizationTreeService.getDepartmentTree(deptId);
        return success(tree);
    }

    /**
     * 获取部门下的岗位和人员信息
     */
    @ApiOperation("获取部门下的岗位和人员信息")
    @GetMapping("/dept/{deptId}/posts-users")
    public AjaxResult getDepartmentPostsAndUsers(
            @ApiParam(value = "部门ID", required = true)
            @PathVariable Long deptId) {
        List<OrganizationTreeVO> data = organizationTreeService.getDepartmentPostsAndUsers(deptId);
        return success(data);
    }

    /**
     * 获取单位树形结构（仅单位）
     */
    @ApiOperation("获取单位树形结构")
    @Anonymous
    @GetMapping("/unitTree")
    public AjaxResult getUnitTree(
            @ApiParam(value = "根单位ID，不传则从顶级单位开始", required = false)
            @RequestParam(value = "deptId", required = false) Long deptId) {
        List<OrganizationTreeVO> tree = organizationTreeService.getUnitTree(deptId);
        return success(tree);
    }

    /**
     * 获取部门树形结构（仅部门）
     */
    @ApiOperation("获取部门树形结构")
    @Anonymous
    @GetMapping("/deptTree")
    public AjaxResult getDeptTree(
            @ApiParam(value = "根部门ID，不传则从顶级部门开始", required = false)
            @RequestParam(value = "deptId", required = false) Long deptId) {
        List<OrganizationTreeVO> tree = organizationTreeService.getDeptTree(deptId);
        return success(tree);
    }

    /**
     * 获取单位-部门层级树形结构
     * 单位作为一级节点，部门作为二级节点
     */
    @ApiOperation("获取单位-部门层级树形结构")
    @Anonymous
    @GetMapping("/unitDeptTree")
    public AjaxResult getUnitDeptTree(
            @ApiParam(value = "根节点ID，不传则从顶级开始", required = false)
            @RequestParam(value = "deptId", required = false) Long deptId) {
        List<OrganizationTreeVO> tree = organizationTreeService.getUnitDeptTree(deptId);
        return success(tree);
    }

    /**
     * 获取简化的组织架构树（仅用于下拉选择）
     */
    @ApiOperation("获取简化的组织架构树")
    @GetMapping("/tree/simple")
    public AjaxResult getSimpleOrganizationTree(
            @ApiParam(value = "根部门ID，不传则从顶级部门开始", required = false)
            @RequestParam(value = "deptId", required = false) Long deptId) {
        List<OrganizationTreeVO> tree = organizationTreeService.getOrganizationTree(deptId);
        return success(tree);
    }
}
