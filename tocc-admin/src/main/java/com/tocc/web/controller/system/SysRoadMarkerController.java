package com.tocc.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.system.domain.SysRoadMarker;
import com.tocc.system.domain.vo.RoadMarkerTreeVO;
import com.tocc.system.service.ISysRoadMarkerService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * 公路编号Controller
 * 
 * <AUTHOR>
 * @date 2025-06-02
 * @date 2025-06-02
 */
@Api(tags = "公路编号")
@RestController
@RequestMapping("/system/marker")
public class SysRoadMarkerController extends BaseController
{
    @Autowired
    private ISysRoadMarkerService sysRoadMarkerService;

    /**
     * 查询公路编号列表
     */
    @ApiOperation("查询公路编号列表")
    @PreAuthorize("@ss.hasPermi('system:marker:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysRoadMarker sysRoadMarker)
    {
        startPage();
        List<SysRoadMarker> list = sysRoadMarkerService.selectSysRoadMarkerList(sysRoadMarker);
        return getDataTable(list);
    }

    /**
     * 导出公路编号列表
     */
    @ApiOperation("导出公路编号列表")
    @PreAuthorize("@ss.hasPermi('system:marker:export')")
    @Log(title = "公路编号", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRoadMarker sysRoadMarker)
    {
        List<SysRoadMarker> list = sysRoadMarkerService.selectSysRoadMarkerList(sysRoadMarker);
        ExcelUtil<SysRoadMarker> util = new ExcelUtil<SysRoadMarker>(SysRoadMarker.class);
        util.exportExcel(response, list, "公路编号数据");
    }

    /**
     * 获取公路编号详细信息
     */
    @ApiOperation("获取公路编号详细信息")
    @PreAuthorize("@ss.hasPermi('system:marker:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysRoadMarkerService.selectSysRoadMarkerById(id));
    }

    /**
     * 新增公路编号
     */
    @ApiOperation("新增公路编号")
    @PreAuthorize("@ss.hasPermi('system:marker:add')")
    @Log(title = "公路编号", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysRoadMarker sysRoadMarker)
    {
        return toAjax(sysRoadMarkerService.insertSysRoadMarker(sysRoadMarker));
    }

    /**
     * 修改公路编号
     */
    @ApiOperation("修改公路编号")
    @PreAuthorize("@ss.hasPermi('system:marker:edit')")
    @Log(title = "公路编号", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysRoadMarker sysRoadMarker)
    {
        return toAjax(sysRoadMarkerService.updateSysRoadMarker(sysRoadMarker));
    }

    /**
     * 删除公路编号
     */
    @ApiOperation("删除公路编号")
    @PreAuthorize("@ss.hasPermi('system:marker:remove')")
    @Log(title = "公路编号", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysRoadMarkerService.deleteSysRoadMarkerByIds(ids));
    }

    /**
     * 获取道路类型字典数据
     */
    @ApiOperation("获取道路类型字典数据")
    @GetMapping("/roadTypes")
    public AjaxResult getRoadTypes()
    {
        // 这里可以调用字典服务获取道路类型数据
        // 暂时返回硬编码的数据，后续可以改为调用字典服务
        Map<String, String> roadTypes = new HashMap<>();
        roadTypes.put("1", "高速公路");
        roadTypes.put("2", "国省干道");
        roadTypes.put("3", "县乡公路");
        roadTypes.put("4", "城市道路");

        return success(roadTypes);
    }

    /**
     * 获取公路编号树形结构
     */
    @ApiOperation("获取公路编号树形结构")
    @PreAuthorize("@ss.hasPermi('system:marker:list')")
    @GetMapping("/tree")
    public AjaxResult getTree()
    {
        List<RoadMarkerTreeVO> tree = sysRoadMarkerService.getRoadMarkerTree();
        return success(tree);
    }
}
