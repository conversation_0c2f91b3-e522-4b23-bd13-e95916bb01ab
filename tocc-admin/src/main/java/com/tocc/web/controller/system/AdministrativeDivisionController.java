package com.tocc.web.controller.system;

import com.tocc.common.annotation.Anonymous;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.domain.vo.AdministrativeDivisionVO;
import com.tocc.system.service.IAdministrativeDivisionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 行政区划 Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "行政区划管理")
@RestController
@RequestMapping("/system/division")
public class AdministrativeDivisionController extends BaseController {
    
    @Autowired
    private IAdministrativeDivisionService divisionService;
    
    /**
     * 获取完整的行政区划树形结构
     */
    @ApiOperation("获取完整的行政区划树形结构")
    @Anonymous
    @GetMapping("/tree")
    public AjaxResult getDivisionTree() {
        List<AdministrativeDivisionVO> tree = divisionService.getDivisionTree();
        return success(tree);
    }

    /**
     * 根据父级ID获取子级行政区划
     */
    @ApiOperation("根据父级ID获取子级行政区划")
    @Anonymous
    @GetMapping("/children")
    public AjaxResult getChildren(
            @ApiParam(value = "父级ID，不传或传0获取省级", required = false)
            @RequestParam(value = "pid", required = false, defaultValue = "0") String pid) {
        List<AdministrativeDivisionVO> children = divisionService.getChildrenByPid(pid);
        return success(children);
    }

    /**
     * 根据层级获取行政区划
     */
    @ApiOperation("根据层级获取行政区划")
    @Anonymous
    @GetMapping("/level/{deep}")
    public AjaxResult getDivisionsByLevel(
            @ApiParam(value = "层级深度 (0-省/直辖市, 1-市, 2-区/县, 3-街道/乡镇)", required = true)
            @PathVariable Integer deep) {
        List<AdministrativeDivisionVO> divisions = divisionService.getDivisionsByDeep(deep);
        return success(divisions);
    }

    /**
     * 根据ID获取行政区划详情
     */
    @ApiOperation("根据ID获取行政区划详情")
    @Anonymous
    @GetMapping("/{id}")
    public AjaxResult getDivisionById(
            @ApiParam(value = "行政区划ID", required = true)
            @PathVariable String id) {
        AdministrativeDivisionVO division = divisionService.getDivisionById(id);
        return success(division);
    }
    
    /**
     * 根据名称搜索行政区划
     */
    @ApiOperation("根据名称搜索行政区划")
    @Anonymous
    @GetMapping("/search")
    public AjaxResult searchDivisions(
            @ApiParam(value = "名称关键字", required = true)
            @RequestParam String name) {
        List<AdministrativeDivisionVO> divisions = divisionService.searchDivisionsByName(name);
        return success(divisions);
    }

    /**
     * 获取指定ID的完整路径
     */
    @ApiOperation("获取指定ID的完整路径")
    @Anonymous
    @GetMapping("/path/{id}")
    public AjaxResult getDivisionPath(
            @ApiParam(value = "行政区划ID", required = true)
            @PathVariable String id) {
        List<AdministrativeDivisionVO> path = divisionService.getDivisionPath(id);
        return success(path);
    }

    /**
     * 获取省级行政区划列表
     */
    @ApiOperation("获取省级行政区划列表")
    @Anonymous
    @GetMapping("/provinces")
    public AjaxResult getProvinces() {
        List<AdministrativeDivisionVO> provinces = divisionService.getProvinces();
        return success(provinces);
    }
    
    /**
     * 获取指定省份下的市级行政区划
     */
    @ApiOperation("获取指定省份下的市级行政区划")
    @Anonymous
    @GetMapping("/cities/{provinceId}")
    public AjaxResult getCitiesByProvince(
            @ApiParam(value = "省份ID", required = true)
            @PathVariable String provinceId) {
        List<AdministrativeDivisionVO> cities = divisionService.getCitiesByProvince(provinceId);
        return success(cities);
    }

    /**
     * 获取指定市下的区县级行政区划
     */
    @ApiOperation("获取指定市下的区县级行政区划")
    @Anonymous
    @GetMapping("/districts/{cityId}")
    public AjaxResult getDistrictsByCity(
            @ApiParam(value = "市ID", required = true)
            @PathVariable String cityId) {
        List<AdministrativeDivisionVO> districts = divisionService.getDistrictsByCity(cityId);
        return success(districts);
    }

    /**
     * 获取指定区县下的街道/乡镇级行政区划
     */
    @ApiOperation("获取指定区县下的街道/乡镇级行政区划")
    @Anonymous
    @GetMapping("/streets/{districtId}")
    public AjaxResult getStreetsByDistrict(
            @ApiParam(value = "区县ID", required = true)
            @PathVariable String districtId) {
        List<AdministrativeDivisionVO> streets = divisionService.getStreetsByDistrict(districtId);
        return success(streets);
    }
}
