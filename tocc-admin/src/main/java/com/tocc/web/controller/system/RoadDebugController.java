package com.tocc.web.controller.system;

import com.alibaba.fastjson2.JSON;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.system.mapper.SysRoadBillingSegmentMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 道路数据调试Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "道路数据调试")
@RestController
@RequestMapping("/debug/road")
public class RoadDebugController extends BaseController {

    @Autowired
    private SysRoadBillingSegmentMapper roadBillingSegmentMapper;

    /**
     * 调试路段几何信息解析
     */
    @ApiOperation("调试路段几何信息解析")
    @GetMapping("/debug-geometry")
    public AjaxResult debugGeometry(
            @RequestParam String code,
            @RequestParam String sectionName) {
        
        try {
            // 1. 查询原始数据
            List<Map<String, Object>> segments = roadBillingSegmentMapper
                .selectGeometryByCodeAndSection(code, sectionName);
            
            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("totalSegments", segments.size());
            
            List<Map<String, Object>> segmentDetails = new ArrayList<>();
            
            for (Map<String, Object> segment : segments) {
                Map<String, Object> detail = new HashMap<>();
                detail.put("id", segment.get("id"));
                detail.put("name", segment.get("segment_name"));
                detail.put("direction", segment.get("direction"));
                detail.put("km1", segment.get("km1"));
                detail.put("km2", segment.get("km2"));
                
                String geomStr = (String) segment.get("geom");
                detail.put("geomStr", geomStr);
                
                if (geomStr != null && !geomStr.isEmpty()) {
                    try {
                        // 解析几何信息
                        Map<String, Object> geometry = JSON.parseObject(geomStr, Map.class);
                        detail.put("geometryType", geometry.get("type"));
                        
                        @SuppressWarnings("unchecked")
                        List<List<Object>> rawCoordinates = (List<List<Object>>) geometry.get("coordinates");
                        
                        if (rawCoordinates != null && !rawCoordinates.isEmpty()) {
                            detail.put("coordinateCount", rawCoordinates.size());
                            
                            // 检查第一个坐标点的类型
                            List<Object> firstCoord = rawCoordinates.get(0);
                            List<String> coordTypes = new ArrayList<>();
                            for (Object value : firstCoord) {
                                coordTypes.add(value != null ? value.getClass().getSimpleName() : "null");
                            }
                            detail.put("firstCoordinate", firstCoord);
                            detail.put("coordinateTypes", coordTypes);
                            
                            // 尝试转换坐标
                            List<List<Double>> convertedCoords = convertCoordinates(rawCoordinates);
                            detail.put("convertedCoordinates", convertedCoords);
                            detail.put("conversionSuccess", true);
                        } else {
                            detail.put("coordinateCount", 0);
                            detail.put("conversionSuccess", false);
                            detail.put("error", "坐标数据为空");
                        }
                        
                    } catch (Exception e) {
                        detail.put("conversionSuccess", false);
                        detail.put("error", e.getMessage());
                        detail.put("errorType", e.getClass().getSimpleName());
                    }
                } else {
                    detail.put("conversionSuccess", false);
                    detail.put("error", "几何信息为空");
                }
                
                segmentDetails.add(detail);
            }
            
            debugInfo.put("segments", segmentDetails);
            
            return success(debugInfo);
            
        } catch (Exception e) {
            logger.error("调试几何信息时发生异常", e);
            return error("调试失败: " + e.getMessage());
        }
    }
    
    /**
     * 转换坐标类型
     */
    private List<List<Double>> convertCoordinates(List<List<Object>> rawCoordinates) {
        List<List<Double>> result = new ArrayList<>();
        
        for (List<Object> rawCoordinate : rawCoordinates) {
            List<Double> coordinate = new ArrayList<>();
            for (Object value : rawCoordinate) {
                if (value instanceof Number) {
                    coordinate.add(((Number) value).doubleValue());
                } else {
                    try {
                        coordinate.add(Double.parseDouble(value.toString()));
                    } catch (NumberFormatException e) {
                        coordinate.add(0.0);
                    }
                }
            }
            result.add(coordinate);
        }
        
        return result;
    }
}
