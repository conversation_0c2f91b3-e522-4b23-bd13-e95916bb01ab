package com.tocc.web.drillPlanController;


import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.domain.dto.DrillDataDto;
import com.tocc.domain.dto.DrillPlanDto;
import com.tocc.domain.dto.DrillReviewReportDto;
import com.tocc.service.IDrillDataService;
import com.tocc.service.IDrillPlanService;
import com.tocc.service.IDrillReviewReportService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/drill")
@RequiredArgsConstructor
public class DrillPlanController extends BaseController {

    private final IDrillPlanService drillPlanService;

    private final IDrillDataService drillDataService;

    private final IDrillReviewReportService drillReviewReportService;

    @PostMapping("/plan/page")
    @ApiOperation(value = "分页")
    public TableDataInfo drillPlanPage(@RequestBody DrillPlanDto dto){
        startPage();
        return getDataTable(drillPlanService.drillPlanListVO(dto));
    }


    @PutMapping("/plan/addOrUpdate")
    @ApiOperation(value = "新增或修改演练计划")
    public AjaxResult saveOrUpdateEntity(@RequestBody DrillPlanDto dto){
        return AjaxResult.success(drillPlanService.insertEntity(dto));
    }

    @PutMapping("/data/addOrUpdate")
    @ApiOperation(value = "新增或修改演练资料")
    public AjaxResult saveEntity(@RequestBody DrillDataDto dto){
        return AjaxResult.success(drillDataService.saveEntity(dto));
    }

    @PutMapping("/review/addOrUpdate")
    @ApiOperation(value = "新增或修改复盘资料")
    public AjaxResult saveEntity(@RequestBody DrillReviewReportDto dto){
        return AjaxResult.success(drillReviewReportService.saveEntity(dto));
    }

    @GetMapping("/data/{id}")
    @ApiOperation(value = "演练资料详情查看")
    public AjaxResult drillDataDetail(@PathVariable Long id){
        return AjaxResult.success(drillPlanService.drillDataDetail(id));
    }

    @GetMapping("/review/{id}")
    @ApiOperation(value = "复盘报告详情查看")
    public AjaxResult DrillReviewReportDetail(@PathVariable Long id){
        return AjaxResult.success(drillPlanService.DrillReviewReportDetail(id));
    }

    @GetMapping("/static")
    @ApiOperation(value = "复盘报告详情查看")
    public AjaxResult DrillReviewReportDetail(){
        return AjaxResult.success(drillPlanService.staticDetail());
    }

    @DeleteMapping("/plan")
    @ApiOperation(value = "实战演练删除")
    public AjaxResult deleteByIds(@RequestBody List<Long> ids){
        return AjaxResult.success(drillPlanService.deleteByIds(ids));
    }
}
