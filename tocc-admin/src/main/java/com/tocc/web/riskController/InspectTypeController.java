package com.tocc.web.riskController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.risk.domain.InspectType;
import com.tocc.risk.service.IInspectTypeService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 检查类别Controller
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags = "检查类别")
@RestController
@RequestMapping("/risk/type")
public class InspectTypeController extends BaseController
{
    @Autowired
    private IInspectTypeService inspectTypeService;

    /**
     * 查询检查类别列表
     */
    @ApiOperation("获取检查类别列表")
//    @PreAuthorize("@ss.hasPermi('system:type:list')")
    @GetMapping("/list")
    public TableDataInfo list(InspectType inspectType)
    {
        startPage();
        List<InspectType> list = inspectTypeService.selectInspectTypeList(inspectType);
        return getDataTable(list);
    }


    /**
     * 查询检查类别列表
     */
    @ApiOperation("获取检查类别树")
//    @PreAuthorize("@ss.hasPermi('system:type:list')")
    @GetMapping("/treeList")
    public TableDataInfo treeList()
    {
        List<InspectType> list = inspectTypeService.treeList();
        return getDataTable(list);
    }


    /**
     * 导出检查类别列表
     */
    @ApiOperation("导出检查类别列表")
//    @PreAuthorize("@ss.hasPermi('system:type:export')")
    @Log(title = "检查类别", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InspectType inspectType)
    {
        List<InspectType> list = inspectTypeService.selectInspectTypeList(inspectType);
        ExcelUtil<InspectType> util = new ExcelUtil<InspectType>(InspectType.class);
        util.exportExcel(response, list, "检查类别数据");
    }

    /**
     * 获取检查类别详细信息
     */
    @ApiOperation("获取检查类别详情")
//    @PreAuthorize("@ss.hasPermi('system:type:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inspectTypeService.selectInspectTypeById(id));
    }

    /**
     * 新增检查类别
     */
    @ApiOperation("新增检查类别")
//    @PreAuthorize("@ss.hasPermi('system:type:add')")
    @Log(title = "检查类别", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody InspectType inspectType)
    {
        return toAjax(inspectTypeService.insertInspectType(inspectType));
    }

    /**
     * 修改检查类别
     */
    @ApiOperation("修改检查类别")
//    @PreAuthorize("@ss.hasPermi('system:type:edit')")
    @Log(title = "检查类别", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody InspectType inspectType)
    {
        return toAjax(inspectTypeService.updateInspectType(inspectType));
    }

    /**
     * 删除检查类别
     */
    @ApiOperation("删除检查类别")
//    @PreAuthorize("@ss.hasPermi('system:type:remove')")
    @Log(title = "检查类别", businessType = BusinessType.DELETE)
	@PostMapping("/remove")
    public AjaxResult remove(@RequestBody InspectType type)
    {
        return toAjax(inspectTypeService.deleteInspectTypeById(type.getId()));
    }
}
