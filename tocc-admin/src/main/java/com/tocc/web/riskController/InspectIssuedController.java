package com.tocc.web.riskController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tocc.risk.domain.InspectTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.risk.domain.InspectIssued;
import com.tocc.risk.service.IInspectIssuedService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 检查下发Controller
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags =  "检查下发")
@RestController
@RequestMapping("/risk/issued")
public class InspectIssuedController extends BaseController
{
    @Autowired
    private IInspectIssuedService inspectIssuedService;

    /**
     * 查询检查下发列表
     */
    @ApiOperation("获取检查下发列表")
//    @PreAuthorize("@ss.hasPermi('system:issued:list')")
    @GetMapping("/list")
    public TableDataInfo list(InspectIssued inspectIssued)
    {
        startPage();
        List<InspectIssued> list = inspectIssuedService.selectInspectIssuedList(inspectIssued);
        return getDataTable(list);
    }

    /**
     * 查询检查下发列表
     */
    @ApiOperation("获取进度")
//    @PreAuthorize("@ss.hasPermi('system:issued:list')")
    @GetMapping("/getProgress")
    public AjaxResult getProgress(InspectIssued inspectIssued)
    {
        List<InspectTask> list = inspectIssuedService.getProgress(inspectIssued);
        return success(list);
    }

    /**
     * 导出检查下发列表
     */
    @ApiOperation("导出检查下发列表")
//    @PreAuthorize("@ss.hasPermi('system:issued:export')")
    @Log(title = "检查下发", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InspectIssued inspectIssued)
    {
        List<InspectIssued> list = inspectIssuedService.selectInspectIssuedList(inspectIssued);
        ExcelUtil<InspectIssued> util = new ExcelUtil<InspectIssued>(InspectIssued.class);
        util.exportExcel(response, list, "检查下发数据");
    }

    /**
     * 获取检查下发详细信息
     */
    @ApiOperation("获取检查下发详情")
//    @PreAuthorize("@ss.hasPermi('system:issued:query')")
    @GetMapping(value = "/getInfo/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inspectIssuedService.selectInspectIssuedById(id));
    }

    /**
     * 新增检查下发
     */
    @ApiOperation("下发任务")
//    @PreAuthorize("@ss.hasPermi('system:issued:add')")
    @Log(title = "检查下发", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody InspectIssued inspectIssued)
    {
        return toAjax(inspectIssuedService.insertInspectIssued(inspectIssued));
    }

    /**
     * 修改检查下发
     */
    @ApiOperation("修改检查下发")
//    @PreAuthorize("@ss.hasPermi('system:issued:edit')")
    @Log(title = "检查下发", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody InspectIssued inspectIssued)
    {
        return toAjax(inspectIssuedService.updateInspectIssued(inspectIssued));
    }

    /**
     * 删除检查下发
     */
    @ApiOperation("删除检查下发")
//    @PreAuthorize("@ss.hasPermi('system:issued:remove')")
    @Log(title = "检查下发", businessType = BusinessType.DELETE)
	@DeleteMapping("/remove")
    public AjaxResult remove(@RequestBody InspectIssued issued)
    {
        return toAjax(inspectIssuedService.deleteInspectIssuedById(issued.getId()));
    }
}
