package com.tocc.web.dispatchController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.domain.DispatchMeeting;
import com.tocc.service.IDispatchMeetingService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 现场指挥协调会议Controller
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Api("现场指挥协调会议")
@RestController
@RequestMapping("/dispatch/meeting")
public class DispatchMeetingController extends BaseController {

    @Autowired
    private IDispatchMeetingService dispatchMeetingService;

    /**
     * 查询现场指挥协调会议列表
     */
    @ApiOperation("查询现场指挥协调会议列表")
    @PreAuthorize("@ss.hasPermi('dispatch:meeting:list')")
    @GetMapping("/list")
    public TableDataInfo list(DispatchMeeting dispatchMeeting) {
        startPage();
        List<DispatchMeeting> list = dispatchMeetingService.selectDispatchMeetingList(dispatchMeeting);
        return getDataTable(list);
    }

    /**
     * 导出现场指挥协调会议列表
     */
    @ApiOperation("导出现场指挥协调会议列表")
    @PreAuthorize("@ss.hasPermi('dispatch:meeting:export')")
    @Log(title = "现场指挥协调会议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DispatchMeeting dispatchMeeting) {
        List<DispatchMeeting> list = dispatchMeetingService.selectDispatchMeetingList(dispatchMeeting);
        ExcelUtil<DispatchMeeting> util = new ExcelUtil<DispatchMeeting>(DispatchMeeting.class);
        util.exportExcel(response, list, "现场指挥协调会议数据");
    }

    /**
     * 获取现场指挥协调会议详细信息
     */
    @ApiOperation("获取现场指挥协调会议详细信息")
    @PreAuthorize("@ss.hasPermi('dispatch:meeting:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dispatchMeetingService.selectDispatchMeetingById(id));
    }

    /**
     * 新增现场指挥协调会议
     */
    @ApiOperation("新增现场指挥协调会议")
    @PreAuthorize("@ss.hasPermi('dispatch:meeting:add')")
    @Log(title = "现场指挥协调会议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DispatchMeeting dispatchMeeting) {
        String token = dispatchMeetingService.insertDispatchMeeting(dispatchMeeting);
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        return AjaxResult.success(result);
    }

    /**
     * 修改现场指挥协调会议
     */
    @ApiOperation("修改现场指挥协调会议")
    @PreAuthorize("@ss.hasPermi('dispatch:meeting:edit')")
    @Log(title = "现场指挥协调会议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DispatchMeeting dispatchMeeting) {
        return toAjax(dispatchMeetingService.updateDispatchMeeting(dispatchMeeting));
    }

    /**
     * 删除现场指挥协调会议
     */
    @ApiOperation("删除现场指挥协调会议")
    @PreAuthorize("@ss.hasPermi('dispatch:meeting:remove')")
    @Log(title = "现场指挥协调会议", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dispatchMeetingService.deleteDispatchMeetingByIds(ids));
    }

    @ApiOperation("获取声网token")
    @GetMapping("/token")
    public AjaxResult getTokenWithAccount(@RequestParam Long meetingId) {
        String token = dispatchMeetingService.generateTokenWithAccount(meetingId);
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        return AjaxResult.success(result);
    }
}
