package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.em.domain.EmPrePlanDeptUser;
import com.tocc.em.service.IEmPrePlanDeptUserService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 预案组织体系人员Controller
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags ="预案组织体系人员")
@RestController
@RequestMapping("/em/prePlanDeptUser")
public class EmPrePlanDeptUserController extends BaseController
{
    @Autowired
    private IEmPrePlanDeptUserService emPrePlanDeptUserService;

    /**
     * 查询预案组织体系人员列表
     */
    @ApiOperation("查询预案组织体系人员列表")
    @PreAuthorize("@ss.hasPermi('em:PrePlanDeptUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmPrePlanDeptUser emPrePlanDeptUser)
    {
        startPage();
        List<EmPrePlanDeptUser> list = emPrePlanDeptUserService.selectEmPrePlanDeptUserList(emPrePlanDeptUser);
        return getDataTable(list);
    }

    /**
     * 导出预案组织体系人员列表
     */
    @ApiOperation("导出预案组织体系人员列表")
    @PreAuthorize("@ss.hasPermi('em:PrePlanDeptUser:export')")
    @Log(title = "预案组织体系人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmPrePlanDeptUser emPrePlanDeptUser)
    {
        List<EmPrePlanDeptUser> list = emPrePlanDeptUserService.selectEmPrePlanDeptUserList(emPrePlanDeptUser);
        ExcelUtil<EmPrePlanDeptUser> util = new ExcelUtil<EmPrePlanDeptUser>(EmPrePlanDeptUser.class);
        util.exportExcel(response, list, "预案组织体系人员数据");
    }

    /**
     * 获取预案组织体系人员详细信息
     */
    @ApiOperation("获取预案组织体系人员详细信息")
    @PreAuthorize("@ss.hasPermi('em:PrePlanDeptUser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(emPrePlanDeptUserService.selectEmPrePlanDeptUserById(id));
    }

    /**
     * 新增预案组织体系人员
     */
    @ApiOperation("新增预案组织体系人员")
    @PreAuthorize("@ss.hasPermi('em:PrePlanDeptUser:add')")
    @Log(title = "预案组织体系人员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmPrePlanDeptUser emPrePlanDeptUser)
    {
        return toAjax(emPrePlanDeptUserService.insertEmPrePlanDeptUser(emPrePlanDeptUser));
    }

    /**
     * 修改预案组织体系人员
     */
    @ApiOperation("修改预案组织体系人员")
    @PreAuthorize("@ss.hasPermi('em:PrePlanDeptUser:edit')")
    @Log(title = "预案组织体系人员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmPrePlanDeptUser emPrePlanDeptUser)
    {
        return toAjax(emPrePlanDeptUserService.updateEmPrePlanDeptUser(emPrePlanDeptUser));
    }

    /**
     * 删除预案组织体系人员
     */
    @ApiOperation("删除预案组织体系人员")
    @PreAuthorize("@ss.hasPermi('em:PrePlanDeptUser:remove')")
    @Log(title = "预案组织体系人员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(emPrePlanDeptUserService.deleteEmPrePlanDeptUserByIds(ids));
    }
}
