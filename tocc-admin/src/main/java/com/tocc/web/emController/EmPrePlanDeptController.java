package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.em.domain.EmPrePlanDept;
import com.tocc.em.service.IEmPrePlanDeptService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 应急预案组织体系Controller
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags  ="应急预案组织体系")
@RestController
@RequestMapping("/em/prePlanDept")
public class EmPrePlanDeptController extends BaseController
{
    @Autowired
    private IEmPrePlanDeptService emPrePlanDeptService;

    /**
     * 查询应急预案组织体系列表
     */
    @ApiOperation("查询应急预案组织体系列表")
    @PreAuthorize("@ss.hasPermi('em:prePlanDept:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmPrePlanDept emPrePlanDept)
    {
        startPage();
        List<EmPrePlanDept> list = emPrePlanDeptService.selectEmPrePlanDeptList(emPrePlanDept);
        return getDataTable(list);
    }

    /**
     * 导出应急预案组织体系列表
     */
    @ApiOperation("导出应急预案组织体系列表")
    @PreAuthorize("@ss.hasPermi('em:prePlanDept:export')")
    @Log(title = "应急预案组织体系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmPrePlanDept emPrePlanDept)
    {
        List<EmPrePlanDept> list = emPrePlanDeptService.selectEmPrePlanDeptList(emPrePlanDept);
        ExcelUtil<EmPrePlanDept> util = new ExcelUtil<EmPrePlanDept>(EmPrePlanDept.class);
        util.exportExcel(response, list, "应急预案组织体系数据");
    }

    /**
     * 获取应急预案组织体系详细信息
     */
    @ApiOperation("获取应急预案组织体系详细信息")
    @PreAuthorize("@ss.hasPermi('em:prePlanDept:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(emPrePlanDeptService.selectEmPrePlanDeptById(id));
    }

    /**
     * 新增应急预案组织体系
     */
    @ApiOperation("新增应急预案组织体系")
    @PreAuthorize("@ss.hasPermi('em:prePlanDept:add')")
    @Log(title = "应急预案组织体系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmPrePlanDept emPrePlanDept)
    {
        return toAjax(emPrePlanDeptService.insertEmPrePlanDept(emPrePlanDept));
    }

    /**
     * 修改应急预案组织体系
     */
    @ApiOperation("修改应急预案组织体系")
    @PreAuthorize("@ss.hasPermi('em:prePlanDept:edit')")
    @Log(title = "应急预案组织体系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmPrePlanDept emPrePlanDept)
    {
        return toAjax(emPrePlanDeptService.updateEmPrePlanDept(emPrePlanDept));
    }

    /**
     * 删除应急预案组织体系
     */
    @ApiOperation("删除应急预案组织体系")
    @PreAuthorize("@ss.hasPermi('em:prePlanDept:remove')")
    @Log(title = "应急预案组织体系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(emPrePlanDeptService.deleteEmPrePlanDeptByIds(ids));
    }
}
