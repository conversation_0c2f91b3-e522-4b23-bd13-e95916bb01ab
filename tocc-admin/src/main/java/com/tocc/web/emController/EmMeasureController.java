package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.em.domain.EmMeasure;
import com.tocc.em.service.IEmMeasureService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 应急响应处置措施Controller
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags  ="应急响应处置措施")
@RestController
@RequestMapping("/em/measure")
public class EmMeasureController extends BaseController
{
    @Autowired
    private IEmMeasureService emMeasureService;

    /**
     * 查询应急响应处置措施列表
     */
    @ApiOperation("查询应急响应处置措施列表")
    @PreAuthorize("@ss.hasPermi('em:EmMeasure:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmMeasure emMeasure)
    {
        startPage();
        List<EmMeasure> list = emMeasureService.selectEmMeasureList(emMeasure);
        return getDataTable(list);
    }

    /**
     * 导出应急响应处置措施列表
     */
    @ApiOperation("导出应急响应处置措施列表")
    @PreAuthorize("@ss.hasPermi('em:EmMeasure:export')")
    @Log(title = "应急响应处置措施", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmMeasure emMeasure)
    {
        List<EmMeasure> list = emMeasureService.selectEmMeasureList(emMeasure);
        ExcelUtil<EmMeasure> util = new ExcelUtil<EmMeasure>(EmMeasure.class);
        util.exportExcel(response, list, "应急响应处置措施数据");
    }

    /**
     * 获取应急响应处置措施详细信息
     */
    @ApiOperation("获取应急响应处置措施详细信息")
    @PreAuthorize("@ss.hasPermi('em:EmMeasure:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(emMeasureService.selectEmMeasureById(id));
    }

    /**
     * 新增应急响应处置措施
     */
    @ApiOperation("新增应急响应处置措施")
    @PreAuthorize("@ss.hasPermi('em:EmMeasure:add')")
    @Log(title = "应急响应处置措施", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmMeasure emMeasure)
    {
        return toAjax(emMeasureService.insertEmMeasure(emMeasure));
    }

    /**
     * 修改应急响应处置措施
     */
    @ApiOperation("修改应急响应处置措施")
    @PreAuthorize("@ss.hasPermi('em:EmMeasure:edit')")
    @Log(title = "应急响应处置措施", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmMeasure emMeasure)
    {
        return toAjax(emMeasureService.updateEmMeasure(emMeasure));
    }

    /**
     * 删除应急响应处置措施
     */
    @ApiOperation("删除应急响应处置措施")
    @PreAuthorize("@ss.hasPermi('em:EmMeasure:remove')")
    @Log(title = "应急响应处置措施", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(emMeasureService.deleteEmMeasureByIds(ids));
    }
}
