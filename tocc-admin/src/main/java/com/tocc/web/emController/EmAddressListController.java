package com.tocc.web.emController;

import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.domain.dto.EmergencyEventDTO;
import com.tocc.domain.vo.EmergencyEventVO;
import com.tocc.em.dto.EmAddressListDTO;
import com.tocc.em.qo.SysUserQO;
import com.tocc.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "应急通讯录-暂时保留")
@RestController
@RequestMapping("/em/AddressLis")
public class EmAddressListController extends BaseController {

    @Autowired
    ISysUserService iSysUserService;
    /**
     * 查询应急事件列表
//     */
    @ApiOperation("查询应急通讯录列表")
    @GetMapping("/list")
    public TableDataInfo list(SysUserQO sysUserQO) {
        startPage();
        List<SysUser> list = iSysUserService.selecteAddressList(sysUserQO);
        return getDataTable(list);
    }
}
