package com.tocc.web.emController;

import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.domain.vo.WarehouseCircleVO;
import com.tocc.domain.vo.RescueTeamCircleVO;
import com.tocc.service.IRescueCircleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 应急救援圈Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "应急救援圈")
@RestController
@RequestMapping("/rescue/circle")
public class RescueCircleController extends BaseController {
    
    @Autowired
    private IRescueCircleService rescueCircleService;
    
    /**
     * 根据应急事件ID获取仓库救援圈信息
     */
    @ApiOperation("根据应急事件ID获取仓库救援圈信息")
    @PreAuthorize("@ss.hasPermi('rescue:circle:query')")
    @GetMapping("/warehouse/event/{eventId}")
    public AjaxResult getWarehouseCircleByEventId(
            @ApiParam(value = "应急事件ID", required = true)
            @PathVariable String eventId) {
        WarehouseCircleVO warehouseCircle = rescueCircleService.getWarehouseCircleByEventId(eventId);
        if (warehouseCircle == null) {
            return error("未找到应急事件或事件缺少坐标信息");
        }
        return success(warehouseCircle);
    }

    /**
     * 根据坐标获取仓库救援圈信息
     */
    @ApiOperation("根据坐标获取仓库救援圈信息")
    @PreAuthorize("@ss.hasPermi('rescue:circle:query')")
    @GetMapping("/warehouse/coordinates")
    public AjaxResult getWarehouseCircleByCoordinates(
            @ApiParam(value = "经度", required = true)
            @RequestParam BigDecimal longitude,
            @ApiParam(value = "纬度", required = true)
            @RequestParam BigDecimal latitude) {

        if (longitude == null || latitude == null) {
            return error("经纬度参数不能为空");
        }

        WarehouseCircleVO warehouseCircle = rescueCircleService.getWarehouseCircleByCoordinates(longitude, latitude);
        return success(warehouseCircle);
    }

    /**
     * 根据应急事件ID获取救援队伍救援圈信息
     */
    @ApiOperation("根据应急事件ID获取救援队伍救援圈信息")
    @PreAuthorize("@ss.hasPermi('rescue:circle:query')")
    @GetMapping("/team/event/{eventId}")
    public AjaxResult getRescueTeamCircleByEventId(
            @ApiParam(value = "应急事件ID", required = true)
            @PathVariable String eventId) {
        RescueTeamCircleVO rescueTeamCircle = rescueCircleService.getRescueTeamCircleByEventId(eventId);
        if (rescueTeamCircle == null) {
            return error("未找到应急事件或事件缺少坐标信息");
        }
        return success(rescueTeamCircle);
    }

    /**
     * 根据坐标获取救援队伍救援圈信息
     */
    @ApiOperation("根据坐标获取救援队伍救援圈信息")
    @PreAuthorize("@ss.hasPermi('rescue:circle:query')")
    @GetMapping("/team/coordinates")
    public AjaxResult getRescueTeamCircleByCoordinates(
            @ApiParam(value = "经度", required = true)
            @RequestParam BigDecimal longitude,
            @ApiParam(value = "纬度", required = true)
            @RequestParam BigDecimal latitude) {

        if (longitude == null || latitude == null) {
            return error("经纬度参数不能为空");
        }

        RescueTeamCircleVO rescueTeamCircle = rescueCircleService.getRescueTeamCircleByCoordinates(longitude, latitude);
        return success(rescueTeamCircle);
    }
}
