package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.em.domain.EmPrePlanRecord;
import com.tocc.em.service.IEmPrePlanRecordService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 应急预案数据历史Controller
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags ="应急预案数据历史")
@RestController
@RequestMapping("/em/prePlanRecord")
public class EmPrePlanRecordController extends BaseController
{
    @Autowired
    private IEmPrePlanRecordService emPrePlanRecordService;

    /**
     * 查询应急预案数据历史列表
     */
    @ApiOperation("查询应急预案数据历史列表")
    @PreAuthorize("@ss.hasPermi('em:EmPrePlanRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmPrePlanRecord emPrePlanRecord)
    {
        startPage();
        List<EmPrePlanRecord> list = emPrePlanRecordService.selectEmPrePlanRecordList(emPrePlanRecord);
        return getDataTable(list);
    }

    /**
     * 导出应急预案数据历史列表
     */
    @ApiOperation("导出应急预案数据历史列表")
    @PreAuthorize("@ss.hasPermi('em:EmPrePlanRecord:export')")
    @Log(title = "应急预案数据历史", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmPrePlanRecord emPrePlanRecord)
    {
        List<EmPrePlanRecord> list = emPrePlanRecordService.selectEmPrePlanRecordList(emPrePlanRecord);
        ExcelUtil<EmPrePlanRecord> util = new ExcelUtil<EmPrePlanRecord>(EmPrePlanRecord.class);
        util.exportExcel(response, list, "应急预案数据历史数据");
    }

    /**
     * 获取应急预案数据历史详细信息
     */
    @ApiOperation("获取应急预案数据历史详细信息")
    @PreAuthorize("@ss.hasPermi('em:EmPrePlanRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(emPrePlanRecordService.selectEmPrePlanRecordById(id));
    }

    /**
     * 新增应急预案数据历史
     */
    @ApiOperation("新增应急预案数据历史")
    @PreAuthorize("@ss.hasPermi('em:EmPrePlanRecord:add')")
    @Log(title = "应急预案数据历史", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmPrePlanRecord emPrePlanRecord)
    {
        return toAjax(emPrePlanRecordService.insertEmPrePlanRecord(emPrePlanRecord));
    }

    /**
     * 修改应急预案数据历史
     */
    @ApiOperation("修改应急预案数据历史")
    @PreAuthorize("@ss.hasPermi('em:EmPrePlanRecord:edit')")
    @Log(title = "应急预案数据历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmPrePlanRecord emPrePlanRecord)
    {
        return toAjax(emPrePlanRecordService.updateEmPrePlanRecord(emPrePlanRecord));
    }

    /**
     * 删除应急预案数据历史
     */
    @ApiOperation("删除应急预案数据历史")
    @PreAuthorize("@ss.hasPermi('em:EmPrePlanRecord:remove')")
    @Log(title = "应急预案数据历史", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(emPrePlanRecordService.deleteEmPrePlanRecordByIds(ids));
    }
}
