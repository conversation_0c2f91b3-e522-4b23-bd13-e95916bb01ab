package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.em.domain.EmPrePlanFile;
import com.tocc.em.service.IEmPrePlanFileService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 应急预案附件管理Controller
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags ="应急预案附件管理")
@RestController
@RequestMapping("/em/prePlanFile")
public class EmPrePlanFileController extends BaseController
{
    @Autowired
    private IEmPrePlanFileService emPrePlanFileService;

    /**
     * 查询应急预案附件管理列表
     */
    @ApiOperation("查询应急预案附件管理列表")
    @PreAuthorize("@ss.hasPermi('em:emPrePlanFile:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmPrePlanFile emPrePlanFile)
    {
        startPage();
        List<EmPrePlanFile> list = emPrePlanFileService.selectEmPrePlanFileList(emPrePlanFile);
        return getDataTable(list);
    }

    /**
     * 导出应急预案附件管理列表
     */
    @ApiOperation("导出应急预案附件管理列表")
    @PreAuthorize("@ss.hasPermi('em:emPrePlanFile:export')")
    @Log(title = "应急预案附件管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmPrePlanFile emPrePlanFile)
    {
        List<EmPrePlanFile> list = emPrePlanFileService.selectEmPrePlanFileList(emPrePlanFile);
        ExcelUtil<EmPrePlanFile> util = new ExcelUtil<EmPrePlanFile>(EmPrePlanFile.class);
        util.exportExcel(response, list, "应急预案附件管理数据");
    }

    /**
     * 获取应急预案附件管理详细信息
     */
    @ApiOperation("获取应急预案附件管理详细信息")
    @PreAuthorize("@ss.hasPermi('em:emPrePlanFile:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(emPrePlanFileService.selectEmPrePlanFileById(id));
    }

    /**
     * 新增应急预案附件管理
     */
    @ApiOperation("新增应急预案附件管理")
    @PreAuthorize("@ss.hasPermi('em:emPrePlanFile:add')")
    @Log(title = "应急预案附件管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmPrePlanFile emPrePlanFile)
    {
        return toAjax(emPrePlanFileService.insertEmPrePlanFile(emPrePlanFile));
    }

    /**
     * 修改应急预案附件管理
     */
    @ApiOperation("修改应急预案附件管理")
    @PreAuthorize("@ss.hasPermi('em:emPrePlanFile:edit')")
    @Log(title = "应急预案附件管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmPrePlanFile emPrePlanFile)
    {
        return toAjax(emPrePlanFileService.updateEmPrePlanFile(emPrePlanFile));
    }

    /**
     * 删除应急预案附件管理
     */
    @ApiOperation("删除应急预案附件管理")
    @PreAuthorize("@ss.hasPermi('em:emPrePlanFile:remove')")
    @Log(title = "应急预案附件管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(emPrePlanFileService.deleteEmPrePlanFileByIds(ids));
    }
}
