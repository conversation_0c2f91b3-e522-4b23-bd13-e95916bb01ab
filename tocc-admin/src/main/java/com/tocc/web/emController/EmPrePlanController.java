package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tocc.em.dto.EmPrePlanDTO;
import com.tocc.em.qo.EmPrePlanQO;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.em.vo.EmPrePlanVersionVO;
import com.tocc.em.vo.EmPrePlanCompareVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.em.domain.EmPrePlan;
import com.tocc.em.service.IEmPrePlanService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 应急预案数据Controller
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags ="应急预案数据")
@RestController
@RequestMapping("/em/prePlan")
public class EmPrePlanController extends BaseController
{
    @Autowired
    private IEmPrePlanService emPrePlanService;

    /**
     * 查询应急预案数据列表
     */
    @ApiOperation("查询应急预案数据列表")
    @PreAuthorize("@ss.hasPermi('em:prePlan:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmPrePlanQO emPrePlanQO)
    {
        startPage();
        List<EmPrePlan> list = emPrePlanService.selectEmPrePlanList(emPrePlanQO);
        return getDataTable(list);
    }

    /**
     * 导出应急预案数据列表
     */
    @ApiOperation("导出应急预案数据列表")
    @PreAuthorize("@ss.hasPermi('em:prePlan:export')")
    @Log(title = "应急预案数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,EmPrePlanQO emPrePlanQO)
    {
        List<EmPrePlan> list = emPrePlanService.selectEmPrePlanList(emPrePlanQO);
        ExcelUtil<EmPrePlan> util = new ExcelUtil<EmPrePlan>(EmPrePlan.class);
        util.exportExcel(response, list, "应急预案数据数据");
    }

    /**
     * 获取应急预案数据详细信息
     */
    @ApiOperation("获取应急预案数据详细信息")
    @PreAuthorize("@ss.hasPermi('em:prePlan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(emPrePlanService.selectEmPrePlanById(id));
    }

    /**
     * 新增应急预案数据
     */
    @ApiOperation("新增应急预案数据")
    @PreAuthorize("@ss.hasPermi('em:prePlan:add')")
    @Log(title = "应急预案数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmPrePlanDTO emPrePlanDTO)
    {
        return toAjax(emPrePlanService.insertEmPrePlan(emPrePlanDTO));
    }

    /**
     * 修改应急预案数据
     */
    @ApiOperation("修改应急预案数据")
    @PreAuthorize("@ss.hasPermi('em:prePlan:edit')")
    @Log(title = "应急预案数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmPrePlanDTO emPrePlanDTO)
    {
        return toAjax(emPrePlanService.updateEmPrePlan(emPrePlanDTO));
    }

    /**
     * 删除应急预案数据
     */
    @ApiOperation("删除应急预案数据")
    @PreAuthorize("@ss.hasPermi('em:prePlan:remove')")
    @Log(title = "应急预案数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(emPrePlanService.deleteEmPrePlanByIds(ids));
    }

    /**
     * 查询预案版本历史（包括当前版本和历史版本）
     */
    @ApiOperation("查询预案版本历史")
    @PreAuthorize("@ss.hasPermi('em:prePlan:list')")
    @GetMapping("/versionHistory")
    public TableDataInfo getVersionHistory(@RequestParam String prePlanId)
    {
        startPage();
        List<EmPrePlanVersionVO> list;
        
        if (prePlanId != null && !prePlanId.trim().isEmpty()) {
            // 根据预案ID查询版本历史
            list = emPrePlanService.selectEmPrePlanVersionHistory(prePlanId);
        } else {
            // 查询所有预案的版本历史
            list = emPrePlanService.selectEmPrePlanVersionHistory(null);
        }
        
        return getDataTable(list);
    }

    /**
     * 根据预案ID查询该预案的所有版本
     */
    @ApiOperation("根据预案ID查询版本历史")
    @PreAuthorize("@ss.hasPermi('em:prePlan:query')")
    @GetMapping("/versionHistory/{prePlanId}")
    public AjaxResult getVersionHistoryById(@PathVariable String prePlanId)
    {
        List<EmPrePlanVersionVO> list = emPrePlanService.selectEmPrePlanVersionHistory(prePlanId);
        return success(list);
    }

    /**
     * 根据预案ID和版本号查询预案详情
     */
    @ApiOperation("根据预案ID和版本号查询预案详情")
    @PreAuthorize("@ss.hasPermi('em:prePlan:query')")
    @GetMapping("/{prePlanId}/version/{version}")
    public AjaxResult getInfoByIdAndVersion(@PathVariable("prePlanId") String prePlanId, 
                                           @PathVariable("version") String version)
    {
        try {
            EmPrePlanVO result = emPrePlanService.selectEmPrePlanByIdAndVersion(prePlanId, version);
            return success(result);
        } catch (IllegalArgumentException e) {
            return error(e.getMessage());
        } catch (RuntimeException e) {
            return error(e.getMessage());
        } catch (Exception e) {
            return error("查询预案详情失败：" + e.getMessage());
        }
    }

    /**
     * 比较历史版本与最新版本的预案内容
     */
    @ApiOperation("比较历史版本与最新版本的预案内容")
    @PreAuthorize("@ss.hasPermi('em:prePlan:query')")
    @GetMapping("/compare/{historyVersionId}")
    public AjaxResult compareVersions(@PathVariable("historyVersionId") String historyVersionId)
    {
        try {
            // 检查输入的ID是否是最新版本的ID
            if (emPrePlanService.isLatestVersion(historyVersionId)) {
                return error("当前选择的版本是最新版本，无法与自己进行比较");
            }
            
            EmPrePlanCompareVO result = emPrePlanService.compareVersionsByHistoryId(historyVersionId);
            return success(result);
        } catch (IllegalArgumentException e) {
            return error(e.getMessage());
        } catch (RuntimeException e) {
            return error(e.getMessage());
        } catch (Exception e) {
            return error("版本比较失败：" + e.getMessage());
        }
    }


}
