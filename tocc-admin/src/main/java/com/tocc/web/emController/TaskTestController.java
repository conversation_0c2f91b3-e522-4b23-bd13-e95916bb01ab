package com.tocc.web.emController;

import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.utils.StringUtils;
import com.tocc.quartz.task.InfoUpdateTimeoutCheckTask;
import com.tocc.system.service.ISysDictDataService;
import com.tocc.web.emController.vo.TaskExecutionResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 定时任务测试Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "定时任务测试")
@RestController
@RequestMapping("/task/test")
public class TaskTestController extends BaseController {
    
    private static final Logger log = LoggerFactory.getLogger(TaskTestController.class);
    
    @Autowired
    private InfoUpdateTimeoutCheckTask infoUpdateTimeoutCheckTask;

    @Autowired
    private ISysDictDataService dictDataService;
    
    /**
     * 手动执行信息更新超时检查任务
     */
    @ApiOperation("手动执行信息更新超时检查任务")
    @PreAuthorize("@ss.hasPermi('task:test:execute')")
    @PostMapping("/timeout-check")
    public AjaxResult executeTimeoutCheck(
            @ApiParam(value = "任务参数", required = false)
            @RequestParam(required = false) String params) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始手动执行信息更新超时检查任务，参数：{}", params);
            
            // 执行定时任务
            infoUpdateTimeoutCheckTask.execute(params);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("信息更新超时检查任务执行完成，耗时：{}ms", duration);
            
            return success("任务执行成功，耗时：" + duration + "ms");
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.error("信息更新超时检查任务执行失败，耗时：{}ms", duration, e);
            
            return error("任务执行失败：" + e.getMessage() + "，耗时：" + duration + "ms");
        }
    }
    
    /**
     * 执行信息更新超时检查任务（详细版本）
     */
    @ApiOperation("执行信息更新超时检查任务（详细版本）")
    @PreAuthorize("@ss.hasPermi('task:test:execute')")
    @PostMapping("/timeout-check-details")
    public AjaxResult executeTimeoutCheckWithDetails(
            @ApiParam(value = "任务参数", required = false)
            @RequestParam(required = false) String params) {

        TaskExecutionResultVO result = new TaskExecutionResultVO();
        List<String> executionSteps = new ArrayList<>();
        Date startTime = new Date();
        result.setStartTime(startTime);

        try {
            log.info("开始执行详细版本的信息更新超时检查任务，参数：{}", params);
            executionSteps.add("开始执行信息更新超时检查任务，参数：" + params);

            // 获取超时阈值配置
            Map<String, Integer> timeoutThresholds = getTimeoutThresholds();
            result.setTimeoutThresholds(timeoutThresholds);
            executionSteps.add("获取超时阈值配置：" + timeoutThresholds);

            // 执行定时任务
            executionSteps.add("开始执行定时任务...");
            infoUpdateTimeoutCheckTask.execute(params);
            executionSteps.add("定时任务执行完成");

            Date endTime = new Date();
            result.setEndTime(endTime);
            result.setDuration(endTime.getTime() - startTime.getTime());
            result.setSuccess(true);
            result.setMessage("任务执行成功");
            result.setExecutionSteps(executionSteps);

            log.info("详细版本的信息更新超时检查任务执行完成，耗时：{}ms", result.getDuration());
            return success(result);

        } catch (Exception e) {
            Date endTime = new Date();
            result.setEndTime(endTime);
            result.setDuration(endTime.getTime() - startTime.getTime());
            result.setSuccess(false);
            result.setMessage("任务执行失败");
            result.setErrorMessage(e.getMessage());
            result.setExecutionSteps(executionSteps);

            log.error("执行详细版本的信息更新超时检查任务失败", e);
            return AjaxResult.error(result.getMessage()).put("data", result);
        }
    }

    /**
     * 检查任务执行环境
     */
    @ApiOperation("检查任务执行环境")
    @PreAuthorize("@ss.hasPermi('task:test:status')")
    @GetMapping("/environment-check")
    public AjaxResult checkTaskEnvironment() {
        TaskExecutionResultVO result = new TaskExecutionResultVO();
        List<String> executionSteps = new ArrayList<>();
        Date startTime = new Date();
        result.setStartTime(startTime);

        try {
            log.info("开始检查任务执行环境");
            executionSteps.add("开始检查任务执行环境");

            // 检查字典配置
            Map<String, Integer> timeoutThresholds = getTimeoutThresholds();
            result.setTimeoutThresholds(timeoutThresholds);
            executionSteps.add("字典配置检查完成：" + timeoutThresholds);

            // 检查各服务是否可用
            checkServiceAvailability(executionSteps);

            Date endTime = new Date();
            result.setEndTime(endTime);
            result.setDuration(endTime.getTime() - startTime.getTime());
            result.setSuccess(true);
            result.setMessage("环境检查完成");
            result.setExecutionSteps(executionSteps);

            log.info("任务执行环境检查完成");
            return success(result);

        } catch (Exception e) {
            Date endTime = new Date();
            result.setEndTime(endTime);
            result.setDuration(endTime.getTime() - startTime.getTime());
            result.setSuccess(false);
            result.setMessage("环境检查失败");
            result.setErrorMessage(e.getMessage());
            result.setExecutionSteps(executionSteps);

            log.error("检查任务执行环境失败", e);
            return AjaxResult.error(result.getMessage()).put("data", result);
        }
    }

    /**
     * 获取任务执行状态信息
     */
    @ApiOperation("获取任务执行状态信息")
    @PreAuthorize("@ss.hasPermi('task:test:status')")
    @GetMapping("/status")
    public AjaxResult getTaskStatus() {
        try {
            // 这里可以添加一些状态检查逻辑
            // 比如检查相关服务是否正常、数据库连接是否正常等

            return success("任务相关服务状态正常");

        } catch (Exception e) {
            log.error("获取任务状态失败", e);
            return error("获取任务状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取超时阈值配置
     */
    private Map<String, Integer> getTimeoutThresholds() {
        Map<String, Integer> thresholds = new HashMap<>();

        try {
            String prePlanThreshold = dictDataService.selectDictLabel("info_update_timeout", "应急预案更新阈值");
            thresholds.put("应急预案更新阈值", StringUtils.isNotEmpty(prePlanThreshold) ? Integer.parseInt(prePlanThreshold) : 1);
        } catch (Exception e) {
            thresholds.put("应急预案更新阈值", 1);
        }

        try {
            String rescueTeamThreshold = dictDataService.selectDictLabel("info_update_timeout", "救援队伍更新阈值");
            thresholds.put("救援队伍更新阈值", StringUtils.isNotEmpty(rescueTeamThreshold) ? Integer.parseInt(rescueTeamThreshold) : 1);
        } catch (Exception e) {
            thresholds.put("救援队伍更新阈值", 1);
        }

        try {
            String warehouseThreshold = dictDataService.selectDictLabel("info_update_timeout", "物资仓库更新阈值");
            thresholds.put("物资仓库更新阈值", StringUtils.isNotEmpty(warehouseThreshold) ? Integer.parseInt(warehouseThreshold) : 1);
        } catch (Exception e) {
            thresholds.put("物资仓库更新阈值", 1);
        }

        return thresholds;
    }

    /**
     * 检查服务可用性
     */
    private void checkServiceAvailability(List<String> executionSteps) {
        try {
            // 检查字典服务
            dictDataService.selectDictLabel("info_update_timeout", "应急预案更新阈值");
            executionSteps.add("字典服务检查：正常");
        } catch (Exception e) {
            executionSteps.add("字典服务检查：异常 - " + e.getMessage());
        }

        try {
            // 检查定时任务类是否可用
            if (infoUpdateTimeoutCheckTask != null) {
                executionSteps.add("定时任务类检查：正常");
            } else {
                executionSteps.add("定时任务类检查：异常 - 任务类为null");
            }
        } catch (Exception e) {
            executionSteps.add("定时任务类检查：异常 - " + e.getMessage());
        }
    }
}
