package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tocc.em.domain.EmWarehouse;
import com.tocc.em.service.IEmWarehouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 应急物资仓库基础信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@Api(tags = "应急物资仓库基础信息")
@RestController
@RequestMapping("/em/warehouse")
public class EmWarehouseController extends BaseController
{
    @Autowired
    private IEmWarehouseService emWarehouseService;

    /**
     * 查询应急物资仓库基础信息列表
     */
    @ApiOperation("查询应急物资仓库基础信息列表")
    @PreAuthorize("@ss.hasPermi('em:warehouse:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmWarehouse emWarehouse)
    {
        startPage();
        List<EmWarehouse> list = emWarehouseService.selectEmWarehouseList(emWarehouse);
        return getDataTable(list);
    }

    /**
     * 导出应急物资仓库基础信息列表
     */
    @ApiOperation("导出应急物资仓库基础信息列表")
    @PreAuthorize("@ss.hasPermi('em:warehouse:export')")
    @Log(title = "应急物资仓库基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmWarehouse emWarehouse)
    {
        List<EmWarehouse> list = emWarehouseService.selectEmWarehouseList(emWarehouse);
        ExcelUtil<EmWarehouse> util = new ExcelUtil<EmWarehouse>(EmWarehouse.class);
        util.exportExcel(response, list, "应急物资仓库基础信息数据");
    }

    /**
     * 获取应急物资仓库基础信息详细信息
     */
    @ApiOperation("获取应急物资仓库基础信息详细信息")
    @PreAuthorize("@ss.hasPermi('em:warehouse:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(emWarehouseService.selectEmWarehouseById(id));
    }

    /**
     * 新增应急物资仓库基础信息
     */
    @ApiOperation("新增应急物资仓库基础信息")
    @PreAuthorize("@ss.hasPermi('em:warehouse:add')")
    @Log(title = "应急物资仓库基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmWarehouse emWarehouse)
    {
        return toAjax(emWarehouseService.insertEmWarehouse(emWarehouse));
    }

    /**
     * 修改应急物资仓库基础信息
     */
    @ApiOperation("修改应急物资仓库基础信息")
    @PreAuthorize("@ss.hasPermi('em:warehouse:edit')")
    @Log(title = "应急物资仓库基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmWarehouse emWarehouse)
    {
        return toAjax(emWarehouseService.updateEmWarehouse(emWarehouse));
    }

    /**
     * 删除应急物资仓库基础信息
     */
    @ApiOperation("删除应急物资仓库基础信息")
    @PreAuthorize("@ss.hasPermi('em:warehouse:remove')")
    @Log(title = "应急物资仓库基础信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(emWarehouseService.deleteEmWarehouseByIds(ids));
    }
}
