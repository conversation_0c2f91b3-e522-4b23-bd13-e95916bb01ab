package com.tocc.web.emController.vo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 任务执行结果VO
 * 
 * <AUTHOR>
 */
public class TaskExecutionResultVO {
    
    /** 执行是否成功 */
    private Boolean success;
    
    /** 执行开始时间 */
    private Date startTime;
    
    /** 执行结束时间 */
    private Date endTime;
    
    /** 执行耗时（毫秒） */
    private Long duration;
    
    /** 执行消息 */
    private String message;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 检查的应急预案数量 */
    private Integer prePlanCount;
    
    /** 超时的应急预案数量 */
    private Integer timeoutPrePlanCount;
    
    /** 检查的救援队伍数量 */
    private Integer rescueTeamCount;
    
    /** 超时的救援队伍数量 */
    private Integer timeoutRescueTeamCount;
    
    /** 检查的物资仓库数量 */
    private Integer warehouseCount;
    
    /** 超时的物资仓库数量 */
    private Integer timeoutWarehouseCount;
    
    /** 创建的告警数量 */
    private Integer createdAlarmCount;
    
    /** 详细执行步骤 */
    private List<String> executionSteps;
    
    /** 超时阈值配置 */
    private Map<String, Integer> timeoutThresholds;

    // Getter and Setter methods
    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getPrePlanCount() {
        return prePlanCount;
    }

    public void setPrePlanCount(Integer prePlanCount) {
        this.prePlanCount = prePlanCount;
    }

    public Integer getTimeoutPrePlanCount() {
        return timeoutPrePlanCount;
    }

    public void setTimeoutPrePlanCount(Integer timeoutPrePlanCount) {
        this.timeoutPrePlanCount = timeoutPrePlanCount;
    }

    public Integer getRescueTeamCount() {
        return rescueTeamCount;
    }

    public void setRescueTeamCount(Integer rescueTeamCount) {
        this.rescueTeamCount = rescueTeamCount;
    }

    public Integer getTimeoutRescueTeamCount() {
        return timeoutRescueTeamCount;
    }

    public void setTimeoutRescueTeamCount(Integer timeoutRescueTeamCount) {
        this.timeoutRescueTeamCount = timeoutRescueTeamCount;
    }

    public Integer getWarehouseCount() {
        return warehouseCount;
    }

    public void setWarehouseCount(Integer warehouseCount) {
        this.warehouseCount = warehouseCount;
    }

    public Integer getTimeoutWarehouseCount() {
        return timeoutWarehouseCount;
    }

    public void setTimeoutWarehouseCount(Integer timeoutWarehouseCount) {
        this.timeoutWarehouseCount = timeoutWarehouseCount;
    }

    public Integer getCreatedAlarmCount() {
        return createdAlarmCount;
    }

    public void setCreatedAlarmCount(Integer createdAlarmCount) {
        this.createdAlarmCount = createdAlarmCount;
    }

    public List<String> getExecutionSteps() {
        return executionSteps;
    }

    public void setExecutionSteps(List<String> executionSteps) {
        this.executionSteps = executionSteps;
    }

    public Map<String, Integer> getTimeoutThresholds() {
        return timeoutThresholds;
    }

    public void setTimeoutThresholds(Map<String, Integer> timeoutThresholds) {
        this.timeoutThresholds = timeoutThresholds;
    }
}
