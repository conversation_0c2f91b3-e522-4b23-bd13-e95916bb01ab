package com.tocc.web.emController;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.tocc.em.dto.EmEventLevelDTO;
import com.tocc.em.qo.EmEventLevelQO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.enums.BusinessType;
import com.tocc.em.domain.EmEventLevel;
import com.tocc.em.service.IEmEventLevelService;
import com.tocc.common.utils.poi.ExcelUtil;
import com.tocc.common.core.page.TableDataInfo;

/**
 * 事件分级响应条件Controller
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Api(tags ="事件分级响应条件")
@RestController
@RequestMapping("/em/eventLevel")
public class EmEventLevelController extends BaseController
{
    @Autowired
    private IEmEventLevelService emEventLevelService;

    /**
     * 查询事件分级响应条件列表
     */
    @ApiOperation("查询事件分级响应条件列表")
    @PreAuthorize("@ss.hasPermi('em:EmEventLevel:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmEventLevelQO eventLevelQO)
    {
        startPage();
        List<EmEventLevel> list = emEventLevelService.selectEmEventLevelList(eventLevelQO);
        return getDataTable(list);
    }

    /**
     * 导出事件分级响应条件列表
     */
    @ApiOperation("导出事件分级响应条件列表")
    @PreAuthorize("@ss.hasPermi('em:EmEventLevel:export')")
    @Log(title = "事件分级响应条件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmEventLevelQO emEventLevelQO)
    {
        List<EmEventLevel> list = emEventLevelService.selectEmEventLevelList(emEventLevelQO);
        ExcelUtil<EmEventLevel> util = new ExcelUtil<EmEventLevel>(EmEventLevel.class);
        util.exportExcel(response, list, "事件分级响应条件数据");
    }

    /**
     * 获取事件分级响应条件详细信息
     */
    @ApiOperation("获取事件分级响应条件详细信息")
    @PreAuthorize("@ss.hasPermi('em:EmEventLevel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(emEventLevelService.selectEmEventLevelById(id));
    }

    /**
     * 新增事件分级响应条件
     */
    @ApiOperation("新增事件分级响应条件")
    @PreAuthorize("@ss.hasPermi('em:EmEventLevel:add')")
    @Log(title = "事件分级响应条件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmEventLevel emEventLevel)
    {
        return toAjax(emEventLevelService.insertEmEventLevel(emEventLevel));
    }

    /**
     * 修改事件分级响应条件
     */
    @ApiOperation("修改事件分级响应条件")
    @PreAuthorize("@ss.hasPermi('em:EmEventLevel:edit')")
    @Log(title = "事件分级响应条件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmEventLevel emEventLevel)
    {
        return toAjax(emEventLevelService.updateEmEventLevel(emEventLevel));
    }

    /**
     * 删除事件分级响应条件
     */
    @ApiOperation("删除事件分级响应条件")
    @PreAuthorize("@ss.hasPermi('em:EmEventLevel:remove')")
    @Log(title = "事件分级响应条件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(emEventLevelService.deleteEmEventLevelByIds(ids));
    }
}
