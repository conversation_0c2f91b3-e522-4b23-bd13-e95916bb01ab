package com.tocc.web.emController;

import com.tocc.common.annotation.Log;
import com.tocc.common.core.controller.BaseController;
import com.tocc.common.core.domain.AjaxResult;
import com.tocc.common.core.page.TableDataInfo;
import com.tocc.common.enums.BusinessType;
import com.tocc.domain.dto.MaterialDTO;
import com.tocc.domain.vo.MaterialVO;
import com.tocc.service.IMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 物资Controller
 * 
 * <AUTHOR>
 */
@Api(tags = "物资管理")
@RestController
@RequestMapping("/material")
public class MaterialController extends BaseController {
    
    @Autowired
    private IMaterialService materialService;

    /**
     * 查询物资列表（包含物资和装备）
     */
    @ApiOperation("查询物资列表")
    @PreAuthorize("@ss.hasPermi('material:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaterialDTO material) {
        startPage();
        List<MaterialVO> list = materialService.selectMaterialList(material);
        return getDataTable(list);
    }

    /**
     * 新增物资
     */
    @ApiOperation("新增物资")
    @PreAuthorize("@ss.hasPermi('material:add')")
    @Log(title = "物资", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaterialDTO material) {
        // 验证物资归属唯一性
        if (!material.validateOwnership()) {
            return error("物资必须且只能归属于一个仓库或一个救援队伍");
        }
        return toAjax(materialService.insertMaterial(material));
    }

    /**
     * 修改物资
     */
    @ApiOperation("修改物资")
    @PreAuthorize("@ss.hasPermi('material:edit')")
    @Log(title = "物资", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaterialDTO material) {
        // 验证物资归属唯一性
        if (!material.validateOwnership()) {
            return error("物资必须且只能归属于一个仓库或一个救援队伍");
        }
        return toAjax(materialService.updateMaterial(material));
    }

    /**
     * 删除物资
     */
    @ApiOperation("删除物资")
    @PreAuthorize("@ss.hasPermi('material:remove')")
    @Log(title = "物资", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(materialService.deleteMaterialByIds(ids));
    }
}
