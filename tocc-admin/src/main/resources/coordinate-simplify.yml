# 坐标抽稀配置
coordinate:
  simplify:
    # 是否启用坐标抽稀
    enabled: true
    
    # 抽稀算法类型：interval（间隔抽稀）、douglas（道格拉斯-普克算法）
    algorithm: interval
    
    # 道格拉斯-普克算法的容差值（单位：度）
    tolerance: 0.0001
    
    # 最小保留点数
    min-points: 2
    
    # 最大保留点数
    max-points: 100
    
    # 间隔抽稀的配置
    interval:
      # 10个点以下的间隔（不抽稀）
      small: 1
      # 11-50个点的间隔
      medium: 2
      # 51-100个点的间隔
      large: 3
      # 101-200个点的间隔
      xlarge: 4
      # 201-500个点的间隔
      xxlarge: 5
      # 501-1000个点的间隔
      huge: 8
      # 1000个点以上的间隔
      massive: 10
