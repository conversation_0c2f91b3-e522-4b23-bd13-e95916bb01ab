# 项目相关配置
tocc:
  # 名称
  name: Tocc
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/tocc/uploadPath，Linux配置 /home/<USER>/uploadPath）
#  profile: /home/<USER>/uploadPath
  profile: D:/tocc/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8380
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.tocc: debug
    org.springframework: warn
#    org.mybatis.spring: DEBUG

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
#    active: prod
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
#    port: 6378
    port: 6379
    # 数据库索引
    database: 8
    # 密码
#    password: reDIS2021P@SS
    password: 123456
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.tocc.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

#声网配置
agora:
  app-id: 937a6bbbf4cf49be940bf7b9d90cc2e4
  app-certificate: 2f6a2d629a8944f98a6ec691d97658e6
  token-expiration: 18000
  privilege-expiration: 18000

# 外部系统配置
external:
  system:
    # 外部系统的基础URL
    base-url: http://180.142.104.37:11000/admin-api
    # 连接超时时间（毫秒）
    connect-timeout: 5000
    # 读取超时时间（毫秒）
    read-timeout: 10000
    # API密钥（如果需要）
    api-key: your-api-key
    # 是否启用
    enabled: true
    
    # 登录认证配置
    auth:
      # 登录用户名
      username: jklixq
      # 登录密码（明文，系统会使用RSA加密）
      password: Lixq@3.14
      # 登录接口端点
      login-endpoint: system/auth/v2/login
      # 用户信息接口端点
      userinfo-endpoint: /admin-api/system/auth/get-permission-info
      # Token刷新接口端点
      refresh-endpoint: /system/auth/refresh-token
      # Token有效期：8小时，提前30分钟检查过期并重新登录
              # 密码加密类型：rsa（使用前端相同的RSA加密方式）
      password-encrypt-type: rsa
  
  # 外部系统API接口配置
  api:
    # 示例系统管理接口
    system:
      user-list: /system/user/list
      user-detail: /system/user/{id}
      user-create: /system/user
      user-update: /system/user
      user-delete: /system/user/{id}
      role-list: /system/role/list
      dept-list: /system/dept/list
      menu-list: /system/menu/list
      dict-list: /system/dict/data/list

    # 交通数据接口
    traffic:
      station-pre-one-hour: charge/statApi/Api230724/stationPreOneHour
      sectionPreOneHour: charge/roadApi/Api230724/sectionPreOneHour
      getFlowServiceNew: infra/serviceArea/getFlowServiceNew
      interFlowPreOneHour: charge/inter/Api230724/interFlowPreOneHour
      stationFlowDetail: charge/api-v2/stationFlowDetail
      dataHighwayLength: system/dict-data/page?dictType=data_highway_length&pageSize=100&pageNo=1
      getScreenLeftData: charge/common/getScreenLeftData
    # 监控数据接口
    monitor:
      system-status: /monitor/system/status
      server-info: /monitor/server/info
      operation-log: /monitor/operation/log
      login-log: /monitor/login/log
      performance-data: /monitor/performance/data

    # 报表数据接口
    report:
      traffic-report: /report/traffic/summary
      statistics-report: /report/statistics/data
      export-data: /report/export/{type}
      chart-data: /report/chart/data

    # 自定义接口配置
    custom:
      # 示例：收费站小时流量接口
      station-hour-flow:
        endpoint: /admin-api/charge/statApi/Api230724/stationPreOneHour
        method: GET
        description: 收费站小时流量数据
        require-auth: true
        cacheable: true
        cache-time: 300
        default-headers:
          Content-Type: application/json
          Accept: application/json

      # 示例：实时路况接口
      real-time-traffic:
        endpoint: /api/traffic/realtime
        method: GET
        description: 实时路况数据
        require-auth: true
        cacheable: true
        cache-time: 60
        default-headers:
          Content-Type: application/json

      # 示例：事件上报接口
      incident-report:
        endpoint: /api/incident/report
        method: POST
        description: 交通事件上报
        require-auth: true
        cacheable: false
        default-headers:
          Content-Type: application/json