<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.UserCccMapper">
    
    <resultMap type="com.tocc.common.domain.vo.UserCccVO" id="UserCccResult">
        <result property="userId"       column="user_id"       />
        <result property="loginName"    column="login_name"    />
        <result property="displayName"  column="display_name"  />
        <result property="accessKey"    column="access_key"    />
        <result property="accessSecret" column="access_secret" />
    </resultMap>

    <sql id="selectUserCccVo">
        select user_id, login_name, display_name, access_key, access_secret
        from user_ccc
    </sql>

    <select id="selectByUserId" parameterType="String" resultMap="UserCccResult">
        <include refid="selectUserCccVo"/>
        where user_id = #{userId}
    </select>

</mapper>
