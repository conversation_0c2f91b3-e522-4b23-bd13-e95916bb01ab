package com.tocc.service.impl;

import com.tocc.common.domain.vo.UserCccVO;
import com.tocc.common.utils.AliyunCccUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.mapper.UserCccMapper;
import com.tocc.service.ITelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("telService")
public class TelServiceImpl implements ITelService {

    @Autowired
    private UserCccMapper userCccMapper;

    @Override
    public String api(String userId, String action, String request) {
        //获取用户AK、AS信息
        UserCccVO vo = userCccMapper.selectByUserId(userId);
        if(vo == null) {
            throw new IllegalArgumentException("无云呼坐席，请联系管理员创建坐席");
        }

        //接口名称、参数校验
        if(StringUtils.isBlank(action)) {
            throw new IllegalArgumentException("action接口名称不能为空");
        }
        if(StringUtils.isBlank(request)) {
            throw new IllegalArgumentException("request参数不能为空");
        }

        //判断权限，是否有云呼密钥
        if(StringUtils.isBlank(vo.getAccessKey()) || StringUtils.isBlank(vo.getAccessSecret())) {
            throw new IllegalArgumentException("无云呼坐席，请联系管理员创建坐席密钥");
        }

        vo.setAction(action);
        vo.setRequest(request);
        String res = AliyunCccUtils.invokeApiByAk(vo, action, request);
        return res;
    }

}
