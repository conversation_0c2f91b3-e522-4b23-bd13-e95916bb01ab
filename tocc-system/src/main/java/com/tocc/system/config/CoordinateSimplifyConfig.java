package com.tocc.system.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 坐标抽稀配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "coordinate.simplify")
public class CoordinateSimplifyConfig {
    
    /** 是否启用坐标抽稀 */
    private boolean enabled = true;
    
    /** 抽稀算法类型：interval（间隔抽稀）、douglas（道格拉斯-普克算法） */
    private String algorithm = "interval";
    
    /** 道格拉斯-普克算法的容差值 */
    private double tolerance = 0.0001;
    
    /** 最小保留点数 */
    private int minPoints = 2;
    
    /** 最大保留点数 */
    private int maxPoints = 100;
    
    /** 间隔抽稀的配置 */
    private IntervalConfig interval = new IntervalConfig();
    
    public static class IntervalConfig {
        /** 10个点以下的间隔 */
        private int small = 1;
        /** 11-50个点的间隔 */
        private int medium = 2;
        /** 51-100个点的间隔 */
        private int large = 3;
        /** 101-200个点的间隔 */
        private int xlarge = 4;
        /** 201-500个点的间隔 */
        private int xxlarge = 5;
        /** 501-1000个点的间隔 */
        private int huge = 8;
        /** 1000个点以上的间隔 */
        private int massive = 10;
        
        // getters and setters
        public int getSmall() { return small; }
        public void setSmall(int small) { this.small = small; }
        public int getMedium() { return medium; }
        public void setMedium(int medium) { this.medium = medium; }
        public int getLarge() { return large; }
        public void setLarge(int large) { this.large = large; }
        public int getXlarge() { return xlarge; }
        public void setXlarge(int xlarge) { this.xlarge = xlarge; }
        public int getXxlarge() { return xxlarge; }
        public void setXxlarge(int xxlarge) { this.xxlarge = xxlarge; }
        public int getHuge() { return huge; }
        public void setHuge(int huge) { this.huge = huge; }
        public int getMassive() { return massive; }
        public void setMassive(int massive) { this.massive = massive; }
    }
    
    // getters and setters
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getAlgorithm() {
        return algorithm;
    }
    
    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }
    
    public double getTolerance() {
        return tolerance;
    }
    
    public void setTolerance(double tolerance) {
        this.tolerance = tolerance;
    }
    
    public int getMinPoints() {
        return minPoints;
    }
    
    public void setMinPoints(int minPoints) {
        this.minPoints = minPoints;
    }
    
    public int getMaxPoints() {
        return maxPoints;
    }
    
    public void setMaxPoints(int maxPoints) {
        this.maxPoints = maxPoints;
    }
    
    public IntervalConfig getInterval() {
        return interval;
    }
    
    public void setInterval(IntervalConfig interval) {
        this.interval = interval;
    }
}
