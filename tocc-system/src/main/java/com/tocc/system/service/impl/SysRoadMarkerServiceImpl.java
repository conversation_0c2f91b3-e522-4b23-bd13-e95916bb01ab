package com.tocc.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.system.mapper.SysRoadMarkerMapper;
import com.tocc.system.domain.SysRoadMarker;
import com.tocc.system.domain.vo.RoadMarkerTreeVO;
import com.tocc.system.service.ISysRoadMarkerService;

/**
 * 公路编号Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@Service
public class SysRoadMarkerServiceImpl implements ISysRoadMarkerService 
{
    @Autowired
    private SysRoadMarkerMapper sysRoadMarkerMapper;

    /**
     * 查询公路编号
     * 
     * @param id 公路编号主键
     * @return 公路编号
     */
    @Override
    public SysRoadMarker selectSysRoadMarkerById(Long id)
    {
        return sysRoadMarkerMapper.selectSysRoadMarkerById(id);
    }

    /**
     * 查询公路编号列表
     * 
     * @param sysRoadMarker 公路编号
     * @return 公路编号
     */
    @Override
    public List<SysRoadMarker> selectSysRoadMarkerList(SysRoadMarker sysRoadMarker)
    {
        return sysRoadMarkerMapper.selectSysRoadMarkerList(sysRoadMarker);
    }

    /**
     * 新增公路编号
     * 
     * @param sysRoadMarker 公路编号
     * @return 结果
     */
    @Override
    public int insertSysRoadMarker(SysRoadMarker sysRoadMarker)
    {
        return sysRoadMarkerMapper.insertSysRoadMarker(sysRoadMarker);
    }

    /**
     * 修改公路编号
     * 
     * @param sysRoadMarker 公路编号
     * @return 结果
     */
    @Override
    public int updateSysRoadMarker(SysRoadMarker sysRoadMarker)
    {
        return sysRoadMarkerMapper.updateSysRoadMarker(sysRoadMarker);
    }

    /**
     * 批量删除公路编号
     * 
     * @param ids 需要删除的公路编号主键
     * @return 结果
     */
    @Override
    public int deleteSysRoadMarkerByIds(Long[] ids)
    {
        return sysRoadMarkerMapper.deleteSysRoadMarkerByIds(ids);
    }

    /**
     * 删除公路编号信息
     *
     * @param id 公路编号主键
     * @return 结果
     */
    @Override
    public int deleteSysRoadMarkerById(Long id)
    {
        return sysRoadMarkerMapper.deleteSysRoadMarkerById(id);
    }

    /**
     * 获取公路编号树形结构
     *
     * @return 树形结构数据
     */
    @Override
    public List<RoadMarkerTreeVO> getRoadMarkerTree()
    {
        // 查询所有公路编号数据
        List<SysRoadMarker> allMarkers = sysRoadMarkerMapper.selectSysRoadMarkerList(new SysRoadMarker());

        // 道路类型映射
        Map<String, String> roadTypeMap = getRoadTypeMap();

        // 按道路类型分组
        Map<String, List<SysRoadMarker>> groupedByType = allMarkers.stream()
            .collect(Collectors.groupingBy(marker ->
                marker.getRoadType() != null ? marker.getRoadType() : "0"));

        // 构建树形结构
        List<RoadMarkerTreeVO> treeList = new ArrayList<>();

        for (Map.Entry<String, List<SysRoadMarker>> entry : groupedByType.entrySet()) {
            String roadType = entry.getKey();
            List<SysRoadMarker> markers = entry.getValue();

            // 创建道路类型节点
            String roadTypeName = roadTypeMap.getOrDefault(roadType, "未分类");
            RoadMarkerTreeVO typeNode = RoadMarkerTreeVO.createTypeNode(roadType, roadTypeName);

            // 添加公路编号子节点
            for (SysRoadMarker marker : markers) {
                RoadMarkerTreeVO markerNode = RoadMarkerTreeVO.createMarkerNode(
                    marker.getId(),
                    marker.getCode(),
                    marker.getName(),
                    marker.getAllName()
                );
                typeNode.addChild(markerNode);
            }

            treeList.add(typeNode);
        }

        return treeList;
    }

    /**
     * 根据公路编码和路段名称查询公路编号
     *
     * @param code 公路编码
     * @param name 路段名称
     * @return 公路编号
     */
    @Override
    public SysRoadMarker selectByCodeAndName(String code, String name) {
        return sysRoadMarkerMapper.selectByCodeAndName(code, name);
    }

    /**
     * 获取道路类型映射
     *
     * @return 道路类型映射
     */
    private Map<String, String> getRoadTypeMap() {
        Map<String, String> roadTypeMap = new HashMap<>();
        roadTypeMap.put("1", "高速公路");
        roadTypeMap.put("2", "国省干道");
        roadTypeMap.put("3", "县乡公路");
        roadTypeMap.put("4", "城市道路");
        roadTypeMap.put("0", "未分类");
        return roadTypeMap;
    }
}
