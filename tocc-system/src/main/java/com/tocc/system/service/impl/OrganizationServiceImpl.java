package com.tocc.system.service.impl;

import com.tocc.common.core.domain.entity.SysDept;
import com.tocc.common.service.IOrganizationService;
import com.tocc.system.service.ISysDeptService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 组织架构服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class OrganizationServiceImpl implements IOrganizationService {
    
    private static final Logger log = LoggerFactory.getLogger(OrganizationServiceImpl.class);
    
    @Autowired
    private ISysDeptService deptService;
    
    /**
     * 获取单位及其所有子单位的ID列表
     *
     * @param orgId 单位ID
     * @return 单位ID列表（包含自身和所有子单位）
     */
    @Override
    public List<String> getOrgIdsWithChildren(Long orgId) {
        Set<String> orgIdSet = new HashSet<>();

        if (orgId == null) {
            return new ArrayList<>();
        }

        try {
            // 递归获取所有子单位ID
            collectOrgIdsRecursively(orgId, orgIdSet);

        } catch (Exception e) {
            log.error("获取子单位列表失败：orgId={}", orgId, e);
            // 如果查询失败，至少返回当前用户的单位ID
            orgIdSet.clear();
            orgIdSet.add(orgId.toString());
        }

        return new ArrayList<>(orgIdSet);
    }

    /**
     * 递归收集单位及其子单位的ID
     *
     * @param orgId 单位ID
     * @param orgIdSet 单位ID集合
     */
    private void collectOrgIdsRecursively(Long orgId, Set<String> orgIdSet) {
        if (orgId == null || orgIdSet.contains(orgId.toString())) {
            return; // 避免无限递归
        }

        // 添加当前单位ID
        orgIdSet.add(orgId.toString());

        // 查询直接子单位
        SysDept queryCondition = new SysDept();
        queryCondition.setParentId(orgId);
        queryCondition.setOrgType("1"); // 只查询单位类型

        List<SysDept> childUnits = deptService.selectDeptList(queryCondition);
        if (childUnits != null && !childUnits.isEmpty()) {
            // 递归获取所有子单位的ID
            for (SysDept childUnit : childUnits) {
                collectOrgIdsRecursively(childUnit.getDeptId(), orgIdSet);
            }
        }
    }
    
    /**
     * 根据单位ID获取单位名称
     * 
     * @param orgId 单位ID
     * @return 单位名称
     */
    @Override
    public String getUnitNameByOrgId(Long orgId) {
        try {
            if (orgId == null) {
                return "未知单位";
            }
            
            // 查询单位信息（org_type = 1 表示单位）
            SysDept unit = deptService.selectDeptById(orgId);
            if (unit != null && "1".equals(unit.getOrgType())) {
                return unit.getDeptName();
            } else {
                return "未知单位";
            }
        } catch (Exception e) {
            log.error("获取单位名称失败：orgId={}", orgId, e);
            return "未知单位";
        }
    }
}
