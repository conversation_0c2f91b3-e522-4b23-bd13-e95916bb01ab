package com.tocc.system.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSON;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.system.domain.vo.RoadSectionGeometryVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.system.mapper.SysRoadBillingSegmentMapper;
import com.tocc.system.domain.SysRoadBillingSegment;
import com.tocc.system.service.ISysRoadBillingSegmentService;

/**
 * 高速公路计费路段信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Service
public class SysRoadBillingSegmentServiceImpl implements ISysRoadBillingSegmentService 
{
    private static final Logger log = LoggerFactory.getLogger(SysRoadBillingSegmentServiceImpl.class);

    @Autowired
    private SysRoadBillingSegmentMapper sysRoadBillingSegmentMapper;

    /**
     * 查询高速公路计费路段信息
     * 
     * @param id 高速公路计费路段信息主键
     * @return 高速公路计费路段信息
     */
    @Override
    public SysRoadBillingSegment selectSysRoadBillingSegmentById(Long id)
    {
        return sysRoadBillingSegmentMapper.selectSysRoadBillingSegmentById(id);
    }

    /**
     * 查询高速公路计费路段信息列表
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 高速公路计费路段信息
     */
    @Override
    public List<SysRoadBillingSegment> selectSysRoadBillingSegmentList(SysRoadBillingSegment sysRoadBillingSegment)
    {
        return sysRoadBillingSegmentMapper.selectSysRoadBillingSegmentList(sysRoadBillingSegment);
    }

    /**
     * 新增高速公路计费路段信息
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 结果
     */
    @Override
    public int insertSysRoadBillingSegment(SysRoadBillingSegment sysRoadBillingSegment)
    {
        sysRoadBillingSegment.setCreateTime(DateUtils.getNowDate());
        return sysRoadBillingSegmentMapper.insertSysRoadBillingSegment(sysRoadBillingSegment);
    }

    /**
     * 修改高速公路计费路段信息
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 结果
     */
    @Override
    public int updateSysRoadBillingSegment(SysRoadBillingSegment sysRoadBillingSegment)
    {
        sysRoadBillingSegment.setUpdateTime(DateUtils.getNowDate());
        return sysRoadBillingSegmentMapper.updateSysRoadBillingSegment(sysRoadBillingSegment);
    }

    /**
     * 批量删除高速公路计费路段信息
     * 
     * @param ids 需要删除的高速公路计费路段信息主键
     * @return 结果
     */
    @Override
    public int deleteSysRoadBillingSegmentByIds(Long[] ids)
    {
        return sysRoadBillingSegmentMapper.deleteSysRoadBillingSegmentByIds(ids);
    }

    /**
     * 删除高速公路计费路段信息信息
     * 
     * @param id 高速公路计费路段信息主键
     * @return 结果
     */
    @Override
    public int deleteSysRoadBillingSegmentById(Long id)
    {
        return sysRoadBillingSegmentMapper.deleteSysRoadBillingSegmentById(id);
    }

    /**
     * 根据公路编码和路段名称获取路段几何信息
     * 
     * @param code 公路编码
     * @param sectionName 路段名称
     * @return 路段几何信息
     */
    @Override
    public RoadSectionGeometryVO getRoadSectionGeometry(String code, String sectionName) {
        // 1. 查询该路段下所有计费路段
        List<Map<String, Object>> segments = sysRoadBillingSegmentMapper
            .selectGeometryByCodeAndSection(code, sectionName);
        
        if (segments.isEmpty()) {
            throw new ServiceException("未找到指定路段的几何信息");
        }
        
        // 2. 构建返回结果
        RoadSectionGeometryVO result = new RoadSectionGeometryVO();
        result.setRoadCode(code);
        result.setSectionName(sectionName);
        
        // 3. 计算总长度和路段数量
        BigDecimal totalLength = segments.stream()
            .filter(s -> s.get("length_road") != null)
            .map(s -> (BigDecimal) s.get("length_road"))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setTotalLength(totalLength);
        result.setSegmentCount(segments.size());
        
        // 4. 合并所有坐标点
        List<List<Double>> allCoordinates = mergeAllCoordinates(segments);
        result.setCoordinates(allCoordinates);
        
        return result;
    }

    /**
     * 根据road_marker_id获取路段几何信息
     * 
     * @param roadMarkerId 公路编号ID
     * @return 路段几何信息
     */
    @Override
    public RoadSectionGeometryVO getRoadSectionGeometryById(Long roadMarkerId) {
        // 1. 查询该路段下所有计费路段
        List<Map<String, Object>> segments = sysRoadBillingSegmentMapper
            .selectGeometryByRoadMarkerId(roadMarkerId);
        
        if (segments.isEmpty()) {
            throw new ServiceException("未找到指定路段的几何信息");
        }
        
        // 2. 构建返回结果
        RoadSectionGeometryVO result = new RoadSectionGeometryVO();
        Map<String, Object> firstSegment = segments.get(0);
        result.setRoadCode((String) firstSegment.get("road_code"));
        result.setSectionName((String) firstSegment.get("section_name"));
        
        // 3. 计算总长度和路段数量
        BigDecimal totalLength = segments.stream()
            .filter(s -> s.get("length_road") != null)
            .map(s -> (BigDecimal) s.get("length_road"))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.setTotalLength(totalLength);
        result.setSegmentCount(segments.size());
        
        // 4. 合并所有坐标点
        List<List<Double>> allCoordinates = mergeAllCoordinates(segments);
        result.setCoordinates(allCoordinates);
        
        return result;
    }

    /**
     * 根据road_marker_id查询计费路段列表
     *
     * @param roadMarkerId 公路编号ID
     * @return 计费路段集合
     */
    @Override
    public List<SysRoadBillingSegment> selectByRoadMarkerId(Long roadMarkerId) {
        return sysRoadBillingSegmentMapper.selectByRoadMarkerId(roadMarkerId);
    }

    /**
     * 获取所有路段的几何信息
     *
     * @return 所有路段几何信息集合
     */
    @Override
    public List<RoadSectionGeometryVO> getAllRoadSectionGeometry() {
        // 1. 查询所有路段的几何信息
        List<Map<String, Object>> allSegments = sysRoadBillingSegmentMapper.selectAllRoadSectionGeometry();

        if (allSegments.isEmpty()) {
            log.warn("未找到任何路段几何信息");
            return new ArrayList<>();
        }

        log.info("查询到 {} 个计费路段，开始按路段分组", allSegments.size());

        // 2. 按路段分组（code + sectionName）
        Map<String, List<Map<String, Object>>> groupedSegments = new HashMap<>();

        for (Map<String, Object> segment : allSegments) {
            String roadCode = (String) segment.get("road_code");
            String sectionName = (String) segment.get("section_name");

            if (roadCode != null && sectionName != null) {
                String key = roadCode + "-" + sectionName;
                groupedSegments.computeIfAbsent(key, k -> new ArrayList<>()).add(segment);
            }
        }

        log.info("分组完成，共 {} 个路段", groupedSegments.size());

        // 3. 为每个路段构建几何信息
        List<RoadSectionGeometryVO> results = new ArrayList<>();

        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedSegments.entrySet()) {
            String key = entry.getKey();
            List<Map<String, Object>> segments = entry.getValue();

            try {
                // 从第一个路段获取基本信息
                Map<String, Object> firstSegment = segments.get(0);
                String roadCode = (String) firstSegment.get("road_code");
                String sectionName = (String) firstSegment.get("section_name");

                RoadSectionGeometryVO result = new RoadSectionGeometryVO();
                result.setRoadCode(roadCode);
                result.setSectionName(sectionName);

                // 计算总长度和路段数量
                BigDecimal totalLength = segments.stream()
                    .filter(s -> s.get("length_road") != null)
                    .map(s -> (BigDecimal) s.get("length_road"))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                result.setTotalLength(totalLength);
                result.setSegmentCount(segments.size());

                // 合并坐标点
                List<List<Double>> allCoordinates = mergeAllCoordinates(segments);
                result.setCoordinates(allCoordinates);

                results.add(result);

                log.debug("路段 {} 处理完成，包含 {} 个计费路段，{} 个坐标点",
                    key, segments.size(), allCoordinates.size());

            } catch (Exception e) {
                log.warn("处理路段 {} 时发生异常: {}", key, e.getMessage(), e);
            }
        }

        log.info("所有路段几何信息处理完成，返回 {} 个路段", results.size());
        return results;
    }

    /**
     * 合并所有计费路段的坐标点
     */
    private List<List<Double>> mergeAllCoordinates(List<Map<String, Object>> segments) {
        List<List<Double>> allCoordinates = new ArrayList<>();

        if (segments == null || segments.isEmpty()) {
            log.warn("路段数据为空");
            return allCoordinates;
        }

        log.info("开始合并 {} 个路段的坐标点", segments.size());

        // 按桩号排序，确保路段顺序正确
        try {
            segments.sort((a, b) -> {
                try {
                    String km1A = (String) a.get("km1");
                    String km1B = (String) b.get("km1");
                    String directionA = (String) a.get("direction");
                    String directionB = (String) b.get("direction");

                    // 先按方向排序（上行优先），再按桩号排序
                    if (directionA != null && directionB != null && !directionA.equals(directionB)) {
                        return "上行".equals(directionA) ? -1 : 1;
                    }

                    // 简单的桩号比较（实际项目中可能需要更复杂的桩号解析）
                    if (km1A != null && km1B != null) {
                        return km1A.compareTo(km1B);
                    }
                    return 0;
                } catch (Exception e) {
                    log.warn("排序时出现异常: {}", e.getMessage());
                    return 0;
                }
            });
        } catch (Exception e) {
            log.warn("路段排序失败，使用原始顺序: {}", e.getMessage());
        }

        for (Map<String, Object> segment : segments) {
            String geomStr = (String) segment.get("geom");
            Object segmentId = segment.get("id");

            if (StringUtils.isNotEmpty(geomStr)) {
                try {
                    log.debug("处理路段ID: {}, 几何信息: {}", segmentId, geomStr);

                    // 解析GeoJSON几何信息
                    Map<String, Object> geometry = JSON.parseObject(geomStr, Map.class);

                    if ("LineString".equals(geometry.get("type"))) {
                        @SuppressWarnings("unchecked")
                        List<List<Object>> rawCoordinates = (List<List<Object>>) geometry.get("coordinates");

                        if (rawCoordinates != null && !rawCoordinates.isEmpty()) {
                            log.debug("路段ID: {} 包含 {} 个坐标点", segmentId, rawCoordinates.size());

                            // 将当前路段的所有坐标点添加到总坐标列表中
                            for (List<Object> rawCoordinate : rawCoordinates) {
                                try {
                                    // 转换坐标类型（处理BigDecimal转Double）
                                    List<Double> coordinate = convertToDoubleList(rawCoordinate);

                                    // 简单的重复坐标过滤（避免相邻重复点）
                                    if (allCoordinates.isEmpty() ||
                                        !isSameCoordinate(allCoordinates.get(allCoordinates.size() - 1), coordinate)) {
                                        allCoordinates.add(coordinate);
                                    }
                                } catch (Exception coordEx) {
                                    log.warn("转换坐标失败，路段ID: {}, 原始坐标: {}", segmentId, rawCoordinate, coordEx);
                                }
                            }
                        } else {
                            log.warn("路段ID: {} 的坐标数据为空", segmentId);
                        }
                    } else {
                        log.warn("路段ID: {} 的几何类型不是LineString: {}", segmentId, geometry.get("type"));
                    }
                } catch (Exception e) {
                    log.warn("解析几何信息失败，路段ID: {}, 几何信息: {}", segmentId, geomStr, e);
                }
            } else {
                log.warn("路段ID: {} 的几何信息为空", segmentId);
            }
        }

        log.info("坐标合并完成，总共合并了 {} 个坐标点", allCoordinates.size());
        return allCoordinates;
    }

    /**
     * 转换坐标列表类型（处理BigDecimal等类型转换为Double）
     */
    private List<Double> convertToDoubleList(List<Object> rawCoordinate) {
        List<Double> coordinate = new ArrayList<>();

        if (rawCoordinate == null || rawCoordinate.isEmpty()) {
            log.warn("原始坐标数据为空");
            return coordinate;
        }

        for (int i = 0; i < rawCoordinate.size(); i++) {
            Object value = rawCoordinate.get(i);
            try {
                if (value instanceof Number) {
                    double doubleValue = ((Number) value).doubleValue();
                    coordinate.add(doubleValue);
                } else if (value != null) {
                    // 如果不是数字类型，尝试解析字符串
                    double doubleValue = Double.parseDouble(value.toString());
                    coordinate.add(doubleValue);
                } else {
                    log.warn("坐标值[{}]为null，使用默认值0.0", i);
                    coordinate.add(0.0);
                }
            } catch (NumberFormatException e) {
                log.warn("无法解析坐标值[{}]: {}, 使用默认值0.0", i, value);
                coordinate.add(0.0); // 默认值
            } catch (Exception e) {
                log.warn("转换坐标值[{}]时发生异常: {}, 使用默认值0.0", i, value, e);
                coordinate.add(0.0);
            }
        }

        return coordinate;
    }

    /**
     * 判断两个坐标点是否相同（精度容差）
     */
    private boolean isSameCoordinate(List<Double> coord1, List<Double> coord2) {
        if (coord1.size() != coord2.size()) {
            return false;
        }

        double tolerance = 0.000001; // 坐标精度容差
        for (int i = 0; i < coord1.size(); i++) {
            if (Math.abs(coord1.get(i) - coord2.get(i)) > tolerance) {
                return false;
            }
        }
        return true;
    }
}
