package com.tocc.system.service;

import com.tocc.common.domain.vo.AdministrativeDivisionVO;

import java.util.List;

/**
 * 行政区划 Service 接口
 * 
 * <AUTHOR>
 */
public interface IAdministrativeDivisionService {
    
    /**
     * 获取完整的行政区划树形结构（四级）
     *
     * @return 树形结构的行政区划列表
     */
    List<AdministrativeDivisionVO> getDivisionTree();

    /**
     * 根据父级ID获取子级行政区划
     *
     * @param pid 父级ID，传入"0"或null获取省级
     * @return 子级行政区划列表
     */
    List<AdministrativeDivisionVO> getChildrenByPid(String pid);

    /**
     * 根据层级获取行政区划
     *
     * @param deep 层级深度 (0-省/直辖市, 1-市, 2-区/县, 3-街道/乡镇)
     * @return 指定层级的行政区划列表
     */
    List<AdministrativeDivisionVO> getDivisionsByDeep(Integer deep);

    /**
     * 根据ID获取行政区划详情
     *
     * @param id 行政区划ID
     * @return 行政区划详情
     */
    AdministrativeDivisionVO getDivisionById(String id);

    /**
     * 根据名称搜索行政区划
     *
     * @param name 名称关键字
     * @return 匹配的行政区划列表
     */
    List<AdministrativeDivisionVO> searchDivisionsByName(String name);
    
    /**
     * 获取指定ID的完整路径（从省到当前级别）
     *
     * @param id 行政区划ID
     * @return 完整路径列表
     */
    List<AdministrativeDivisionVO> getDivisionPath(String id);

    /**
     * 获取省级行政区划列表
     *
     * @return 省级行政区划列表
     */
    List<AdministrativeDivisionVO> getProvinces();

    /**
     * 获取指定省份下的市级行政区划
     *
     * @param provinceId 省份ID
     * @return 市级行政区划列表
     */
    List<AdministrativeDivisionVO> getCitiesByProvince(String provinceId);

    /**
     * 获取指定市下的区县级行政区划
     *
     * @param cityId 市ID
     * @return 区县级行政区划列表
     */
    List<AdministrativeDivisionVO> getDistrictsByCity(String cityId);

    /**
     * 获取指定区县下的街道/乡镇级行政区划
     *
     * @param districtId 区县ID
     * @return 街道/乡镇级行政区划列表
     */
    List<AdministrativeDivisionVO> getStreetsByDistrict(String districtId);
}
