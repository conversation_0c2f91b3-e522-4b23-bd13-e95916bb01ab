package com.tocc.system.service.impl;

import com.tocc.common.domain.vo.AdministrativeDivisionVO;
import com.tocc.system.mapper.AdministrativeDivisionMapper;
import com.tocc.system.service.IAdministrativeDivisionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 行政区划 Service 实现类
 * 
 * <AUTHOR>
 */
@Service
public class AdministrativeDivisionServiceImpl implements IAdministrativeDivisionService {
    
    @Autowired
    private AdministrativeDivisionMapper divisionMapper;
    
    @Override
    public List<AdministrativeDivisionVO> getDivisionTree() {
        // 获取所有行政区划数据
        List<AdministrativeDivisionVO> allDivisions = divisionMapper.selectAllDivisions();

        // 构建树形结构
        return buildTree(allDivisions, "0");
    }

    @Override
    public List<AdministrativeDivisionVO> getChildrenByPid(String pid) {
        if (pid == null) {
            pid = "0";
        }
        return divisionMapper.selectByPid(pid);
    }

    @Override
    public List<AdministrativeDivisionVO> getDivisionsByDeep(Integer deep) {
        return divisionMapper.selectByDeep(deep);
    }

    @Override
    public AdministrativeDivisionVO getDivisionById(String id) {
        return divisionMapper.selectById(id);
    }

    @Override
    public List<AdministrativeDivisionVO> searchDivisionsByName(String name) {
        return divisionMapper.selectByName(name);
    }
    
    @Override
    public List<AdministrativeDivisionVO> getDivisionPath(String id) {
        List<AdministrativeDivisionVO> path = new ArrayList<>();
        AdministrativeDivisionVO current = getDivisionById(id);

        while (current != null && !"0".equals(current.getPid())) {
            path.add(0, current); // 插入到开头，保持从省到当前的顺序
            current = getDivisionById(current.getPid());
        }

        // 添加省级（如果当前不是省级）
        if (current != null && !"0".equals(current.getId())) {
            path.add(0, current);
        }

        return path;
    }

    @Override
    public List<AdministrativeDivisionVO> getProvinces() {
        return getDivisionsByDeep(0);
    }

    @Override
    public List<AdministrativeDivisionVO> getCitiesByProvince(String provinceId) {
        return divisionMapper.selectByPid(provinceId);
    }

    @Override
    public List<AdministrativeDivisionVO> getDistrictsByCity(String cityId) {
        return divisionMapper.selectByPid(cityId);
    }

    @Override
    public List<AdministrativeDivisionVO> getStreetsByDistrict(String districtId) {
        return divisionMapper.selectByPid(districtId);
    }
    
    /**
     * 构建树形结构
     *
     * @param allDivisions 所有行政区划数据
     * @param parentId 父级ID
     * @return 树形结构列表
     */
    private List<AdministrativeDivisionVO> buildTree(List<AdministrativeDivisionVO> allDivisions, String parentId) {
        // 使用 Map 提高查找效率
        Map<String, List<AdministrativeDivisionVO>> pidMap = allDivisions.stream()
                .collect(Collectors.groupingBy(AdministrativeDivisionVO::getPid));

        return buildTreeRecursive(pidMap, parentId);
    }

    /**
     * 递归构建树形结构
     *
     * @param pidMap 按父级ID分组的Map
     * @param parentId 父级ID
     * @return 树形结构列表
     */
    private List<AdministrativeDivisionVO> buildTreeRecursive(Map<String, List<AdministrativeDivisionVO>> pidMap, String parentId) {
        List<AdministrativeDivisionVO> children = pidMap.get(parentId);
        if (children == null || children.isEmpty()) {
            return new ArrayList<>();
        }

        for (AdministrativeDivisionVO division : children) {
            // 递归设置子级
            List<AdministrativeDivisionVO> subChildren = buildTreeRecursive(pidMap, division.getId());
            if (!subChildren.isEmpty()) {
                division.setChildren(subChildren);
            }
        }

        return children;
    }
}
