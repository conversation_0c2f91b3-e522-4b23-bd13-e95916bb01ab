package com.tocc.system.service;

import java.util.List;
import com.tocc.system.domain.SysRoadBillingSegment;
import com.tocc.system.domain.vo.RoadSectionGeometryVO;

/**
 * 高速公路计费路段信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface ISysRoadBillingSegmentService 
{
    /**
     * 查询高速公路计费路段信息
     * 
     * @param id 高速公路计费路段信息主键
     * @return 高速公路计费路段信息
     */
    public SysRoadBillingSegment selectSysRoadBillingSegmentById(Long id);

    /**
     * 查询高速公路计费路段信息列表
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 高速公路计费路段信息集合
     */
    public List<SysRoadBillingSegment> selectSysRoadBillingSegmentList(SysRoadBillingSegment sysRoadBillingSegment);

    /**
     * 新增高速公路计费路段信息
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 结果
     */
    public int insertSysRoadBillingSegment(SysRoadBillingSegment sysRoadBillingSegment);

    /**
     * 修改高速公路计费路段信息
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 结果
     */
    public int updateSysRoadBillingSegment(SysRoadBillingSegment sysRoadBillingSegment);

    /**
     * 批量删除高速公路计费路段信息
     * 
     * @param ids 需要删除的高速公路计费路段信息主键集合
     * @return 结果
     */
    public int deleteSysRoadBillingSegmentByIds(Long[] ids);

    /**
     * 删除高速公路计费路段信息信息
     * 
     * @param id 高速公路计费路段信息主键
     * @return 结果
     */
    public int deleteSysRoadBillingSegmentById(Long id);

    /**
     * 根据公路编码和路段名称获取路段几何信息
     * 
     * @param code 公路编码
     * @param sectionName 路段名称
     * @return 路段几何信息
     */
    public RoadSectionGeometryVO getRoadSectionGeometry(String code, String sectionName);

    /**
     * 根据road_marker_id获取路段几何信息
     * 
     * @param roadMarkerId 公路编号ID
     * @return 路段几何信息
     */
    public RoadSectionGeometryVO getRoadSectionGeometryById(Long roadMarkerId);

    /**
     * 根据road_marker_id查询计费路段列表
     *
     * @param roadMarkerId 公路编号ID
     * @return 计费路段集合
     */
    public List<SysRoadBillingSegment> selectByRoadMarkerId(Long roadMarkerId);

    /**
     * 获取所有路段的几何信息
     *
     * @return 所有路段几何信息集合
     */
    public List<RoadSectionGeometryVO> getAllRoadSectionGeometry();

    /**
     * 根据公路编码和路段名称获取路段几何信息
     *
     * @param code 公路编码
     * @param sectionName 路段名称
     * @param simplify 是否启用坐标抽稀
     * @return 路段几何信息
     */
    public RoadSectionGeometryVO getRoadSectionGeometry(String code, String sectionName, Boolean simplify);

    /**
     * 根据road_marker_id获取路段几何信息
     *
     * @param roadMarkerId 公路编号ID
     * @param simplify 是否启用坐标抽稀
     * @return 路段几何信息
     */
    public RoadSectionGeometryVO getRoadSectionGeometryById(Long roadMarkerId, Boolean simplify);

    /**
     * 获取所有路段的几何信息
     *
     * @param simplify 是否启用坐标抽稀
     * @return 所有路段几何信息集合
     */
    public List<RoadSectionGeometryVO> getAllRoadSectionGeometry(Boolean simplify);
}
