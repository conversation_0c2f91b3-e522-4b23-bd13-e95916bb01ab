package com.tocc.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 公路编号对象 sys_road_marker
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
public class SysRoadMarker extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表ID */
    private Long id;

    /** 公路编号 */
    @Excel(name = "公路编号")
    private String code;

    /** 公路全称 */
    @Excel(name = "公路全称")
    private String allName;

    /** 公路简称 */
    @Excel(name = "公路简称")
    private String name;

    /** 道路类型 */
    @Excel(name = "道路类型", dictType = "road_type")
    private String roadType;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setAllName(String allName) 
    {
        this.allName = allName;
    }

    public String getAllName() 
    {
        return allName;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }

    public void setRoadType(String roadType)
    {
        this.roadType = roadType;
    }

    public String getRoadType()
    {
        return roadType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("code", getCode())
            .append("allName", getAllName())
            .append("name", getName())
            .append("roadType", getRoadType())
            .toString();
    }
}
