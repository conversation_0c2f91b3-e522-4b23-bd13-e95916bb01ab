package com.tocc.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 高速公路计费路段信息对象 sys_road_billing_segment
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
public class SysRoadBillingSegment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 计费路段ID */
    private Long id;

    /** 关联公路编号ID（可选关联） */
    @Excel(name = "关联公路编号ID")
    private Long roadMarkerId;

    /** 计费路段编码 */
    @Excel(name = "计费路段编码")
    private String code;

    /** 计费路段名称 */
    @Excel(name = "计费路段名称")
    private String name;

    /** 几何信息（GeoJSON格式） */
    @Excel(name = "几何信息")
    private String geom;

    /** 计费路段长度 */
    @Excel(name = "计费路段长度")
    private BigDecimal lengthRoad;

    /** 起点桩号 */
    @Excel(name = "起点桩号")
    private String km1;

    /** 终点桩号 */
    @Excel(name = "终点桩号")
    private String km2;

    /** 计费路段所属的小路段 */
    @Excel(name = "计费路段所属的小路段")
    private String roadMin;

    /** 计费路段所属的大路段 */
    @Excel(name = "计费路段所属的大路段")
    private String roadMax;

    /** 管养单位 */
    @Excel(name = "管养单位")
    private String company;

    /** 车道数（格式2｜1，辅车道） */
    @Excel(name = "车道数")
    private String carWayNum;

    /** 设计时速 */
    @Excel(name = "设计时速")
    private Integer planSpeed;

    /** 计费路段简称 */
    @Excel(name = "计费路段简称")
    private String alias;

    /** 计费路段方向 */
    @Excel(name = "计费路段方向")
    private String direction;

    /** 计费路段桩号 */
    @Excel(name = "计费路段桩号")
    private String milePost;

    /** 管养单位简称 */
    @Excel(name = "管养单位简称")
    private String companyAlias;

    /** 高速公路编码 */
    @Excel(name = "高速公路编码")
    private String highwayCode;

    /** 最小通行能力 */
    @Excel(name = "最小通行能力")
    private Integer minPcu;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setRoadMarkerId(Long roadMarkerId) 
    {
        this.roadMarkerId = roadMarkerId;
    }

    public Long getRoadMarkerId() 
    {
        return roadMarkerId;
    }

    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setGeom(String geom) 
    {
        this.geom = geom;
    }

    public String getGeom() 
    {
        return geom;
    }

    public void setLengthRoad(BigDecimal lengthRoad) 
    {
        this.lengthRoad = lengthRoad;
    }

    public BigDecimal getLengthRoad() 
    {
        return lengthRoad;
    }

    public void setKm1(String km1) 
    {
        this.km1 = km1;
    }

    public String getKm1() 
    {
        return km1;
    }

    public void setKm2(String km2) 
    {
        this.km2 = km2;
    }

    public String getKm2() 
    {
        return km2;
    }

    public void setRoadMin(String roadMin) 
    {
        this.roadMin = roadMin;
    }

    public String getRoadMin() 
    {
        return roadMin;
    }

    public void setRoadMax(String roadMax) 
    {
        this.roadMax = roadMax;
    }

    public String getRoadMax() 
    {
        return roadMax;
    }

    public void setCompany(String company) 
    {
        this.company = company;
    }

    public String getCompany() 
    {
        return company;
    }

    public void setCarWayNum(String carWayNum) 
    {
        this.carWayNum = carWayNum;
    }

    public String getCarWayNum() 
    {
        return carWayNum;
    }

    public void setPlanSpeed(Integer planSpeed) 
    {
        this.planSpeed = planSpeed;
    }

    public Integer getPlanSpeed() 
    {
        return planSpeed;
    }

    public void setAlias(String alias) 
    {
        this.alias = alias;
    }

    public String getAlias() 
    {
        return alias;
    }

    public void setDirection(String direction) 
    {
        this.direction = direction;
    }

    public String getDirection() 
    {
        return direction;
    }

    public void setMilePost(String milePost) 
    {
        this.milePost = milePost;
    }

    public String getMilePost() 
    {
        return milePost;
    }

    public void setCompanyAlias(String companyAlias) 
    {
        this.companyAlias = companyAlias;
    }

    public String getCompanyAlias() 
    {
        return companyAlias;
    }

    public void setHighwayCode(String highwayCode) 
    {
        this.highwayCode = highwayCode;
    }

    public String getHighwayCode() 
    {
        return highwayCode;
    }

    public void setMinPcu(Integer minPcu) 
    {
        this.minPcu = minPcu;
    }

    public Integer getMinPcu() 
    {
        return minPcu;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("roadMarkerId", getRoadMarkerId())
            .append("code", getCode())
            .append("name", getName())
            .append("geom", getGeom())
            .append("lengthRoad", getLengthRoad())
            .append("km1", getKm1())
            .append("km2", getKm2())
            .append("roadMin", getRoadMin())
            .append("roadMax", getRoadMax())
            .append("company", getCompany())
            .append("carWayNum", getCarWayNum())
            .append("planSpeed", getPlanSpeed())
            .append("alias", getAlias())
            .append("direction", getDirection())
            .append("milePost", getMilePost())
            .append("companyAlias", getCompanyAlias())
            .append("highwayCode", getHighwayCode())
            .append("minPcu", getMinPcu())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
