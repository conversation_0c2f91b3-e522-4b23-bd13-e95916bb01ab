package com.tocc.system.domain.dto;

import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 专家信息DTO
 * 
 * <AUTHOR>
 */
public class ExpertInfoDTO extends BaseEntity {
    
    /** 关联系统用户ID */
    @Excel(name = "用户ID")
    private Long userId;
    
    /** 专业领域 */
    @Excel(name = "专业领域")
    private String specialtyField;
    
    /** 职称 */
    @Excel(name = "职称")
    private String professionalTitle;
    
    /** 工作单位 */
    @Excel(name = "工作单位")
    private String workUnit;
    
    /** 学历 */
    @Excel(name = "学历")
    private String education;
    
    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;
    
    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;
    
    /** 联系地址 */
    @Excel(name = "联系地址")
    private String address;
    
    /** 经度 */
    private BigDecimal longitude;
    
    /** 纬度 */
    private BigDecimal latitude;
    
    /** 专业特长描述 */
    @Excel(name = "专业特长描述")
    private String expertiseDescription;
    
    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    // Getter and Setter methods
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSpecialtyField() {
        return specialtyField;
    }

    public void setSpecialtyField(String specialtyField) {
        this.specialtyField = specialtyField;
    }

    public String getProfessionalTitle() {
        return professionalTitle;
    }

    public void setProfessionalTitle(String professionalTitle) {
        this.professionalTitle = professionalTitle;
    }

    public String getWorkUnit() {
        return workUnit;
    }

    public void setWorkUnit(String workUnit) {
        this.workUnit = workUnit;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public String getExpertiseDescription() {
        return expertiseDescription;
    }

    public void setExpertiseDescription(String expertiseDescription) {
        this.expertiseDescription = expertiseDescription;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "ExpertInfoDTO{" +
                "userId=" + userId +
                ", specialtyField='" + specialtyField + '\'' +
                ", professionalTitle='" + professionalTitle + '\'' +
                ", workUnit='" + workUnit + '\'' +
                ", education='" + education + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", address='" + address + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", expertiseDescription='" + expertiseDescription + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
