package com.tocc.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import java.util.ArrayList;

/**
 * 组织架构树形结构VO
 * 
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OrganizationTreeVO {
    
    /** 节点ID（保留用于树形结构构建） */
    private String id;

    /** 部门ID */
    private Long deptId;

    /** 岗位ID */
    private Long postId;

    /** 用户ID */
    private Long userId;

    /** 节点名称 */
    private String name;

    /** 节点类型：dept-部门, post-岗位, user-用户 */
    private String type;

    /** 父节点ID */
    private String parentId;
    
    /** 联系电话 */
    private String phone;
    
    /** 邮箱 */
    private String email;
    
    /** 负责人（仅部门有） */
    private String leader;
    
    /** 用户昵称（仅用户有） */
    private String nickName;
    
    /** 用户名（仅用户有） */
    private String userName;
    
    /** 岗位编码（仅岗位有） */
    private String postCode;
    
    /** 状态 */
    private String status;
    
    /** 排序 */
    private Integer orderNum;
    
    /** 子节点 */
    private List<OrganizationTreeVO> children = new ArrayList<>();
    
    public OrganizationTreeVO() {
    }
    
    public OrganizationTreeVO(String id, String name, String type) {
        this.id = id;
        this.name = name;
        this.type = type;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getParentId() {
        return parentId;
    }
    
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getLeader() {
        return leader;
    }
    
    public void setLeader(String leader) {
        this.leader = leader;
    }
    
    public String getNickName() {
        return nickName;
    }
    
    public void setNickName(String nickName) {
        this.nickName = nickName;
    }
    
    public String getUserName() {
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public String getPostCode() {
        return postCode;
    }
    
    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Integer getOrderNum() {
        return orderNum;
    }
    
    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }
    
    public List<OrganizationTreeVO> getChildren() {
        return children;
    }
    
    public void setChildren(List<OrganizationTreeVO> children) {
        this.children = children;
    }
    
    public void addChild(OrganizationTreeVO child) {
        this.children.add(child);
    }
}
