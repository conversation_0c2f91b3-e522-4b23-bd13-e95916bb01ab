package com.tocc.system.domain.vo;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 路段几何信息返回VO
 * 
 * <AUTHOR>
 */
public class RoadSectionGeometryVO {
    
    /** 公路编码 */
    private String roadCode;
    
    /** 路段名称 */
    private String sectionName;
    
    /** 总长度 */
    private BigDecimal totalLength;
    
    /** 计费路段数量 */
    private Integer segmentCount;
    
    /** 完整的GeoJSON几何信息 */
    private Map<String, Object> geometry;
    
    public RoadSectionGeometryVO() {
    }
    
    public RoadSectionGeometryVO(String roadCode, String sectionName) {
        this.roadCode = roadCode;
        this.sectionName = sectionName;
    }
    
    public String getRoadCode() {
        return roadCode;
    }
    
    public void setRoadCode(String roadCode) {
        this.roadCode = roadCode;
    }
    
    public String getSectionName() {
        return sectionName;
    }
    
    public void setSectionName(String sectionName) {
        this.sectionName = sectionName;
    }
    
    public BigDecimal getTotalLength() {
        return totalLength;
    }
    
    public void setTotalLength(BigDecimal totalLength) {
        this.totalLength = totalLength;
    }
    
    public Integer getSegmentCount() {
        return segmentCount;
    }
    
    public void setSegmentCount(Integer segmentCount) {
        this.segmentCount = segmentCount;
    }
    
    public Map<String, Object> getGeometry() {
        return geometry;
    }
    
    public void setGeometry(Map<String, Object> geometry) {
        this.geometry = geometry;
    }
    
    @Override
    public String toString() {
        return "RoadSectionGeometryVO{" +
                "roadCode='" + roadCode + '\'' +
                ", sectionName='" + sectionName + '\'' +
                ", totalLength=" + totalLength +
                ", segmentCount=" + segmentCount +
                ", geometry=" + geometry +
                '}';
    }
}
