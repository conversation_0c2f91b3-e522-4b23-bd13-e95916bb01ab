package com.tocc.system.domain.vo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 路段几何信息返回VO
 *
 * <AUTHOR>
 */
public class RoadSectionGeometryVO {

    /** 公路编码 */
    private String roadCode;

    /** 路段名称 */
    private String sectionName;

    /** 总长度 */
    private BigDecimal totalLength;

    /** 计费路段数量 */
    private Integer segmentCount;

    /** 合并后的所有坐标点 */
    private List<List<Double>> coordinates;
    
    public RoadSectionGeometryVO() {
    }
    
    public RoadSectionGeometryVO(String roadCode, String sectionName) {
        this.roadCode = roadCode;
        this.sectionName = sectionName;
    }
    
    public String getRoadCode() {
        return roadCode;
    }
    
    public void setRoadCode(String roadCode) {
        this.roadCode = roadCode;
    }
    
    public String getSectionName() {
        return sectionName;
    }
    
    public void setSectionName(String sectionName) {
        this.sectionName = sectionName;
    }
    
    public BigDecimal getTotalLength() {
        return totalLength;
    }
    
    public void setTotalLength(BigDecimal totalLength) {
        this.totalLength = totalLength;
    }
    
    public Integer getSegmentCount() {
        return segmentCount;
    }
    
    public void setSegmentCount(Integer segmentCount) {
        this.segmentCount = segmentCount;
    }

    public List<List<Double>> getCoordinates() {
        return coordinates;
    }

    public void setCoordinates(List<List<Double>> coordinates) {
        this.coordinates = coordinates;
    }
    
    @Override
    public String toString() {
        return "RoadSectionGeometryVO{" +
                "roadCode='" + roadCode + '\'' +
                ", sectionName='" + sectionName + '\'' +
                ", totalLength=" + totalLength +
                ", segmentCount=" + segmentCount +
                ", coordinates=" + coordinates +
                '}';
    }
}
