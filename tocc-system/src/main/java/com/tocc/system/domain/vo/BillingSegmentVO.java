package com.tocc.system.domain.vo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计费路段VO
 * 
 * <AUTHOR>
 */
public class BillingSegmentVO {
    
    /** 计费路段ID */
    private Long id;
    
    /** 计费路段名称 */
    private String name;
    
    /** 计费路段编码 */
    private String code;
    
    /** 方向 */
    private String direction;
    
    /** 长度 */
    private BigDecimal length;
    
    /** 起点桩号 */
    private String kmStart;
    
    /** 终点桩号 */
    private String kmEnd;
    
    /** 管养单位 */
    private String company;
    
    /** 设计时速 */
    private Integer planSpeed;
    
    /** 车道数 */
    private String carWayNum;
    
    /** 坐标点列表 */
    private List<List<Double>> coordinates;
    
    public BillingSegmentVO() {
    }
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getDirection() {
        return direction;
    }
    
    public void setDirection(String direction) {
        this.direction = direction;
    }
    
    public BigDecimal getLength() {
        return length;
    }
    
    public void setLength(BigDecimal length) {
        this.length = length;
    }
    
    public String getKmStart() {
        return kmStart;
    }
    
    public void setKmStart(String kmStart) {
        this.kmStart = kmStart;
    }
    
    public String getKmEnd() {
        return kmEnd;
    }
    
    public void setKmEnd(String kmEnd) {
        this.kmEnd = kmEnd;
    }
    
    public String getCompany() {
        return company;
    }
    
    public void setCompany(String company) {
        this.company = company;
    }
    
    public Integer getPlanSpeed() {
        return planSpeed;
    }
    
    public void setPlanSpeed(Integer planSpeed) {
        this.planSpeed = planSpeed;
    }
    
    public String getCarWayNum() {
        return carWayNum;
    }
    
    public void setCarWayNum(String carWayNum) {
        this.carWayNum = carWayNum;
    }
    
    public List<List<Double>> getCoordinates() {
        return coordinates;
    }
    
    public void setCoordinates(List<List<Double>> coordinates) {
        this.coordinates = coordinates;
    }
    
    @Override
    public String toString() {
        return "BillingSegmentVO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", direction='" + direction + '\'' +
                ", length=" + length +
                ", kmStart='" + kmStart + '\'' +
                ", kmEnd='" + kmEnd + '\'' +
                ", company='" + company + '\'' +
                ", planSpeed=" + planSpeed +
                ", carWayNum='" + carWayNum + '\'' +
                ", coordinates=" + coordinates +
                '}';
    }
}
