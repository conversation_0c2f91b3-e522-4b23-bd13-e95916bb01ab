package com.tocc.system.domain.vo;

import java.util.List;
import java.util.ArrayList;

/**
 * 公路编号树形结构VO
 * 
 * <AUTHOR>
 */
public class RoadMarkerTreeVO {
    
    /** 节点ID */
    private String id;
    
    /** 节点标签 */
    private String label;
    
    /** 节点值 */
    private String value;
    
    /** 节点类型（type: 道路类型节点, marker: 公路编号节点） */
    private String nodeType;
    
    /** 道路类型（仅道路类型节点有值） */
    private String roadType;
    
    /** 公路编号（仅公路编号节点有值） */
    private String code;
    
    /** 公路全称 */
    private String allName;
    
    /** 公路简称 */
    private String name;
    
    /** 数据库ID（仅公路编号节点有值） */
    private Long markerId;
    
    /** 子节点 */
    private List<RoadMarkerTreeVO> children;
    
    /** 是否为叶子节点 */
    private Boolean isLeaf;
    
    public RoadMarkerTreeVO() {
        this.children = new ArrayList<>();
        this.isLeaf = false;
    }
    
    public RoadMarkerTreeVO(String id, String label, String nodeType) {
        this();
        this.id = id;
        this.label = label;
        this.nodeType = nodeType;
        this.value = id;
    }
    
    // Getter and Setter methods
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getLabel() {
        return label;
    }
    
    public void setLabel(String label) {
        this.label = label;
    }
    
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public String getNodeType() {
        return nodeType;
    }
    
    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }
    
    public String getRoadType() {
        return roadType;
    }
    
    public void setRoadType(String roadType) {
        this.roadType = roadType;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getAllName() {
        return allName;
    }
    
    public void setAllName(String allName) {
        this.allName = allName;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Long getMarkerId() {
        return markerId;
    }
    
    public void setMarkerId(Long markerId) {
        this.markerId = markerId;
    }
    
    public List<RoadMarkerTreeVO> getChildren() {
        return children;
    }
    
    public void setChildren(List<RoadMarkerTreeVO> children) {
        this.children = children;
    }
    
    public Boolean getIsLeaf() {
        return isLeaf;
    }
    
    public void setIsLeaf(Boolean isLeaf) {
        this.isLeaf = isLeaf;
    }
    
    /**
     * 添加子节点
     */
    public void addChild(RoadMarkerTreeVO child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
        this.isLeaf = false;
    }
    
    /**
     * 创建道路类型节点
     */
    public static RoadMarkerTreeVO createTypeNode(String roadType, String roadTypeName) {
        RoadMarkerTreeVO node = new RoadMarkerTreeVO("type_" + roadType, roadTypeName, "type");
        node.setRoadType(roadType);
        return node;
    }
    
    /**
     * 创建公路编号节点
     */
    public static RoadMarkerTreeVO createMarkerNode(Long markerId, String code, String name, String allName) {
        RoadMarkerTreeVO node = new RoadMarkerTreeVO("marker_" + markerId, code + " - " + name, "marker");
        node.setMarkerId(markerId);
        node.setCode(code);
        node.setName(name);
        node.setAllName(allName);
        node.setIsLeaf(true);
        return node;
    }
    
    @Override
    public String toString() {
        return "RoadMarkerTreeVO{" +
                "id='" + id + '\'' +
                ", label='" + label + '\'' +
                ", nodeType='" + nodeType + '\'' +
                ", roadType='" + roadType + '\'' +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", markerId=" + markerId +
                ", childrenCount=" + (children != null ? children.size() : 0) +
                ", isLeaf=" + isLeaf +
                '}';
    }
}
