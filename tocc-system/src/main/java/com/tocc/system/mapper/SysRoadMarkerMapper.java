package com.tocc.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.tocc.system.domain.SysRoadMarker;

/**
 * 公路编号Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
public interface SysRoadMarkerMapper 
{
    /**
     * 查询公路编号
     * 
     * @param id 公路编号主键
     * @return 公路编号
     */
    public SysRoadMarker selectSysRoadMarkerById(Long id);

    /**
     * 查询公路编号列表
     * 
     * @param sysRoadMarker 公路编号
     * @return 公路编号集合
     */
    public List<SysRoadMarker> selectSysRoadMarkerList(SysRoadMarker sysRoadMarker);

    /**
     * 新增公路编号
     * 
     * @param sysRoadMarker 公路编号
     * @return 结果
     */
    public int insertSysRoadMarker(SysRoadMarker sysRoadMarker);

    /**
     * 修改公路编号
     * 
     * @param sysRoadMarker 公路编号
     * @return 结果
     */
    public int updateSysRoadMarker(SysRoadMarker sysRoadMarker);

    /**
     * 删除公路编号
     * 
     * @param id 公路编号主键
     * @return 结果
     */
    public int deleteSysRoadMarkerById(Long id);

    /**
     * 批量删除公路编号
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysRoadMarkerByIds(Long[] ids);

    /**
     * 根据公路编码和路段名称查询公路编号
     *
     * @param code 公路编码
     * @param name 路段名称
     * @return 公路编号
     */
    public SysRoadMarker selectByCodeAndName(@Param("code") String code, @Param("name") String name);
}
