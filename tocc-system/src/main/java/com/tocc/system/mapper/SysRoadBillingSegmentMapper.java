package com.tocc.system.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.tocc.system.domain.SysRoadBillingSegment;

/**
 * 高速公路计费路段信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
public interface SysRoadBillingSegmentMapper 
{
    /**
     * 查询高速公路计费路段信息
     * 
     * @param id 高速公路计费路段信息主键
     * @return 高速公路计费路段信息
     */
    public SysRoadBillingSegment selectSysRoadBillingSegmentById(Long id);

    /**
     * 查询高速公路计费路段信息列表
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 高速公路计费路段信息集合
     */
    public List<SysRoadBillingSegment> selectSysRoadBillingSegmentList(SysRoadBillingSegment sysRoadBillingSegment);

    /**
     * 新增高速公路计费路段信息
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 结果
     */
    public int insertSysRoadBillingSegment(SysRoadBillingSegment sysRoadBillingSegment);

    /**
     * 修改高速公路计费路段信息
     * 
     * @param sysRoadBillingSegment 高速公路计费路段信息
     * @return 结果
     */
    public int updateSysRoadBillingSegment(SysRoadBillingSegment sysRoadBillingSegment);

    /**
     * 删除高速公路计费路段信息
     * 
     * @param id 高速公路计费路段信息主键
     * @return 结果
     */
    public int deleteSysRoadBillingSegmentById(Long id);

    /**
     * 批量删除高速公路计费路段信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysRoadBillingSegmentByIds(Long[] ids);

    /**
     * 根据公路编码和路段名称查询几何信息
     * 
     * @param code 公路编码
     * @param sectionName 路段名称
     * @return 几何信息集合
     */
    public List<Map<String, Object>> selectGeometryByCodeAndSection(@Param("code") String code, @Param("sectionName") String sectionName);

    /**
     * 根据road_marker_id查询几何信息
     * 
     * @param roadMarkerId 公路编号ID
     * @return 几何信息集合
     */
    public List<Map<String, Object>> selectGeometryByRoadMarkerId(@Param("roadMarkerId") Long roadMarkerId);

    /**
     * 根据road_marker_id查询计费路段列表
     *
     * @param roadMarkerId 公路编号ID
     * @return 计费路段集合
     */
    public List<SysRoadBillingSegment> selectByRoadMarkerId(@Param("roadMarkerId") Long roadMarkerId);

    /**
     * 查询所有路段的几何信息（按路段分组）
     *
     * @return 所有路段几何信息集合
     */
    public List<Map<String, Object>> selectAllRoadSectionGeometry();
}
