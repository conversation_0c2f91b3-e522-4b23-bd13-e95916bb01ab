package com.tocc.system.mapper;

import com.tocc.system.domain.vo.OrganizationTreeVO;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 组织架构树形结构 数据层
 * 
 * <AUTHOR>
 */
public interface OrganizationTreeMapper {
    
    /**
     * 查询所有部门信息
     * 
     * @param deptId 部门ID，为空时查询所有部门
     * @return 部门信息列表
     */
    List<OrganizationTreeVO> selectDeptList(@Param("deptId") Long deptId);
    
    /**
     * 根据部门ID查询该部门下的所有岗位
     * 
     * @param deptId 部门ID
     * @return 岗位信息列表
     */
    List<OrganizationTreeVO> selectPostsByDeptId(@Param("deptId") Long deptId);
    
    /**
     * 根据部门ID和岗位ID查询用户信息
     * 
     * @param deptId 部门ID
     * @param postId 岗位ID，为空时查询部门下所有用户
     * @return 用户信息列表
     */
    List<OrganizationTreeVO> selectUsersByDeptAndPost(@Param("deptId") Long deptId, @Param("postId") Long postId);
    
    /**
     * 查询完整的组织架构树形数据
     * 包含部门、岗位、用户的完整信息
     * 
     * @param deptId 根部门ID，为空时从顶级部门开始
     * @return 组织架构树形数据
     */
    List<OrganizationTreeVO> selectOrganizationTree(@Param("deptId") Long deptId);
}
