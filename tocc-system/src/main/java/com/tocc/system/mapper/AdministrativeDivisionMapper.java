package com.tocc.system.mapper;

import com.tocc.common.domain.vo.AdministrativeDivisionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 行政区划 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface AdministrativeDivisionMapper {
    
    /**
     * 查询所有行政区划
     *
     * @return 行政区划列表
     */
    List<AdministrativeDivisionVO> selectAllDivisions();

    /**
     * 根据父级ID查询子级行政区划
     *
     * @param pid 父级ID
     * @return 子级行政区划列表
     */
    List<AdministrativeDivisionVO> selectByPid(@Param("pid") String pid);

    /**
     * 根据层级查询行政区划
     *
     * @param deep 层级深度
     * @return 行政区划列表
     */
    List<AdministrativeDivisionVO> selectByDeep(@Param("deep") Integer deep);

    /**
     * 根据ID查询行政区划
     *
     * @param id 行政区划ID
     * @return 行政区划信息
     */
    AdministrativeDivisionVO selectById(@Param("id") String id);

    /**
     * 根据名称模糊查询行政区划
     *
     * @param name 名称关键字
     * @return 行政区划列表
     */
    List<AdministrativeDivisionVO> selectByName(@Param("name") String name);

    /**
     * 查询指定层级范围的行政区划
     *
     * @param minDeep 最小层级
     * @param maxDeep 最大层级
     * @return 行政区划列表
     */
    List<AdministrativeDivisionVO> selectByDeepRange(@Param("minDeep") Integer minDeep,
                                                  @Param("maxDeep") Integer maxDeep);
}
