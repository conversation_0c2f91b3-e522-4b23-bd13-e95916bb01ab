package com.tocc.em.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 应急预案版本信息VO
 * 用于查询预案的所有版本（包括当前版本和历史版本）
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@ApiModel("应急预案版本信息")
public class EmPrePlanVersionVO 
{
    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /** 预案ID（关联主表ID，历史版本记录的是原预案ID） */
    @ApiModelProperty(value = "预案ID")
    private String prePlanId;

    /** 预案名称 */
    @ApiModelProperty(value = "预案名称")
    private String planName;

    /** 版本号 */
    @ApiModelProperty(value = "版本号")
    private String version;

    /** 预案类型 */
    @ApiModelProperty(value = "预案类型")
    private String planType;

    /** 编制单位 */
    @ApiModelProperty(value = "编制单位")
    private String compilingDept;

    /** 适用单位 */
    @ApiModelProperty(value = "适用单位")
    private String applicableDeptIds;

    /** 预案状态(0草稿/1提交) */
    @ApiModelProperty(value = "预案状态(0草稿/1提交)")
    private Integer planStatus;

    /** 启用状态(0启用/1停止) */
    @ApiModelProperty(value = "启用状态(0启用/1停止)")
    private Integer enableStatus;

    /** 检查状态(0未检查/1已检查) */
    @ApiModelProperty(value = "检查状态(0未检查/1已检查)")
    private Integer checkStatus;

    /** 最新检查时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "最新检查时间")
    private Date lastCheckTime;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String creator;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 更新人 */
    @ApiModelProperty(value = "更新人")
    private String updater;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /** 修订人 */
    @ApiModelProperty(value = "修订人")
    private String reviser;

    /** 修订时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "修订时间")
    private Date revisionTime;

    /** 修订内容 */
    @ApiModelProperty(value = "修订内容")
    private String revisionContent;

    /** 版本类型（current-当前版本，history-历史版本） */
    @ApiModelProperty(value = "版本类型")
    private String versionType;

    /** 数据来源表（em_pre_plan-当前版本表，em_pre_plan_record-历史版本表） */
    @ApiModelProperty(value = "数据来源表")
    private String sourceTable;
} 