package com.tocc.em.service.impl;

import java.util.List;
import com.tocc.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmPrePlanRecordMapper;
import com.tocc.em.domain.EmPrePlanRecord;
import com.tocc.em.service.IEmPrePlanRecordService;

/**
 * 应急预案数据历史Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class EmPrePlanRecordServiceImpl implements IEmPrePlanRecordService 
{
    @Autowired
    private EmPrePlanRecordMapper emPrePlanRecordMapper;

    /**
     * 查询应急预案数据历史
     * 
     * @param id 应急预案数据历史主键
     * @return 应急预案数据历史
     */
    @Override
    public EmPrePlanRecord selectEmPrePlanRecordById(String id)
    {
        return emPrePlanRecordMapper.selectEmPrePlanRecordById(id);
    }

    /**
     * 查询应急预案数据历史列表
     * 
     * @param emPrePlanRecord 应急预案数据历史
     * @return 应急预案数据历史
     */
    @Override
    public List<EmPrePlanRecord> selectEmPrePlanRecordList(EmPrePlanRecord emPrePlanRecord)
    {
        return emPrePlanRecordMapper.selectEmPrePlanRecordList(emPrePlanRecord);
    }

    /**
     * 新增应急预案数据历史
     * 
     * @param emPrePlanRecord 应急预案数据历史
     * @return 结果
     */
    @Override
    public int insertEmPrePlanRecord(EmPrePlanRecord emPrePlanRecord)
    {
        emPrePlanRecord.setCreateTime(DateUtils.getNowDate());
        return emPrePlanRecordMapper.insertEmPrePlanRecord(emPrePlanRecord);
    }

    /**
     * 修改应急预案数据历史
     * 
     * @param emPrePlanRecord 应急预案数据历史
     * @return 结果
     */
    @Override
    public int updateEmPrePlanRecord(EmPrePlanRecord emPrePlanRecord)
    {
        emPrePlanRecord.setUpdateTime(DateUtils.getNowDate());
        return emPrePlanRecordMapper.updateEmPrePlanRecord(emPrePlanRecord);
    }

    /**
     * 批量删除应急预案数据历史
     * 
     * @param ids 需要删除的应急预案数据历史主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanRecordByIds(String[] ids)
    {
        return emPrePlanRecordMapper.deleteEmPrePlanRecordByIds(ids);
    }

    /**
     * 删除应急预案数据历史信息
     * 
     * @param id 应急预案数据历史主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanRecordById(String id)
    {
        return emPrePlanRecordMapper.deleteEmPrePlanRecordById(id);
    }
}
