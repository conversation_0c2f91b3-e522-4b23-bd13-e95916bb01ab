package com.tocc.em.service;

import java.util.List;
import com.tocc.em.domain.EmPrePlanDeptUser;
import com.tocc.em.dto.EmPrePlanDeptUserDTO;

/**
 * 预案组织体系人员Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IEmPrePlanDeptUserService 
{
    /**
     * 查询预案组织体系人员
     * 
     * @param id 预案组织体系人员主键
     * @return 预案组织体系人员
     */
    public EmPrePlanDeptUser selectEmPrePlanDeptUserById(String id);

    /**
     * 查询预案组织体系人员列表
     * 
     * @param emPrePlanDeptUser 预案组织体系人员
     * @return 预案组织体系人员集合
     */
    public List<EmPrePlanDeptUser> selectEmPrePlanDeptUserList(EmPrePlanDeptUser emPrePlanDeptUser);

    /**
     * 新增预案组织体系人员
     * 
     * @param emPrePlanDeptUser 预案组织体系人员
     * @return 结果
     */
    public int insertEmPrePlanDeptUser(EmPrePlanDeptUser emPrePlanDeptUser);

    /**
     * 修改预案组织体系人员
     * 
     * @param emPrePlanDeptUser 预案组织体系人员
     * @return 结果
     */
    public int updateEmPrePlanDeptUser(EmPrePlanDeptUser emPrePlanDeptUser);

    /**
     * 批量删除预案组织体系人员
     * 
     * @param ids 需要删除的预案组织体系人员主键集合
     * @return 结果
     */
    public int deleteEmPrePlanDeptUserByIds(String[] ids);

    /**
     * 删除预案组织体系人员信息
     * 
     * @param id 预案组织体系人员主键
     * @return 结果
     */
    public int deleteEmPrePlanDeptUserById(String id);

    void insertBatchEmPrePlanDeptUser(List<EmPrePlanDeptUserDTO> emPrePlanDeptUserDTOList);
}
