package com.tocc.em.service;

import java.time.LocalDateTime;
import java.util.List;
import com.tocc.em.domain.EmPrePlan;
import com.tocc.em.dto.EmPrePlanDTO;
import com.tocc.em.qo.EmPrePlanQO;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.em.vo.EmPrePlanVersionVO;
import com.tocc.em.vo.EmPrePlanCompareVO;

/**
 * 应急预案数据Service接口
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IEmPrePlanService
{
    /**
     * 查询应急预案数据
     *
     * @param id 应急预案数据主键
     * @return 应急预案数据
     */
    public EmPrePlanVO selectEmPrePlanById(String id);

    /**
     * 查询应急预案数据列表
     *
     * @param emPrePlanQO 应急预案数据
     * @return 应急预案数据集合
     */
    public List<EmPrePlan> selectEmPrePlanList(EmPrePlanQO emPrePlanQO );

    /**
     * 新增应急预案数据
     *
     * @param emPrePlanDTO 应急预案数据
     * @return 结果
     */
    public int insertEmPrePlan( EmPrePlanDTO emPrePlanDTO);

    /**
     * 修改应急预案数据
     *
     * @param emPrePlanDTO 应急预案数据
     * @return 结果
     */
    public int updateEmPrePlan(EmPrePlanDTO emPrePlanDTO);

    /**
     * 批量删除应急预案数据
     *
     * @param ids 需要删除的应急预案数据主键集合
     * @return 结果
     */
    public int deleteEmPrePlanByIds(String[] ids);

    /**
     * 删除应急预案数据信息
     *
     * @param id 应急预案数据主键
     * @return 结果
     */
    public int deleteEmPrePlanById(String id);

    /**
     * 查询更新超时的应急预案
     *
     * @param timeoutTime 超时时间点
     * @return 超时的应急预案集合
     */
    public List<EmPrePlanVO> selectTimeoutPlans(LocalDateTime timeoutTime);

    /**
     * 查询预案的所有版本（包括当前版本和历史版本）
     *
     * @param prePlanId 预案ID，如果为null则查询所有预案的版本
     * @return 版本列表，按照版本号降序排列
     */
    public List<EmPrePlanVersionVO> selectEmPrePlanVersionHistory(String prePlanId);

    /**
     * 根据预案名称查询预案的所有版本（包括当前版本和历史版本）
     *
     * @param planName 预案名称
     * @return 版本列表，按照版本号降序排列
     */
    public List<EmPrePlanVersionVO> selectEmPrePlanVersionHistoryByName(String planName);

    /**
     * 根据预案ID和版本号查询预案详情
     *
     * @param prePlanId 预案ID
     * @param version 版本号
     * @return 预案详情
     */
    public EmPrePlanVO selectEmPrePlanByIdAndVersion(String prePlanId, String version);

    /**
     * 比较当前版本与历史版本的预案内容
     *
     * @param prePlanId 预案ID
     * @param historyVersion 历史版本号
     * @return 比较结果
     */
    public EmPrePlanCompareVO compareVersions(String prePlanId, String historyVersion);

    EmPrePlanCompareVO compareVersionsByHistoryId(String historyVersionId);

    boolean isLatestVersion(String historyVersionId);
}
