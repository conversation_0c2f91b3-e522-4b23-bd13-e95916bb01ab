package com.tocc.em.service.impl;

import java.util.List;
import com.tocc.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmPrePlanFileMapper;
import com.tocc.em.domain.EmPrePlanFile;
import com.tocc.em.service.IEmPrePlanFileService;

/**
 * 应急预案附件管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class EmPrePlanFileServiceImpl implements IEmPrePlanFileService 
{
    @Autowired
    private EmPrePlanFileMapper emPrePlanFileMapper;

    /**
     * 查询应急预案附件管理
     * 
     * @param id 应急预案附件管理主键
     * @return 应急预案附件管理
     */
    @Override
    public EmPrePlanFile selectEmPrePlanFileById(String id)
    {
        return emPrePlanFileMapper.selectEmPrePlanFileById(id);
    }

    /**
     * 查询应急预案附件管理列表
     * 
     * @param emPrePlanFile 应急预案附件管理
     * @return 应急预案附件管理
     */
    @Override
    public List<EmPrePlanFile> selectEmPrePlanFileList(EmPrePlanFile emPrePlanFile)
    {
        return emPrePlanFileMapper.selectEmPrePlanFileList(emPrePlanFile);
    }

    /**
     * 新增应急预案附件管理
     * 
     * @param emPrePlanFile 应急预案附件管理
     * @return 结果
     */
    @Override
    public int insertEmPrePlanFile(EmPrePlanFile emPrePlanFile)
    {
        emPrePlanFile.setCreateTime(DateUtils.getNowDate());
        return emPrePlanFileMapper.insertEmPrePlanFile(emPrePlanFile);
    }

    /**
     * 修改应急预案附件管理
     * 
     * @param emPrePlanFile 应急预案附件管理
     * @return 结果
     */
    @Override
    public int updateEmPrePlanFile(EmPrePlanFile emPrePlanFile)
    {
        emPrePlanFile.setUpdateTime(DateUtils.getNowDate());
        return emPrePlanFileMapper.updateEmPrePlanFile(emPrePlanFile);
    }

    /**
     * 批量删除应急预案附件管理
     * 
     * @param ids 需要删除的应急预案附件管理主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanFileByIds(String[] ids)
    {
        return emPrePlanFileMapper.deleteEmPrePlanFileByIds(ids);
    }

    /**
     * 删除应急预案附件管理信息
     * 
     * @param id 应急预案附件管理主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanFileById(String id)
    {
        return emPrePlanFileMapper.deleteEmPrePlanFileById(id);
    }
}
