package com.tocc.em.service;


import com.tocc.em.domain.EmWarehouse;

import java.util.List;

/**
 * 应急物资仓库基础信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
public interface IEmWarehouseService 
{
    /**
     * 查询应急物资仓库基础信息
     * 
     * @param id 应急物资仓库基础信息主键
     * @return 应急物资仓库基础信息
     */
    public EmWarehouse selectEmWarehouseById(String id);

    /**
     * 查询应急物资仓库基础信息列表
     * 
     * @param emWarehouse 应急物资仓库基础信息
     * @return 应急物资仓库基础信息集合
     */
    public List<EmWarehouse> selectEmWarehouseList(EmWarehouse emWarehouse);

    /**
     * 新增应急物资仓库基础信息
     * 
     * @param emWarehouse 应急物资仓库基础信息
     * @return 结果
     */
    public int insertEmWarehouse(EmWarehouse emWarehouse);

    /**
     * 修改应急物资仓库基础信息
     * 
     * @param emWarehouse 应急物资仓库基础信息
     * @return 结果
     */
    public int updateEmWarehouse(EmWarehouse emWarehouse);

    /**
     * 批量删除应急物资仓库基础信息
     * 
     * @param ids 需要删除的应急物资仓库基础信息主键集合
     * @return 结果
     */
    public int deleteEmWarehouseByIds(String[] ids);

    /**
     * 删除应急物资仓库基础信息信息
     * 
     * @param id 应急物资仓库基础信息主键
     * @return 结果
     */
    public int deleteEmWarehouseById(String id);
}
