package com.tocc.em.service.impl;

import java.util.List;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.bean.BeanUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.em.dto.EmPrePlanDeptUserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmPrePlanDeptUserMapper;
import com.tocc.em.domain.EmPrePlanDeptUser;
import com.tocc.em.service.IEmPrePlanDeptUserService;

/**
 * 预案组织体系人员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class EmPrePlanDeptUserServiceImpl implements IEmPrePlanDeptUserService 
{
    @Autowired
    private EmPrePlanDeptUserMapper emPrePlanDeptUserMapper;

    /**
     * 查询预案组织体系人员
     * 
     * @param id 预案组织体系人员主键
     * @return 预案组织体系人员
     */
    @Override
    public EmPrePlanDeptUser selectEmPrePlanDeptUserById(String id)
    {
        return emPrePlanDeptUserMapper.selectEmPrePlanDeptUserById(id);
    }

    /**
     * 查询预案组织体系人员列表
     * 
     * @param emPrePlanDeptUser 预案组织体系人员
     * @return 预案组织体系人员
     */
    @Override
    public List<EmPrePlanDeptUser> selectEmPrePlanDeptUserList(EmPrePlanDeptUser emPrePlanDeptUser)
    {
        return emPrePlanDeptUserMapper.selectEmPrePlanDeptUserList(emPrePlanDeptUser);
    }

    /**
     * 新增预案组织体系人员
     * 
     * @param emPrePlanDeptUser 预案组织体系人员
     * @return 结果
     */
    @Override
    public int insertEmPrePlanDeptUser(EmPrePlanDeptUser emPrePlanDeptUser)
    {
        emPrePlanDeptUser.setCreateTime(DateUtils.getNowDate());
        return emPrePlanDeptUserMapper.insertEmPrePlanDeptUser(emPrePlanDeptUser);
    }

    /**
     * 修改预案组织体系人员
     * 
     * @param emPrePlanDeptUser 预案组织体系人员
     * @return 结果
     */
    @Override
    public int updateEmPrePlanDeptUser(EmPrePlanDeptUser emPrePlanDeptUser)
    {
        emPrePlanDeptUser.setUpdateTime(DateUtils.getNowDate());
        return emPrePlanDeptUserMapper.updateEmPrePlanDeptUser(emPrePlanDeptUser);
    }

    /**
     * 批量删除预案组织体系人员
     * 
     * @param ids 需要删除的预案组织体系人员主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanDeptUserByIds(String[] ids)
    {
        return emPrePlanDeptUserMapper.deleteEmPrePlanDeptUserByIds(ids);
    }

    /**
     * 删除预案组织体系人员信息
     * 
     * @param id 预案组织体系人员主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanDeptUserById(String id)
    {
        return emPrePlanDeptUserMapper.deleteEmPrePlanDeptUserById(id);
    }

    @Override
    public void insertBatchEmPrePlanDeptUser(List<EmPrePlanDeptUserDTO> emPrePlanDeptUserDTOList) {
        String userName = SecurityUtils.getLoginUser().getUser().getUserName();
        for (EmPrePlanDeptUserDTO emPrePlanDeptUserDTO : emPrePlanDeptUserDTOList) {
            EmPrePlanDeptUser emPrePlanDeptUser = new EmPrePlanDeptUser();
            BeanUtils.copyProperties(emPrePlanDeptUserDTO,emPrePlanDeptUser);
            emPrePlanDeptUser.setId(IdUtils.fastSimpleUUID());
            emPrePlanDeptUser.setCreator(userName);
            emPrePlanDeptUser.setUpdater(userName);
            emPrePlanDeptUserMapper.insertEmPrePlanDeptUser(emPrePlanDeptUser);
        }
    }
}
