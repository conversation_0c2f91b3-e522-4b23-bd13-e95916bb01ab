package com.tocc.em.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.tocc.common.core.domain.entity.SysRole;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.UUID;
import com.tocc.em.domain.EmPrePlanDeptUser;
import com.tocc.em.dto.EmPrePlanDeptDTO;
import com.tocc.em.dto.EmPrePlanDeptUserDTO;
import com.tocc.em.service.IEmPrePlanDeptUserService;
import com.tocc.system.domain.SysPost;
import com.tocc.system.service.ISysPostService;
import com.tocc.system.service.ISysRoleService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmPrePlanDeptMapper;
import com.tocc.em.domain.EmPrePlanDept;
import com.tocc.em.service.IEmPrePlanDeptService;

import javax.annotation.Resource;

/**
 * 应急预案组织体系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class EmPrePlanDeptServiceImpl implements IEmPrePlanDeptService
{
    @Autowired
    private EmPrePlanDeptMapper emPrePlanDeptMapper;


    @Resource
    private IEmPrePlanDeptUserService iEmPrePlanDeptUserService ;

    @Resource
    private ISysRoleService roleService;

    @Resource
    private ISysPostService postService;
    /**
     * 查询应急预案组织体系
     *
     * @param id 应急预案组织体系主键
     * @return 应急预案组织体系
     */
    @Override
    public EmPrePlanDept selectEmPrePlanDeptById(String id)
    {
        return emPrePlanDeptMapper.selectEmPrePlanDeptById(id);
    }

    /**
     * 查询应急预案组织体系列表
     *
     * @param emPrePlanDept 应急预案组织体系
     * @return 应急预案组织体系
     */
    @Override
    public List<EmPrePlanDept> selectEmPrePlanDeptList(EmPrePlanDept emPrePlanDept)
    {
        return emPrePlanDeptMapper.selectEmPrePlanDeptList(emPrePlanDept);
    }

    /**
     * 新增应急预案组织体系
     *
     * @param emPrePlanDept 应急预案组织体系
     * @return 结果
     */
    @Override
    public int insertEmPrePlanDept(EmPrePlanDept emPrePlanDept)
    {
        emPrePlanDept.setCreateTime(DateUtils.getNowDate());
        return emPrePlanDeptMapper.insertEmPrePlanDept(emPrePlanDept);
    }

    /**
     * 修改应急预案组织体系
     *
     * @param emPrePlanDept 应急预案组织体系
     * @return 结果
     */
    @Override
    public int updateEmPrePlanDept(EmPrePlanDept emPrePlanDept)
    {
        emPrePlanDept.setUpdateTime(DateUtils.getNowDate());
        return emPrePlanDeptMapper.updateEmPrePlanDept(emPrePlanDept);
    }

    /**
     * 批量删除应急预案组织体系
     *
     * @param ids 需要删除的应急预案组织体系主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanDeptByIds(String[] ids)
    {
        return emPrePlanDeptMapper.deleteEmPrePlanDeptByIds(ids);
    }
    @Override
    public List<EmPrePlanDeptDTO> selectEmPrePlanDeptListWithTree(EmPrePlanDept emPrePlanDept) {
        System.out.println("=== selectEmPrePlanDeptListWithTree 调试信息开始 ===");
        System.out.println("查询参数: prePlanId=" + emPrePlanDept.getPrePlanId() + 
                          ", eventLevel=" + emPrePlanDept.getEventLevel());
        
        List<EmPrePlanDept> emPrePlanDepts = this.selectEmPrePlanDeptList(emPrePlanDept);
        System.out.println("查询到的EmPrePlanDept原始数据大小: " + emPrePlanDepts.size());
        
        List<EmPrePlanDeptDTO> emPrePlanDeptDTOList = new ArrayList<>(emPrePlanDepts.size());
        //获取所有角色
        List<SysRole>  roles= roleService.selectRoleList(new SysRole());
        //获取所有职位
        List<SysPost> posts = postService.selectPostAll();
        for (EmPrePlanDept prePlanDept : emPrePlanDepts) {
            EmPrePlanDeptDTO emPrePlanDeptDTO = new EmPrePlanDeptDTO();
            BeanUtils.copyProperties(prePlanDept, emPrePlanDeptDTO);

            // Initialize string builders
            StringBuilder leader = new StringBuilder();
            StringBuilder leaderAss = new StringBuilder();
            StringBuilder members = new StringBuilder();
            StringBuilder experts = new StringBuilder();

            // Get department users
            List<EmPrePlanDeptUser> emPrePlanDeptUsers = getDeptUsers(prePlanDept.getId());
            List<EmPrePlanDeptUserDTO> emPrePlanDeptUserDTOList = new ArrayList<>(emPrePlanDeptUsers.size());

            for (EmPrePlanDeptUser user : emPrePlanDeptUsers) {
                EmPrePlanDeptUserDTO userDTO = new EmPrePlanDeptUserDTO();
                BeanUtils.copyProperties(user, userDTO);

                // Set role information
                setRoleInfo(roles, userDTO);

                // Process user information
                processUserInfo(user, userDTO, leader, leaderAss, members, experts, posts);

                emPrePlanDeptUserDTOList.add(userDTO);
            }

            // Set the aggregated information
            setAggregatedInfo(emPrePlanDeptDTO, leader, leaderAss, members, experts);
            
            // 设置用户列表到部门DTO中
            emPrePlanDeptDTO.setEmPrePlanDeptUserDTOList(emPrePlanDeptUserDTOList);

            emPrePlanDeptDTOList.add(emPrePlanDeptDTO);
        }

        System.out.println("转换后的EmPrePlanDeptDTO数据大小: " + emPrePlanDeptDTOList.size());
        System.out.println("=== selectEmPrePlanDeptListWithTree 调试信息结束 ===");
        
        return   buildTree(emPrePlanDeptDTOList);

    }
    // Helper methods

    private List<EmPrePlanDeptUser> getDeptUsers(String deptId) {
        EmPrePlanDeptUser query = new EmPrePlanDeptUser();
        query.setEmDeptId(deptId);
        return iEmPrePlanDeptUserService.selectEmPrePlanDeptUserList(query);
    }

    private void setRoleInfo(List<SysRole> roles, EmPrePlanDeptUserDTO userDTO) {
        String roleId = userDTO.getRoleId();
        roles.stream()
                .filter(role -> role.getRoleId().longValue() == Long.valueOf(roleId))
                .findFirst()
                .ifPresent(role -> userDTO.setRoleName(role.getRoleName()));
    }

    private void processUserInfo(EmPrePlanDeptUser user, EmPrePlanDeptUserDTO userDTO,
                                 StringBuilder leader, StringBuilder leaderAss,
                                 StringBuilder members, StringBuilder experts,
                                 List<SysPost> posts) {
        if (userDTO.getPost() == null || userDTO.getPost().isEmpty()) {
            return;
        }

        String postNames = getPostNames(userDTO.getPost(), posts);
        userDTO.setPostName(postNames);

        String contact = userDTO.getContact();
        String name = user.getLeaderName();
        String roleId = userDTO.getRoleId();

        if (isLeaderRole(roleId)) {
            appendFormattedUser(leader, name, postNames, contact);
        } else if (isLeaderAssRole(roleId)) {
            appendFormattedUser(leaderAss, name, postNames, contact);
        } else if (isMemberRole(roleId)) {
            appendFormattedUserWithComma(members, name, postNames, contact);
        } else if (isExpertRole(roleId)) {
            appendFormattedUserWithComma(experts, name, postNames, contact);
        }
    }

    private String getPostNames(String postIds, List<SysPost> posts) {
        return Arrays.stream(postIds.split(","))
                .map(postId -> posts.stream()
                        .filter(post -> post.getPostId().longValue() == Long.valueOf(postId))
                        .findFirst()
                        .map(SysPost::getPostName)
                        .orElse(""))
                .filter(name -> !name.isEmpty())
                .collect(Collectors.joining(","));
    }

    private boolean isLeaderRole(String roleId) {
        Set<String> roles = Collections.unmodifiableSet(
                new HashSet<>(Arrays.asList("100", "103", "106"))
        );
        return roles.contains(roleId);
    }



    private boolean isLeaderAssRole(String roleId) {
        Set<String> roles = Collections.unmodifiableSet(
                new HashSet<>(Arrays.asList("101", "104", "107"))
        );
        return roles.contains(roleId);
    }


    private boolean isMemberRole(String roleId) {
        return "102".equals(roleId);
    }

    private boolean isExpertRole(String roleId) {
        return "105".equals(roleId); // Experts have roleId 105
    }

    private void appendFormattedUser(StringBuilder builder, String name, String postNames, String contact) {
        builder.append(name)
                .append("(").append(postNames).append(")")
                .append(" ").append(contact);
    }

    private void appendFormattedUserWithComma(StringBuilder builder, String name, String postNames, String contact) {
        appendFormattedUser(builder, name, postNames, contact);
        builder.append("，");
    }

    private void setAggregatedInfo(EmPrePlanDeptDTO dto,
                                   StringBuilder leader, StringBuilder leaderAss,
                                   StringBuilder members, StringBuilder experts) {
        dto.setLeader(leader.toString());
        dto.setLeaderAss(leaderAss.toString());

        if (members.length() > 0) {
            dto.setMember(removeLastComma(members));
        }

        if (experts.length() > 0) {
            dto.setPro(removeLastComma(experts)); // Experts go to pro field
        }
    }

    private String removeLastComma(StringBuilder builder) {
        return builder.deleteCharAt(builder.length() - 1).toString();
    }
    /**
     * 将扁平列表转换为树形结构
     * @param list 包含所有节点的扁平列表
     * @return 完整的树形结构
     */
    public static List<EmPrePlanDeptDTO> buildTree(List<EmPrePlanDeptDTO> list) {
        System.out.println("=== buildTree 调试信息开始 ===");
        System.out.println("传入的list大小: " + (list != null ? list.size() : "null"));
        
        if (list == null || list.isEmpty()) {
            System.out.println("传入的list为空，返回空结果");
            return new ArrayList<>();
        }
        
        // 打印传入的数据
        for (int i = 0; i < list.size(); i++) {
            EmPrePlanDeptDTO node = list.get(i);
            System.out.println("节点[" + i + "]: id=" + node.getId() + 
                             ", parentId=" + node.getParentId() + 
                             ", deptName=" + node.getDeptName());
        }
        
        // 创建结果集合和临时map
        List<EmPrePlanDeptDTO> result = new ArrayList<>();
        Map<String, EmPrePlanDeptDTO> nodeMap = new HashMap<>();

        // 第一次遍历：将所有节点存入map
        for (EmPrePlanDeptDTO node : list) {
            nodeMap.put(node.getId(), node);
            if (node.getChildren() == null) {
                node.setChildren(new ArrayList<>());
            }
        }
        System.out.println("nodeMap大小: " + nodeMap.size());

        // 第二次遍历：建立父子关系
        for (EmPrePlanDeptDTO node : list) {
            String parentId = node.getParentId();
            if (parentId == null || parentId.isEmpty()) {
                // 顶级节点
                System.out.println("找到顶级节点: " + node.getDeptName() + " (id=" + node.getId() + ")");
                result.add(node);
            } else {
                // 子节点处理
                EmPrePlanDeptDTO parent = nodeMap.get(parentId);
                if (parent != null) {
                    System.out.println("将节点 " + node.getDeptName() + " 添加到父节点 " + parent.getDeptName() + " 下");
                    parent.getChildren().add(node);
                    // 设置机构级别
                    node.setDeptLevel(1);
                } else {
                    // 父节点不存在，将其作为根节点处理
                    System.out.println("警告：未找到父节点 parentId=" + parentId + " 对应的节点，将子节点=" + node.getDeptName() + " 作为根节点处理");
                    result.add(node);
                }
            }
        }
        
        // 对根节点按order_num排序
        result.sort((a, b) -> {
            Integer orderA = a.getOrderNum();
            Integer orderB = b.getOrderNum();
            
            // 处理null值，null值放在最后
            if (orderA == null && orderB == null) return 0;
            if (orderA == null) return 1;
            if (orderB == null) return -1;
            
            return orderA.compareTo(orderB);
        });
        
        // 对所有节点的子节点按order_num排序
        sortChildrenByOrderNum(result);
        
        System.out.println("最终result大小: " + result.size());
        System.out.println("对根节点进行排序完成");
        System.out.println("=== buildTree 调试信息结束 ===");
        return result;
    }

    /**
     * 递归地对所有节点的子节点按order_num排序
     */
    private static void sortChildrenByOrderNum(List<EmPrePlanDeptDTO> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }
        
        for (EmPrePlanDeptDTO node : nodes) {
            List<EmPrePlanDeptDTO> children = node.getChildren();
            if (children != null && !children.isEmpty()) {
                // 对子节点按order_num排序
                children.sort((a, b) -> {
                    Integer orderA = a.getOrderNum();
                    Integer orderB = b.getOrderNum();
                    
                    // 处理null值，null值放在最后
                    if (orderA == null && orderB == null) return 0;
                    if (orderA == null) return 1;
                    if (orderB == null) return -1;
                    
                    return orderA.compareTo(orderB);
                });
                
                System.out.println("对节点 " + node.getDeptName() + " 的子节点进行排序，子节点数量: " + children.size());
                
                // 递归排序子节点的子节点
                sortChildrenByOrderNum(children);
            }
        }
    }
    /**
     * 删除应急预案组织体系信息
     *
     * @param id 应急预案组织体系主键
     * @return 结果
     */
    @Override
    public int deleteEmPrePlanDeptById(String id)
    {
        return emPrePlanDeptMapper.deleteEmPrePlanDeptById(id);
    }

    @Override
    public void saveEmPrePlanDept(String version, String emPrePlanID, List<EmPrePlanDeptDTO> emPrePlanDeptDTOList) {
        String loginUserName = SecurityUtils.getLoginUser().getUser().getUserName();
        //构建树形
        generateIds(emPrePlanDeptDTOList,emPrePlanID);
        List<EmPrePlanDeptDTO> flatList = flattenTree(emPrePlanDeptDTOList);
        List<EmPrePlanDept> flatListFinal = new ArrayList<>();
        // 2. 批量保存
        flatList.forEach(dept -> {

            EmPrePlanDept  emPrePlanDept = new EmPrePlanDept();
            BeanUtils.copyProperties(dept,emPrePlanDept);
            emPrePlanDept.setCreator(loginUserName);
            emPrePlanDept.setUpdater(loginUserName);
            emPrePlanDept.setVersion(version);
            flatListFinal.add(emPrePlanDept);
            //机构人员
            List<EmPrePlanDeptUserDTO> emPrePlanDeptUserDTOList = dept.getEmPrePlanDeptUserDTOList();
            if(null != emPrePlanDeptUserDTOList && !emPrePlanDeptUserDTOList.isEmpty()){
                emPrePlanDeptUserDTOList.forEach(
                        e -> {
                            e.setEmDeptId(dept.getId());
                        }
                );
                iEmPrePlanDeptUserService.insertBatchEmPrePlanDeptUser(emPrePlanDeptUserDTOList);
            }
        });
        for (EmPrePlanDept emPrePlanDept : flatListFinal) {
            emPrePlanDeptMapper.insertEmPrePlanDept(emPrePlanDept);
        }
    }



    // 树形结构扁平化
    public static List<EmPrePlanDeptDTO> flattenTree(List<EmPrePlanDeptDTO> tree) {
        List<EmPrePlanDeptDTO> result = new ArrayList<>();
        flattenNodes(tree, result);
        return result;
    }

    private static void flattenNodes(List<EmPrePlanDeptDTO> nodes,
                                     List<EmPrePlanDeptDTO> result) {
        for (EmPrePlanDeptDTO node : nodes) {
            result.add(node);
            if (node.getChildren() != null) {
                flattenNodes(node.getChildren(), result);
            }
        }
    }
    public static void generateIds(List<EmPrePlanDeptDTO> treeList,String emPrePlanID) {
        if (treeList == null) return;

        for (EmPrePlanDeptDTO node : treeList) {
            // 生成节点ID
            node.setId(UUID.randomUUID().toString());
            node.setPrePlanId(emPrePlanID);
            // 处理子节点
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                for (EmPrePlanDeptDTO child : node.getChildren()) {
                    // 设置子节点的父ID
                    child.setParentId(node.getId());
                    // 设置子节点级别
                    child.setDeptLevel(1);
                }
                // 递归处理子节点
                generateIds(node.getChildren(),emPrePlanID);
            }
        }
    }
}
