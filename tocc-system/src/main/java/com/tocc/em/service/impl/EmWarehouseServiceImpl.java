package com.tocc.em.service.impl;

import java.util.List;

import com.tocc.common.map.domain.MapVO;
import com.tocc.common.map.service.IMapService;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.em.domain.EmWarehouse;
import com.tocc.em.mapper.EmWarehouseMapper;
import com.tocc.em.service.IEmWarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 应急物资仓库基础信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@Service
public class EmWarehouseServiceImpl implements IEmWarehouseService
{
    @Autowired
    private EmWarehouseMapper emWarehouseMapper;

    @Autowired
    private IMapService mapService;

    /**
     * 查询应急物资仓库基础信息
     * 
     * @param id 应急物资仓库基础信息主键
     * @return 应急物资仓库基础信息
     */
    @Override
    public EmWarehouse selectEmWarehouseById(String id)
    {
        return emWarehouseMapper.selectEmWarehouseById(id);
    }

    /**
     * 查询应急物资仓库基础信息列表
     * 
     * @param emWarehouse 应急物资仓库基础信息
     * @return 应急物资仓库基础信息
     */
    @Override
    public List<EmWarehouse> selectEmWarehouseList(EmWarehouse emWarehouse)
    {
        return emWarehouseMapper.selectEmWarehouseList(emWarehouse);
    }

    /**
     * 新增应急物资仓库基础信息
     * 
     * @param emWarehouse 应急物资仓库基础信息
     * @return 结果
     */
    @Override
    public int insertEmWarehouse(EmWarehouse emWarehouse)
    {   emWarehouse.setId(IdUtils.fastSimpleUUID());
        try{
            MapVO lonAndLatByAddress = mapService.getLonAndLatByAddress(emWarehouse.getAddress());
            emWarehouse.setLatitude(lonAndLatByAddress.getLng());
            emWarehouse.setLongitude(lonAndLatByAddress.getLat());
        }catch (Exception e){
            e.printStackTrace();
        }
        String userName = SecurityUtils.getLoginUser().getUser().getUserName();
        emWarehouse.setCreator(userName);
        emWarehouse.setUpdater(userName);
        return emWarehouseMapper.insertEmWarehouse(emWarehouse);
    }

    /**
     * 修改应急物资仓库基础信息
     * 
     * @param emWarehouse 应急物资仓库基础信息
     * @return 结果
     */
    @Override
    public int updateEmWarehouse(EmWarehouse emWarehouse)
    {
        emWarehouse.setUpdateTime(DateUtils.getNowDate());
        return emWarehouseMapper.updateEmWarehouse(emWarehouse);
    }

    /**
     * 批量删除应急物资仓库基础信息
     * 
     * @param ids 需要删除的应急物资仓库基础信息主键
     * @return 结果
     */
    @Override
    public int deleteEmWarehouseByIds(String[] ids)
    {
        return emWarehouseMapper.deleteEmWarehouseByIds(ids);
    }

    /**
     * 删除应急物资仓库基础信息信息
     * 
     * @param id 应急物资仓库基础信息主键
     * @return 结果
     */
    @Override
    public int deleteEmWarehouseById(String id)
    {
        return emWarehouseMapper.deleteEmWarehouseById(id);
    }
}
