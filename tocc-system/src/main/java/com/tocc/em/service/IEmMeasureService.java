package com.tocc.em.service;

import java.util.List;
import com.tocc.em.domain.EmMeasure;

/**
 * 应急响应处置措施Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IEmMeasureService 
{
    /**
     * 查询应急响应处置措施
     * 
     * @param id 应急响应处置措施主键
     * @return 应急响应处置措施
     */
    public EmMeasure selectEmMeasureById(Long id);

    /**
     * 查询应急响应处置措施列表
     * 
     * @param emMeasure 应急响应处置措施
     * @return 应急响应处置措施集合
     */
    public List<EmMeasure> selectEmMeasureList(EmMeasure emMeasure);

    /**
     * 新增应急响应处置措施
     * 
     * @param emMeasure 应急响应处置措施
     * @return 结果
     */
    public int insertEmMeasure(EmMeasure emMeasure);

    /**
     * 修改应急响应处置措施
     * 
     * @param emMeasure 应急响应处置措施
     * @return 结果
     */
    public int updateEmMeasure(EmMeasure emMeasure);

    /**
     * 批量删除应急响应处置措施
     * 
     * @param ids 需要删除的应急响应处置措施主键集合
     * @return 结果
     */
    public int deleteEmMeasureByIds(Long[] ids);

    /**
     * 删除应急响应处置措施信息
     * 
     * @param id 应急响应处置措施主键
     * @return 结果
     */
    public int deleteEmMeasureById(String id);
}
