package com.tocc.em.service;

import java.util.List;
import com.tocc.em.domain.EmPrePlanRecord;

/**
 * 应急预案数据历史Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IEmPrePlanRecordService 
{
    /**
     * 查询应急预案数据历史
     * 
     * @param id 应急预案数据历史主键
     * @return 应急预案数据历史
     */
    public EmPrePlanRecord selectEmPrePlanRecordById(String id);

    /**
     * 查询应急预案数据历史列表
     * 
     * @param emPrePlanRecord 应急预案数据历史
     * @return 应急预案数据历史集合
     */
    public List<EmPrePlanRecord> selectEmPrePlanRecordList(EmPrePlanRecord emPrePlanRecord);

    /**
     * 新增应急预案数据历史
     * 
     * @param emPrePlanRecord 应急预案数据历史
     * @return 结果
     */
    public int insertEmPrePlanRecord(EmPrePlanRecord emPrePlanRecord);

    /**
     * 修改应急预案数据历史
     * 
     * @param emPrePlanRecord 应急预案数据历史
     * @return 结果
     */
    public int updateEmPrePlanRecord(EmPrePlanRecord emPrePlanRecord);

    /**
     * 批量删除应急预案数据历史
     * 
     * @param ids 需要删除的应急预案数据历史主键集合
     * @return 结果
     */
    public int deleteEmPrePlanRecordByIds(String[] ids);

    /**
     * 删除应急预案数据历史信息
     * 
     * @param id 应急预案数据历史主键
     * @return 结果
     */
    public int deleteEmPrePlanRecordById(String id);
}
