package com.tocc.em.service.impl;

import java.beans.Transient;
import java.util.List;
import com.tocc.common.utils.DateUtils;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.em.dto.EmEventLevelDTO;
import com.tocc.em.qo.EmEventLevelQO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmEventLevelMapper;
import com.tocc.em.domain.EmEventLevel;
import com.tocc.em.service.IEmEventLevelService;

/**
 * 事件分级响应条件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class EmEventLevelServiceImpl implements IEmEventLevelService 
{
    @Autowired
    private EmEventLevelMapper emEventLevelMapper;

    /**
     * 查询事件分级响应条件
     * 
     * @param id 事件分级响应条件主键
     * @return 事件分级响应条件
     */
    @Override
    public EmEventLevel selectEmEventLevelById(Long id)
    {
        return emEventLevelMapper.selectEmEventLevelById(id);
    }

    /**
     * 查询事件分级响应条件列表
     * 
     * @param emEventLevelQO 事件分级响应条件
     * @return 事件分级响应条件
     */
    @Override
    public List<EmEventLevel> selectEmEventLevelList(EmEventLevelQO emEventLevelQO)
    {
        EmEventLevel emEventLevel = new EmEventLevel();
        BeanUtils.copyProperties(emEventLevelQO,emEventLevel);
        return emEventLevelMapper.selectEmEventLevelList(emEventLevel);
    }

    /**
     * 新增事件分级响应条件
     * 
     * @param emEventLevel 事件分级响应条件
     * @return 结果
     */
    @Override
    public int insertEmEventLevel(EmEventLevel emEventLevel)
    {
        return emEventLevelMapper.insertEmEventLevel(emEventLevel);
    }

    /**
     * 修改事件分级响应条件
     * 
     * @param emEventLevel 事件分级响应条件
     * @return 结果
     */
    @Override
    public int updateEmEventLevel(EmEventLevel emEventLevel)
    {
        emEventLevel.setUpdateTime(DateUtils.getNowDate());
        return emEventLevelMapper.updateEmEventLevel(emEventLevel);
    }

    /**
     * 批量删除事件分级响应条件
     * 
     * @param ids 需要删除的事件分级响应条件主键
     * @return 结果
     */
    @Override
    public int deleteEmEventLevelByIds(Long[] ids)
    {
        return emEventLevelMapper.deleteEmEventLevelByIds(ids);
    }

    /**
     * 删除事件分级响应条件信息
     * 
     * @param id 事件分级响应条件主键
     * @return 结果
     */
    @Override
    public int deleteEmEventLevelById(String id)
    {
        return emEventLevelMapper.deleteEmEventLevelById(id);
    }

    @Override
    public void insertBatchEmEventLevel(List<EmEventLevel> emEventLevels) {
        String userName = SecurityUtils.getLoginUser().getUser().getUserName();
        for (EmEventLevel emEventLevel : emEventLevels) {
            emEventLevel.setCreator(userName);
            emEventLevel.setUpdater(userName);
            insertEmEventLevel(emEventLevel);
        }
    }
}
