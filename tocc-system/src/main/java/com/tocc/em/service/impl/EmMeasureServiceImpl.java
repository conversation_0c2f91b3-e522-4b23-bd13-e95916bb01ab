package com.tocc.em.service.impl;

import java.util.List;
import com.tocc.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.em.mapper.EmMeasureMapper;
import com.tocc.em.domain.EmMeasure;
import com.tocc.em.service.IEmMeasureService;

/**
 * 应急响应处置措施Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class EmMeasureServiceImpl implements IEmMeasureService 
{
    @Autowired
    private EmMeasureMapper emMeasureMapper;

    /**
     * 查询应急响应处置措施
     * 
     * @param id 应急响应处置措施主键
     * @return 应急响应处置措施
     */
    @Override
    public EmMeasure selectEmMeasureById(Long id)
    {
        return emMeasureMapper.selectEmMeasureById(id);
    }

    /**
     * 查询应急响应处置措施列表
     * 
     * @param emMeasure 应急响应处置措施
     * @return 应急响应处置措施
     */
    @Override
    public List<EmMeasure> selectEmMeasureList(EmMeasure emMeasure)
    {
        return emMeasureMapper.selectEmMeasureList(emMeasure);
    }

    /**
     * 新增应急响应处置措施
     * 
     * @param emMeasure 应急响应处置措施
     * @return 结果
     */
    @Override
    public int insertEmMeasure(EmMeasure emMeasure)
    {
        emMeasure.setCreateTime(DateUtils.getNowDate());
        return emMeasureMapper.insertEmMeasure(emMeasure);
    }

    /**
     * 修改应急响应处置措施
     * 
     * @param emMeasure 应急响应处置措施
     * @return 结果
     */
    @Override
    public int updateEmMeasure(EmMeasure emMeasure)
    {
        emMeasure.setUpdateTime(DateUtils.getNowDate());
        return emMeasureMapper.updateEmMeasure(emMeasure);
    }

    /**
     * 批量删除应急响应处置措施
     * 
     * @param ids 需要删除的应急响应处置措施主键
     * @return 结果
     */
    @Override
    public int deleteEmMeasureByIds(Long[] ids)
    {
        return emMeasureMapper.deleteEmMeasureByIds(ids);
    }

    /**
     * 删除应急响应处置措施信息
     * 
     * @param id 应急响应处置措施主键
     * @return 结果
     */
    @Override
    public int deleteEmMeasureById(String id)
    {
        return emMeasureMapper.deleteEmMeasureById(id);
    }
}
