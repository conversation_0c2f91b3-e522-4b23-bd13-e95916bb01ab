package com.tocc.em.qo;

import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 事件分级响应条件对象 QO
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class EmEventLevelQO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */

    private String id;

    /** 关联的预案ID */
    @Excel(name = "关联的预案ID")
    private String prePlanId;

    /** 版本号 */
    @Excel(name = "版本号")
    private String version;

    /** 事件级别 */
    @Excel(name = "事件级别")
    private Integer eventLevel;

    /** 事件响应内容 */
    @Excel(name = "事件响应条件")
    private String conditions;

    /** 应急处置流程 */
    @Excel(name = "应急处置流程")
    private String processFlow;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updater;

    /** 删除标志(0存在 1删除) */
    @Excel(name = "删除标志(0存在 1删除)")
    private Integer isDeleted;


}
