package com.tocc.em.mapper;

import java.util.List;
import com.tocc.em.domain.EmPrePlanDept;

/**
 * 应急预案组织体系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface EmPrePlanDeptMapper 
{
    /**
     * 查询应急预案组织体系
     * 
     * @param id 应急预案组织体系主键
     * @return 应急预案组织体系
     */
    public EmPrePlanDept selectEmPrePlanDeptById(String id);

    /**
     * 查询应急预案组织体系列表
     * 
     * @param emPrePlanDept 应急预案组织体系
     * @return 应急预案组织体系集合
     */
    public List<EmPrePlanDept> selectEmPrePlanDeptList(EmPrePlanDept emPrePlanDept);

    /**
     * 新增应急预案组织体系
     * 
     * @param emPrePlanDept 应急预案组织体系
     * @return 结果
     */
    public int insertEmPrePlanDept(EmPrePlanDept emPrePlanDept);

    /**
     * 修改应急预案组织体系
     * 
     * @param emPrePlanDept 应急预案组织体系
     * @return 结果
     */
    public int updateEmPrePlanDept(EmPrePlanDept emPrePlanDept);

    /**
     * 删除应急预案组织体系
     * 
     * @param id 应急预案组织体系主键
     * @return 结果
     */
    public int deleteEmPrePlanDeptById(String id);

    /**
     * 批量删除应急预案组织体系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmPrePlanDeptByIds(String[] ids);
}
