package com.tocc.em.mapper;

import java.util.List;
import com.tocc.em.domain.EmPrePlanDeptUser;

/**
 * 预案组织体系人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface EmPrePlanDeptUserMapper 
{
    /**
     * 查询预案组织体系人员
     * 
     * @param id 预案组织体系人员主键
     * @return 预案组织体系人员
     */
    public EmPrePlanDeptUser selectEmPrePlanDeptUserById(String id);

    /**
     * 查询预案组织体系人员列表
     * 
     * @param emPrePlanDeptUser 预案组织体系人员
     * @return 预案组织体系人员集合
     */
    public List<EmPrePlanDeptUser> selectEmPrePlanDeptUserList(EmPrePlanDeptUser emPrePlanDeptUser);

    /**
     * 新增预案组织体系人员
     * 
     * @param emPrePlanDeptUser 预案组织体系人员
     * @return 结果
     */
    public int insertEmPrePlanDeptUser(EmPrePlanDeptUser emPrePlanDeptUser);

    /**
     * 修改预案组织体系人员
     * 
     * @param emPrePlanDeptUser 预案组织体系人员
     * @return 结果
     */
    public int updateEmPrePlanDeptUser(EmPrePlanDeptUser emPrePlanDeptUser);

    /**
     * 删除预案组织体系人员
     * 
     * @param id 预案组织体系人员主键
     * @return 结果
     */
    public int deleteEmPrePlanDeptUserById(String id);

    /**
     * 批量删除预案组织体系人员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmPrePlanDeptUserByIds(String[] ids);
}
