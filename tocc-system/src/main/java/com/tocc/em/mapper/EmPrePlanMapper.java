package com.tocc.em.mapper;

import java.time.LocalDateTime;
import java.util.List;
import com.tocc.em.domain.EmPrePlan;
import com.tocc.em.qo.EmPrePlanQO;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.em.vo.EmPrePlanVersionVO;
import org.apache.ibatis.annotations.Param;

/**
 * 应急预案数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface EmPrePlanMapper 
{
    /**
     * 查询应急预案数据
     * 
     * @param id 应急预案数据主键
     * @return 应急预案数据
     */
    public EmPrePlan selectEmPrePlanById(String id);

    /**
     * 查询应急预案数据列表
     * 
     * @param emPrePlan 应急预案数据
     * @return 应急预案数据集合
     */
    public List<EmPrePlan> selectEmPrePlanList(EmPrePlanQO emPrePlan);

    /**
     * 新增应急预案数据
     * 
     * @param emPrePlan 应急预案数据
     * @return 结果
     */
    public int insertEmPrePlan(EmPrePlan emPrePlan);

    /**
     * 修改应急预案数据
     * 
     * @param emPrePlan 应急预案数据
     * @return 结果
     */
    public int updateEmPrePlan(EmPrePlan emPrePlan);

    /**
     * 删除应急预案数据
     * 
     * @param id 应急预案数据主键
     * @return 结果
     */
    public int deleteEmPrePlanById(String id);

    /**
     * 批量删除应急预案数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmPrePlanByIds(String[] ids);

    /**
     * 查询更新超时的应急预案
     *
     * @param timeoutTime 超时时间点
     * @return 超时的应急预案集合
     */
    public List<EmPrePlanVO> selectTimeoutPlans(LocalDateTime timeoutTime);

    /**
     * 查询预案的所有版本（包括当前版本和历史版本）
     * 
     * @param prePlanId 预案ID，如果为null则查询所有预案的版本
     * @return 版本列表，按照版本号降序排列
     */
    public List<EmPrePlanVersionVO> selectEmPrePlanVersionHistory(String prePlanId);

    /**
     * 根据预案名称查询预案的所有版本（包括当前版本和历史版本）
     * 
     * @param planName 预案名称
     * @return 版本列表，按照版本号降序排列
     */
    public List<EmPrePlanVersionVO> selectEmPrePlanVersionHistoryByName(String planName);

    /**
     * 根据预案ID和版本号查询预案详情
     * 
     * @param prePlanId 预案ID
     * @param version 版本号
     * @return 预案详情
     */
    public EmPrePlanVO selectEmPrePlanByIdAndVersion(@Param("prePlanId") String prePlanId, @Param("version") String version);
}
