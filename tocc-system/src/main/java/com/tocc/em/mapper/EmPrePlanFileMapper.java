package com.tocc.em.mapper;

import java.util.List;
import com.tocc.em.domain.EmPrePlanFile;

/**
 * 应急预案附件管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface EmPrePlanFileMapper 
{
    /**
     * 查询应急预案附件管理
     * 
     * @param id 应急预案附件管理主键
     * @return 应急预案附件管理
     */
    public EmPrePlanFile selectEmPrePlanFileById(String id);

    /**
     * 查询应急预案附件管理列表
     * 
     * @param emPrePlanFile 应急预案附件管理
     * @return 应急预案附件管理集合
     */
    public List<EmPrePlanFile> selectEmPrePlanFileList(EmPrePlanFile emPrePlanFile);

    /**
     * 新增应急预案附件管理
     * 
     * @param emPrePlanFile 应急预案附件管理
     * @return 结果
     */
    public int insertEmPrePlanFile(EmPrePlanFile emPrePlanFile);

    /**
     * 修改应急预案附件管理
     * 
     * @param emPrePlanFile 应急预案附件管理
     * @return 结果
     */
    public int updateEmPrePlanFile(EmPrePlanFile emPrePlanFile);

    /**
     * 删除应急预案附件管理
     * 
     * @param id 应急预案附件管理主键
     * @return 结果
     */
    public int deleteEmPrePlanFileById(String id);

    /**
     * 批量删除应急预案附件管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmPrePlanFileByIds(String[] ids);
}
