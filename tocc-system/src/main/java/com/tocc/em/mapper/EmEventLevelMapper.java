package com.tocc.em.mapper;

import java.util.List;
import com.tocc.em.domain.EmEventLevel;
import com.tocc.em.dto.EmEventLevelDTO;
import com.tocc.em.qo.EmEventLevelQO;

/**
 * 事件分级响应条件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface EmEventLevelMapper 
{
    /**
     * 查询事件分级响应条件
     * 
     * @param id 事件分级响应条件主键
     * @return 事件分级响应条件
     */
    public EmEventLevel selectEmEventLevelById(Long id);

    /**
     * 查询事件分级响应条件列表
     * 
     * @param emEventLevelQO 事件分级响应条件
     * @return 事件分级响应条件集合
     */
    public List<EmEventLevel> selectEmEventLevelList(EmEventLevel emEventLevelQO);

    /**
     * 新增事件分级响应条件
     * 
     * @param emEventLevel 事件分级响应条件
     * @return 结果
     */
    public int insertEmEventLevel(EmEventLevel emEventLevel);

    /**
     * 修改事件分级响应条件
     * 
     * @param emEventLevel 事件分级响应条件
     * @return 结果
     */
    public int updateEmEventLevel(EmEventLevel emEventLevel);

    /**
     * 删除事件分级响应条件
     * 
     * @param id 事件分级响应条件主键
     * @return 结果
     */
    public int deleteEmEventLevelById(String id);

    /**
     * 批量删除事件分级响应条件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmEventLevelByIds(Long[] ids);
}
