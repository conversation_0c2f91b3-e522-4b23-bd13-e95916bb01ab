package com.tocc.em.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 应急响应处置措施对象 em_measure
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class EmMeasure extends BaseEntity
{


    /** 雪花ID主键 */

    private String  id;

    /** 版本号 */
    @Excel(name = "版本号")
    private String version;

    /** 关联预案ID */
    @Excel(name = "关联预案ID")
    private String prePlanId;

    /** 事件级别 */
    @Excel(name = "事件级别")
    private Integer eventLevel;

    /** 触发条件描述 */
    @Excel(name = "触发条件描述")
    private String triggerCondition;

    /** 处置措施详情 */
    @Excel(name = "处置措施详情")
    private String measureContent;

    /** 是否派遣工作组(0否1是) */
    @Excel(name = "是否派遣工作组(0否1是)")
    private Integer isDispatchTeam;

    /** 需上报上级(Ⅱ级以上1是) */
    @Excel(name = "需上报上级(Ⅱ级以上1是)")
    private Integer needReport;

    /** 需调度资源类型数组 */
    @Excel(name = "需调度资源类型数组")
    private String resourceTypes;

    /** 专项作业JSON数组 */
    @Excel(name = "专项作业JSON数组")
    private String specialOperations;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updater;

    /** 删除标志(0存在1删除) */
    @Excel(name = "删除标志(0存在1删除)")
    private Integer delFlag;


}
