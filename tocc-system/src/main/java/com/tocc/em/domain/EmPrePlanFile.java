package com.tocc.em.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 应急预案附件管理对象 em_pre_plan_file
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class EmPrePlanFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */

    private String id;

    /** 版本号 */
    @Excel(name = "版本号")
    private String version;

    /** 关联业务ID */
    @Excel(name = "关联业务ID")
    private String bizId;

    /** 附件名称(含扩展名) */
    @Excel(name = "附件名称(含扩展名)")
    private String fileName;

    /** 附件类型,字典：file_type */
    @Excel(name = "附件类型,字典：file_type")
    private String fileType;

    /** 附件描述 */
    @Excel(name = "附件描述")
    private String fileDesc;

    /** 创建人 */
    @Excel(name = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    private String updater;

    /** 删除标志(0存在/1删除) */
    private Integer delFlag;

    /** 文件URL */
    @Excel(name = "文件URL")
    private String url;

    /** 文件大小 */
    @Excel(name = "文件大小")
    private Long fileSize;

    /** 文件原始名称 */
    @Excel(name = "文件原始名称")
    private String originalFileName;

    /** 文件新名字 */
    @Excel(name = "文件新名字")
    private String newFileName;


}
