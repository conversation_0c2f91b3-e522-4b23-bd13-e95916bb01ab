package com.tocc.em.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;

/**
 * 应急物资仓库基础信息对象 em_warehouse
 * 
 * <AUTHOR>
 * @date 2025-06-02
 */
@Data
public class EmWarehouse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /** 仓库名称 */
    @Excel(name = "仓库名称")
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /** 仓库类型 */
    @Excel(name = "仓库类型")
    @ApiModelProperty(value = "仓库类型")
    private String warehouseType;

    /** 所属单位code */
    @Excel(name = "所属单位code")
    @ApiModelProperty(value = "所属单位code")
    private String belongOrgCode;

    /** 所属单位名称 */
    @Excel(name = "所属单位名称")
    @ApiModelProperty(value = "所属单位名称")
    private String belongOrgName;

    /** 详细地址 */
    @Excel(name = "详细地址")
    @ApiModelProperty(value = "详细地址")
    private String address;

    /** 负责人姓名 */
    @Excel(name = "负责人姓名")
    @ApiModelProperty(value = "负责人姓名")
    private String principal;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 路段编号 */
    @Excel(name = "路段编号")
    @ApiModelProperty(value = "路段编号")
    private String roadCode;

    /** 桩号 */
    @Excel(name = "桩号")
    @ApiModelProperty(value = "桩号")
    private String stake;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private String latitude;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private String longitude;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updater;

    /** 删除标志(0正常/1删除) */
    @ApiModelProperty(value = "删除标志")
    private Integer delFlag;

    /** 距离 */
    @TableField(exist = false)
    private Double distance;


}
