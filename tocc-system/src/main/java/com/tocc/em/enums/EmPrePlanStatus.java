package com.tocc.em.enums;

/**
 * 预案状态
 */
public enum EmPrePlanStatus {

    DRAFT(0, "草稿"),
    SUBMITTED(1, "已提交");
    /**
     * 值
     */
    private final Integer value;

    /**
     * 名称
     */
    private final String text;


    EmPrePlanStatus(Integer value, String text) {
        this.value = value;
        this.text = text;
    }


    public Integer getValue() {
        return value;
    }

    public String getText() {
        return text;
    }
}
