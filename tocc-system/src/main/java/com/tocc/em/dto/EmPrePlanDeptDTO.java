package com.tocc.em.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.tocc.common.annotation.Excel;
import com.tocc.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 应急预案组织体系DTO
 *
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class EmPrePlanDeptDTO
{

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private String id;

    @ApiModelProperty(value = "显示排序")
    private Integer orderNum;

    /** 上级机构ID */
    @Excel(name = "上级机构ID")
    @ApiModelProperty(value = "上级机构ID")
    private String parentId;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private String version;

    /** 关联预案ID */
    @Excel(name = "关联预案ID")
    @ApiModelProperty(value = "关联预案ID")
    private String prePlanId;

    /** 机构名称 */
    @Excel(name = "机构名称")
    @ApiModelProperty(value = "机构名称")
    private String deptName;

    /** 机构级别,0顶级机构，1下级机构 */
    @Excel(name = "机构级别,0顶级机构，1下级机构")
    @ApiModelProperty(value = "机构级别,0顶级机构，1下级机构")
    private Integer deptLevel;

    /** 主要职责 */
    @Excel(name = "主要职责")
    @ApiModelProperty(value = "主要职责")
    private String deptJob;

    /** 关联事件级别 */
    @Excel(name = "关联事件级别")
    @ApiModelProperty(value = "关联事件级别")
    private String eventLevel;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String creator;

    /** 更新人 */
    @Excel(name = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updater;

    /** 删除标志(0存在/1删除) */
    @ApiModelProperty(value = "删除标志(0存在/1删除)")
    private Integer delFlag;


    /**
     * 下级机构
     */
    @JsonProperty("children")
    @ApiModelProperty(value = "下级机构")
    List<EmPrePlanDeptDTO> children;
    /**
     * 组织体系人员
      */
    @JsonProperty("emPrePlanDeptUserDTOList")
    @ApiModelProperty(value = "组织体系人员")
    List<EmPrePlanDeptUserDTO> emPrePlanDeptUserDTOList;

    @ApiModelProperty(value = "负责人")
    private String leader;

    @ApiModelProperty(value = "负责人的协助人，例如副组长，副指挥")
    private String leaderAss;

    @ApiModelProperty(value = "成员")
    private String member;


    @ApiModelProperty(value = "专家")
    private String pro;


}
