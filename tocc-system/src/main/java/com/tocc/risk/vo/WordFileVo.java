package com.tocc.risk.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 风险隐患情况分析报告导出类
 */
@Data
public class WordFileVo {

    // 截至当前时间  中文  年月日
    private String nowDate;
    // 风险总量
    public BigDecimal overalls;
    // 重大隐患数
    public BigDecimal zd;
    // 重大隐患占比
    public BigDecimal zdzb;
    // 一般隐患数
    public BigDecimal yb;
    // 一般隐患占比
    public BigDecimal ybzb;
    // 已完成整改
    public BigDecimal isZg;
    // 已完成整占比
    public BigDecimal isZgzb;
    // 未完成整改数量
    public BigDecimal noZg;
    // 重大未整改数量
    public BigDecimal zdNoZg;
    // 一般未整改数量
    public BigDecimal ybNoZg;


    // 城市
    private String city;
    // 城市总隐患数量
    public BigDecimal cityAlls;
    // 城市重大隐患数量
    public BigDecimal cityZd;
    // 城市重大隐患占比
    public BigDecimal cityZb;
    // 其他城市合计数量
    public BigDecimal otherCityAlls;
    // 其他城市重大隐患占比
    public BigDecimal otherCityZb;



    // 领域
    public String areas;
    // 领域隐患数量
    public BigDecimal areasAlls;
    // 领域隐患占比
    public BigDecimal areasZb;
    // 责任单位
    public String units;
    // 自然灾害风险
    public BigDecimal zrzhfx;
    // 人为风险
    public BigDecimal rwfx;
    // 其他城市合计数量
    public BigDecimal otherAreasAlls;
    // 其他城市重大隐患数量
    public BigDecimal otherAreasZd;


    // 整改总量
    public BigDecimal speedAlls;
    // 累计完成整改
    public BigDecimal succ;
    // 完成重大隐患整改
    public BigDecimal huge;
    // 完成一般隐患整改
    public BigDecimal pub;
    // 完成整改占比
    public BigDecimal succZb;
    // 未完成整改总数
    public BigDecimal pau;
    // 未完成重大隐患整改
    public BigDecimal pauh;
    // 未完成整改重大隐患占比
    public BigDecimal pauhZb;
    // 未完成整改一般隐患
    public BigDecimal paup;
    // 未完成整改一般隐患占比
    public BigDecimal paupZb;


    // 未完成隐患整改城市
    private String recCity;
    // 城市未完成隐患整改数量
    public BigDecimal notRec;
}
