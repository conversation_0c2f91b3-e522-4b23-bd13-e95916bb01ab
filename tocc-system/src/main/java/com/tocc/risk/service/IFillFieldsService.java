package com.tocc.risk.service;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tocc.risk.domain.FillFields;
import org.apache.ibatis.annotations.Mapper;

/**
 * 填报项字段Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IFillFieldsService extends IService<FillFields>
{
    /**
     * 查询填报项字段
     * 
     * @param id 填报项字段主键
     * @return 填报项字段
     */
    public FillFields selectFillFieldsById(Long id);

    /**
     * 查询填报项字段列表
     * 
     * @param fillFields 填报项字段
     * @return 填报项字段集合
     */
    public List<FillFields> selectFillFieldsList(FillFields fillFields);

    /**
     * 根据下发任务获取填报项数据
     *
     * @param id 下发任务ID
     * @return 填报项字段集合
     */
    public List<FillFields> getFieldsByIssuedId(Long id);

    /**
     * 新增填报项字段
     * 
     * @param fillFields 填报项字段
     * @return 结果
     */
    public int insertFillFields(FillFields fillFields);

    /**
     * 修改填报项字段
     * 
     * @param fillFields 填报项字段
     * @return 结果
     */
    public int updateFillFields(FillFields fillFields);

    /**
     * 批量删除填报项字段
     * 
     * @param ids 需要删除的填报项字段主键集合
     * @return 结果
     */
    public int deleteFillFieldsByIds(Long[] ids);

    /**
     * 删除填报项字段信息
     * 
     * @param id 填报项字段主键
     * @return 结果
     */
    public int deleteFillFieldsById(Long id);
}
