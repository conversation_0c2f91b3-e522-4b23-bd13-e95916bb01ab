package com.tocc.risk.service;

import java.util.List;
import com.tocc.risk.domain.Pitfalls;

import javax.servlet.http.HttpServletResponse;

/**
 * 隐患列Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IPitfallsService 
{
    /**
     * 查询隐患列
     * 
     * @param id 隐患列主键
     * @return 隐患列
     */
    public Pitfalls selectPitfallsById(Long id);

    /**
     * 查询隐患列列表
     * 
     * @param pitfalls 隐患列
     * @return 隐患列集合
     */
    public List<Pitfalls> selectPitfallsList(Pitfalls pitfalls);

    /**
     * 新增隐患列
     * 
     * @param pitfalls 隐患列
     * @return 结果
     */
    public int insertPitfalls(Pitfalls pitfalls);

    /**
     * 修改隐患列
     * 
     * @param pitfalls 隐患列
     * @return 结果
     */
    public int updatePitfalls(Pitfalls pitfalls);

    /**
     * 批量删除隐患列
     * 
     * @param ids 需要删除的隐患列主键集合
     * @return 结果
     */
    public int deletePitfallsByIds(Long[] ids);

    /**
     * 删除隐患列信息
     * 
     * @param id 隐患列主键
     * @return 结果
     */
    public int deletePitfallsById(Long id);

    /**
     * 导出隐患分析报告
     * @param response
     * @param pitfalls
     */
    void exportAnalysis(HttpServletResponse response, Pitfalls pitfalls);
}
