package com.tocc.risk.service;

import java.util.List;
import java.util.Map;

import com.tocc.risk.domain.Projects;

/**
 * 项目Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IRiskProjectsService 
{
    /**
     * 查询项目
     * 
     * @param id 项目主键
     * @return 项目
     */
    public Projects selectRiskProjectsById(Long id);

    /**
     * 查询项目列表
     * 
     * @param projects 项目
     * @return 项目集合
     */
    public List<Projects> selectRiskProjectsList(Projects projects);

    /**
     * 获取项目责任人下拉框内容
     *
     * @param projects 项目
     * @return 项目集合
     */
    public List<Map> getProjectCharger(Projects projects);

    /**
     * 新增项目
     * 
     * @param projects 项目
     * @return 结果
     */
    public int insertRiskProjects(Projects projects);

    /**
     * 修改项目
     * 
     * @param projects 项目
     * @return 结果
     */
    public int updateRiskProjects(Projects projects);

    /**
     * 批量删除项目
     * 
     * @param ids 需要删除的项目主键集合
     * @return 结果
     */
    public int deleteRiskProjectsByIds(Long[] ids);

    /**
     * 删除项目信息
     * 
     * @param id 项目主键
     * @return 结果
     */
    public int deleteRiskProjectsById(Long id);
}
