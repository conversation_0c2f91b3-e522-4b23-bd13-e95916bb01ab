package com.tocc.risk.service;

import java.util.List;
import java.util.Map;

import com.tocc.risk.domain.Approve;
import com.tocc.risk.domain.Pitfalls;

/**
 * 检查审批Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IApproveService 
{
    /**
     * 查询检查审批
     * 
     * @param id 检查审批主键
     * @return 检查审批
     */
    public Approve selectApproveById(Long id);

    /**
     * 查询检查审批列表
     * 
     * @param pitfalls
     * @return 检查审批集合
     */
    public List<Map> selectApproveList(Pitfalls pitfalls);

    /**
     * 新增检查审批
     * 
     * @param approve 检查审批
     * @return 结果
     */
    public int insertApprove(Approve approve);

    /**
     * 修改检查审批
     * 
     * @param approve 检查审批
     * @return 结果
     */
    public int updateApprove(Approve approve);

    /**
     * 批量删除检查审批
     * 
     * @param ids 需要删除的检查审批主键集合
     * @return 结果
     */
    public int deleteApproveByIds(Long[] ids);

    /**
     * 删除检查审批信息
     * 
     * @param id 检查审批主键
     * @return 结果
     */
    public int deleteApproveById(Long id);
}
