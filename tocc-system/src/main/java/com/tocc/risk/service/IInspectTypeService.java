package com.tocc.risk.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tocc.risk.domain.InspectType;

/**
 * 检查类别Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface IInspectTypeService extends IService<InspectType>
{
    /**
     * 查询检查类别
     * 
     * @param id 检查类别主键
     * @return 检查类别
     */
    public InspectType selectInspectTypeById(Long id);

    /**
     * 查询检查类别列表
     * 
     * @param inspectType 检查类别
     * @return 检查类别集合
     */
    public List<InspectType> selectInspectTypeList(InspectType inspectType);

    /**
     * 查询检查类别树
     *
     * @return 检查类别集合
     */
    public List<InspectType> treeList();

    /**
     * 新增检查类别
     * 
     * @param inspectType 检查类别
     * @return 结果
     */
    public int insertInspectType(InspectType inspectType);

    /**
     * 修改检查类别
     * 
     * @param inspectType 检查类别
     * @return 结果
     */
    public int updateInspectType(InspectType inspectType);

    /**
     * 批量删除检查类别
     * 
     * @param ids 需要删除的检查类别主键集合
     * @return 结果
     */
    public int deleteInspectTypeByIds(Long[] ids);

    /**
     * 删除检查类别信息
     * 
     * @param id 检查类别主键
     * @return 结果
     */
    public int deleteInspectTypeById(Long id);
}
