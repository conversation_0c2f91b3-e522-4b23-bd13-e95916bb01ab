package com.tocc.risk.service.impl;

import java.util.List;
import com.tocc.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.ModifyTaskMapper;
import com.tocc.risk.domain.ModifyTask;
import com.tocc.risk.service.IModifyTaskService;

/**
 * 整改任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class ModifyTaskServiceImpl implements IModifyTaskService 
{
    @Autowired
    private ModifyTaskMapper modifyTaskMapper;

    /**
     * 查询整改任务
     * 
     * @param id 整改任务主键
     * @return 整改任务
     */
    @Override
    public ModifyTask selectModifyTaskById(Long id)
    {
        return modifyTaskMapper.selectModifyTaskById(id);
    }

    /**
     * 查询整改任务列表
     * 
     * @param modifyTask 整改任务
     * @return 整改任务
     */
    @Override
    public List<ModifyTask> selectModifyTaskList(ModifyTask modifyTask)
    {
        return modifyTaskMapper.selectModifyTaskList(modifyTask);
    }

    /**
     * 新增整改任务
     * 
     * @param modifyTask 整改任务
     * @return 结果
     */
    @Override
    public int insertModifyTask(ModifyTask modifyTask)
    {
        modifyTask.setDelFlag(0);
        modifyTask.setCreateTime(DateUtils.getNowDate());
        return modifyTaskMapper.insertModifyTask(modifyTask);
    }

    /**
     * 修改整改任务
     * 
     * @param modifyTask 整改任务
     * @return 结果
     */
    @Override
    public int updateModifyTask(ModifyTask modifyTask)
    {
        modifyTask.setUpdateTime(DateUtils.getNowDate());
        return modifyTaskMapper.updateModifyTask(modifyTask);
    }

    /**
     * 批量删除整改任务
     * 
     * @param ids 需要删除的整改任务主键
     * @return 结果
     */
    @Override
    public int deleteModifyTaskByIds(Long[] ids)
    {
        return modifyTaskMapper.deleteModifyTaskByIds(ids);
    }

    /**
     * 删除整改任务信息
     * 
     * @param id 整改任务主键
     * @return 结果
     */
    @Override
    public int deleteModifyTaskById(Long id)
    {
        return modifyTaskMapper.deleteModifyTaskById(id);
    }
}
