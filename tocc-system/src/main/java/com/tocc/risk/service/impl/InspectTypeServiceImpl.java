package com.tocc.risk.service.impl;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tocc.risk.mapper.InspectTypeMapper;
import com.tocc.risk.domain.InspectType;
import com.tocc.risk.service.IInspectTypeService;

/**
 * 检查类别Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class InspectTypeServiceImpl extends ServiceImpl<InspectTypeMapper, InspectType> implements IInspectTypeService
{
    @Autowired
    private InspectTypeMapper inspectTypeMapper;

    /**
     * 查询检查类别
     * 
     * @param id 检查类别主键
     * @return 检查类别
     */
    @Override
    public InspectType selectInspectTypeById(Long id)
    {
        return inspectTypeMapper.selectInspectTypeById(id);
    }

    /**
     * 查询检查类别列表
     * 
     * @param inspectType 检查类别
     * @return 检查类别
     */
    @Override
    public List<InspectType> selectInspectTypeList(InspectType inspectType)
    {
        return inspectTypeMapper.selectInspectTypeList(inspectType);
    }

    @Override
    public List<InspectType> treeList() {

        List<InspectType> list = inspectTypeMapper.selectList(new QueryWrapper<InspectType>().lambda()
                .eq(InspectType::getDelFlag, 0));

        Map<Long, InspectType> nodeMap = new HashMap<>();
        list.forEach(node -> nodeMap.put(node.getId(), node));

        // 3. 构建树
        List<InspectType> tree = new ArrayList<>();
        for (InspectType node : list) {
            Long parentId = node.getParentId();
            if (parentId == null || parentId == 0 || !nodeMap.containsKey(parentId)) {
                tree.add(node);  // 根节点
            } else {
                InspectType parent = nodeMap.get(parentId);
                parent.addChild(node);  // 添加到父节点
            }
        }
        return tree;
    }

    /**
     * 新增检查类别
     * 
     * @param inspectType 检查类别
     * @return 结果
     */
    @Override
    public int insertInspectType(InspectType inspectType)
    {
        inspectType.setCreateTime(DateUtils.getNowDate());
        inspectType.setDelFlag(0);
        return inspectTypeMapper.insertInspectType(inspectType);
    }

    /**
     * 修改检查类别
     * 
     * @param inspectType 检查类别
     * @return 结果
     */
    @Override
    public int updateInspectType(InspectType inspectType)
    {
        return inspectTypeMapper.updateInspectType(inspectType);
    }

    /**
     * 批量删除检查类别
     * 
     * @param ids 需要删除的检查类别主键
     * @return 结果
     */
    @Override
    public int deleteInspectTypeByIds(Long[] ids)
    {
        return inspectTypeMapper.deleteInspectTypeByIds(ids);
    }

    /**
     * 删除检查类别信息
     * 
     * @param id 检查类别主键
     * @return 结果
     */
    @Override
    public int deleteInspectTypeById(Long id)
    {
        return inspectTypeMapper.deleteInspectTypeById(id);
    }
}
