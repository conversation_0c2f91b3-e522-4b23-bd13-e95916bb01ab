package com.tocc.risk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tocc.risk.domain.FillFields;
import com.tocc.risk.domain.Pitfalls;
import com.tocc.risk.vo.MaterialVO;
import com.tocc.risk.vo.RescueTeamVO;
import com.tocc.risk.vo.RiskHome;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
public interface RiskHomeMapper extends BaseMapper<RiskHome>
{
    public List<MaterialVO> getMaterialByWarId(String id);
    public List<MaterialVO> getMaterialByTeamId(String id);

    List<RescueTeamVO> selectRescueTeamList();
}
