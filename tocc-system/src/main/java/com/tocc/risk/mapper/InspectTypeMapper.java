package com.tocc.risk.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tocc.risk.domain.InspectType;
import org.apache.ibatis.annotations.Mapper;

/**
 * 检查类别Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Mapper
public interface InspectTypeMapper extends BaseMapper<InspectType>
{
    /**
     * 查询检查类别
     * 
     * @param id 检查类别主键
     * @return 检查类别
     */
    public InspectType selectInspectTypeById(Long id);

    /**
     * 查询检查类别列表
     * 
     * @param inspectType 检查类别
     * @return 检查类别集合
     */
    public List<InspectType> selectInspectTypeList(InspectType inspectType);

    /**
     * 新增检查类别
     * 
     * @param inspectType 检查类别
     * @return 结果
     */
    public int insertInspectType(InspectType inspectType);

    /**
     * 修改检查类别
     * 
     * @param inspectType 检查类别
     * @return 结果
     */
    public int updateInspectType(InspectType inspectType);

    /**
     * 删除检查类别
     * 
     * @param id 检查类别主键
     * @return 结果
     */
    public int deleteInspectTypeById(Long id);

    /**
     * 批量删除检查类别
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInspectTypeByIds(Long[] ids);
}
