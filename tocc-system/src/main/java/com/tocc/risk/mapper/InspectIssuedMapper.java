package com.tocc.risk.mapper;

import java.util.List;
import com.tocc.risk.domain.InspectIssued;
import com.tocc.risk.domain.InspectTask;

/**
 * 检查下发Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface InspectIssuedMapper 
{
    /**
     * 查询检查下发
     * 
     * @param id 检查下发主键
     * @return 检查下发
     */
    public InspectIssued selectInspectIssuedById(Long id);

    /**
     * 查询检查下发列表
     * 
     * @param inspectIssued 检查下发
     * @return 检查下发集合
     */
    public List<InspectIssued> selectInspectIssuedList(InspectIssued inspectIssued);

    public List<InspectTask> getProgress(Long id);
    /**
     * 新增检查下发
     * 
     * @param inspectIssued 检查下发
     * @return 结果
     */
    public int insertInspectIssued(InspectIssued inspectIssued);

    /**
     * 修改检查下发
     * 
     * @param inspectIssued 检查下发
     * @return 结果
     */
    public int updateInspectIssued(InspectIssued inspectIssued);

    /**
     * 删除检查下发
     * 
     * @param id 检查下发主键
     * @return 结果
     */
    public int deleteInspectIssuedById(Long id);

    /**
     * 批量删除检查下发
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInspectIssuedByIds(Long[] ids);
}
