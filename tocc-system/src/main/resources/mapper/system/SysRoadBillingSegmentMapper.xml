<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.system.mapper.SysRoadBillingSegmentMapper">
    
    <resultMap type="SysRoadBillingSegment" id="SysRoadBillingSegmentResult">
        <result property="id"    column="id"    />
        <result property="roadMarkerId"    column="road_marker_id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="geom"    column="geom"    />
        <result property="lengthRoad"    column="length_road"    />
        <result property="km1"    column="km1"    />
        <result property="km2"    column="km2"    />
        <result property="roadMin"    column="road_min"    />
        <result property="roadMax"    column="road_max"    />
        <result property="company"    column="company"    />
        <result property="carWayNum"    column="car_way_num"    />
        <result property="planSpeed"    column="plan_speed"    />
        <result property="alias"    column="alias"    />
        <result property="direction"    column="direction"    />
        <result property="milePost"    column="mile_post"    />
        <result property="companyAlias"    column="company_alias"    />
        <result property="highwayCode"    column="highway_code"    />
        <result property="minPcu"    column="min_pcu"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysRoadBillingSegmentVo">
        select id, road_marker_id, code, name, geom, length_road, km1, km2, road_min, road_max, company, car_way_num, plan_speed, alias, direction, mile_post, company_alias, highway_code, min_pcu, status, create_by, create_time, update_by, update_time, remark from sys_road_billing_segment
    </sql>

    <select id="selectSysRoadBillingSegmentList" parameterType="SysRoadBillingSegment" resultMap="SysRoadBillingSegmentResult">
        <include refid="selectSysRoadBillingSegmentVo"/>
        <where>  
            <if test="roadMarkerId != null "> and road_marker_id = #{roadMarkerId}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="lengthRoad != null "> and length_road = #{lengthRoad}</if>
            <if test="km1 != null  and km1 != ''"> and km1 = #{km1}</if>
            <if test="km2 != null  and km2 != ''"> and km2 = #{km2}</if>
            <if test="roadMin != null  and roadMin != ''"> and road_min like concat('%', #{roadMin}, '%')</if>
            <if test="roadMax != null  and roadMax != ''"> and road_max like concat('%', #{roadMax}, '%')</if>
            <if test="company != null  and company != ''"> and company like concat('%', #{company}, '%')</if>
            <if test="carWayNum != null  and carWayNum != ''"> and car_way_num = #{carWayNum}</if>
            <if test="planSpeed != null "> and plan_speed = #{planSpeed}</if>
            <if test="alias != null  and alias != ''"> and alias like concat('%', #{alias}, '%')</if>
            <if test="direction != null  and direction != ''"> and direction = #{direction}</if>
            <if test="milePost != null  and milePost != ''"> and mile_post = #{milePost}</if>
            <if test="companyAlias != null  and companyAlias != ''"> and company_alias like concat('%', #{companyAlias}, '%')</if>
            <if test="highwayCode != null  and highwayCode != ''"> and highway_code = #{highwayCode}</if>
            <if test="minPcu != null "> and min_pcu = #{minPcu}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by km1, direction
    </select>
    
    <select id="selectSysRoadBillingSegmentById" parameterType="Long" resultMap="SysRoadBillingSegmentResult">
        <include refid="selectSysRoadBillingSegmentVo"/>
        where id = #{id}
    </select>

    <select id="selectGeometryByCodeAndSection" resultType="map">
        SELECT 
            rbs.id,
            rbs.name as segment_name,
            rbs.geom,
            rbs.direction,
            rbs.length_road,
            rbs.km1,
            rbs.km2,
            rbs.company,
            rbs.plan_speed,
            rbs.car_way_num,
            rm.code as road_code,
            rm.name as section_name
        FROM sys_road_marker rm
        LEFT JOIN sys_road_billing_segment rbs ON rm.id = rbs.road_marker_id
        WHERE rm.code = #{code} 
          AND rm.name = #{sectionName}
          AND rbs.status = '0'
          AND rbs.geom IS NOT NULL
        ORDER BY rbs.km1, rbs.direction
    </select>

    <select id="selectGeometryByRoadMarkerId" resultType="map">
        SELECT 
            rbs.id,
            rbs.name as segment_name,
            rbs.geom,
            rbs.direction,
            rbs.length_road,
            rbs.km1,
            rbs.km2,
            rbs.company,
            rbs.plan_speed,
            rbs.car_way_num,
            rm.code as road_code,
            rm.name as section_name
        FROM sys_road_marker rm
        LEFT JOIN sys_road_billing_segment rbs ON rm.id = rbs.road_marker_id
        WHERE rm.id = #{roadMarkerId}
          AND rbs.status = '0'
          AND rbs.geom IS NOT NULL
        ORDER BY rbs.km1, rbs.direction
    </select>

    <select id="selectByRoadMarkerId" parameterType="Long" resultMap="SysRoadBillingSegmentResult">
        <include refid="selectSysRoadBillingSegmentVo"/>
        where road_marker_id = #{roadMarkerId} and status = '0'
        order by km1, direction
    </select>
        
    <insert id="insertSysRoadBillingSegment" parameterType="SysRoadBillingSegment" useGeneratedKeys="true" keyProperty="id">
        insert into sys_road_billing_segment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roadMarkerId != null">road_marker_id,</if>
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="geom != null">geom,</if>
            <if test="lengthRoad != null">length_road,</if>
            <if test="km1 != null">km1,</if>
            <if test="km2 != null">km2,</if>
            <if test="roadMin != null">road_min,</if>
            <if test="roadMax != null">road_max,</if>
            <if test="company != null">company,</if>
            <if test="carWayNum != null">car_way_num,</if>
            <if test="planSpeed != null">plan_speed,</if>
            <if test="alias != null">alias,</if>
            <if test="direction != null">direction,</if>
            <if test="milePost != null">mile_post,</if>
            <if test="companyAlias != null">company_alias,</if>
            <if test="highwayCode != null">highway_code,</if>
            <if test="minPcu != null">min_pcu,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roadMarkerId != null">#{roadMarkerId},</if>
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="geom != null">#{geom},</if>
            <if test="lengthRoad != null">#{lengthRoad},</if>
            <if test="km1 != null">#{km1},</if>
            <if test="km2 != null">#{km2},</if>
            <if test="roadMin != null">#{roadMin},</if>
            <if test="roadMax != null">#{roadMax},</if>
            <if test="company != null">#{company},</if>
            <if test="carWayNum != null">#{carWayNum},</if>
            <if test="planSpeed != null">#{planSpeed},</if>
            <if test="alias != null">#{alias},</if>
            <if test="direction != null">#{direction},</if>
            <if test="milePost != null">#{milePost},</if>
            <if test="companyAlias != null">#{companyAlias},</if>
            <if test="highwayCode != null">#{highwayCode},</if>
            <if test="minPcu != null">#{minPcu},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysRoadBillingSegment" parameterType="SysRoadBillingSegment">
        update sys_road_billing_segment
        <trim prefix="SET" suffixOverrides=",">
            <if test="roadMarkerId != null">road_marker_id = #{roadMarkerId},</if>
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="geom != null">geom = #{geom},</if>
            <if test="lengthRoad != null">length_road = #{lengthRoad},</if>
            <if test="km1 != null">km1 = #{km1},</if>
            <if test="km2 != null">km2 = #{km2},</if>
            <if test="roadMin != null">road_min = #{roadMin},</if>
            <if test="roadMax != null">road_max = #{roadMax},</if>
            <if test="company != null">company = #{company},</if>
            <if test="carWayNum != null">car_way_num = #{carWayNum},</if>
            <if test="planSpeed != null">plan_speed = #{planSpeed},</if>
            <if test="alias != null">alias = #{alias},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="milePost != null">mile_post = #{milePost},</if>
            <if test="companyAlias != null">company_alias = #{companyAlias},</if>
            <if test="highwayCode != null">highway_code = #{highwayCode},</if>
            <if test="minPcu != null">min_pcu = #{minPcu},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysRoadBillingSegmentById" parameterType="Long">
        delete from sys_road_billing_segment where id = #{id}
    </delete>

    <delete id="deleteSysRoadBillingSegmentByIds" parameterType="String">
        delete from sys_road_billing_segment where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
