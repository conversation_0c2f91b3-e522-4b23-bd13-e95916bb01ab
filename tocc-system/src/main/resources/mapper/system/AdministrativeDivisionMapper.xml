<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.system.mapper.AdministrativeDivisionMapper">

    <resultMap id="AdministrativeDivisionResult" type="com.tocc.common.domain.vo.AdministrativeDivisionVO">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="deep" column="deep"/>
        <result property="name" column="name"/>
        <result property="pinyinPrefix" column="pinyin_prefix"/>
        <result property="extId" column="ext_id"/>
        <result property="extName" column="ext_name"/>
    </resultMap>

    <sql id="selectDivisionVo">
        SELECT id, pid, deep, name, pinyin_prefix, ext_id, ext_name
        FROM administrative_divisions
    </sql>

    <!-- 查询所有行政区划 -->
    <select id="selectAllDivisions" resultMap="AdministrativeDivisionResult">
        <include refid="selectDivisionVo"/>
        ORDER BY deep ASC, id ASC
    </select>

    <!-- 根据父级ID查询子级行政区划 -->
    <select id="selectByPid" parameterType="String" resultMap="AdministrativeDivisionResult">
        <include refid="selectDivisionVo"/>
        WHERE pid = #{pid}
        ORDER BY id ASC
    </select>

    <!-- 根据层级查询行政区划 -->
    <select id="selectByDeep" parameterType="Integer" resultMap="AdministrativeDivisionResult">
        <include refid="selectDivisionVo"/>
        WHERE deep = #{deep}
        ORDER BY id ASC
    </select>

    <!-- 根据ID查询行政区划 -->
    <select id="selectById" parameterType="String" resultMap="AdministrativeDivisionResult">
        <include refid="selectDivisionVo"/>
        WHERE id = #{id}
    </select>

    <!-- 根据名称模糊查询行政区划 -->
    <select id="selectByName" parameterType="String" resultMap="AdministrativeDivisionResult">
        <include refid="selectDivisionVo"/>
        WHERE name LIKE CONCAT('%', #{name}, '%')
           OR ext_name LIKE CONCAT('%', #{name}, '%')
        ORDER BY deep ASC, id ASC
    </select>

    <!-- 查询指定层级范围的行政区划 -->
    <select id="selectByDeepRange" resultMap="AdministrativeDivisionResult">
        <include refid="selectDivisionVo"/>
        WHERE deep BETWEEN #{minDeep} AND #{maxDeep}
        ORDER BY deep ASC, id ASC
    </select>

</mapper>
