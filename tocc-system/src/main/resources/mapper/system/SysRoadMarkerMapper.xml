<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.system.mapper.SysRoadMarkerMapper">
    
    <resultMap type="SysRoadMarker" id="SysRoadMarkerResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="allName"    column="all_name"    />
        <result property="name"    column="name"    />
        <result property="roadType"    column="road_type"    />
    </resultMap>

    <sql id="selectSysRoadMarkerVo">
        select id, code, all_name, name, road_type from sys_road_marker
    </sql>

    <select id="selectSysRoadMarkerList" parameterType="SysRoadMarker" resultMap="SysRoadMarkerResult">
        <include refid="selectSysRoadMarkerVo"/>
        <where>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="allName != null  and allName != ''"> and all_name like concat('%', #{allName}, '%')</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="roadType != null  and roadType != ''"> and road_type = #{roadType}</if>
        </where>
    </select>
    
    <select id="selectSysRoadMarkerById" parameterType="Long" resultMap="SysRoadMarkerResult">
        <include refid="selectSysRoadMarkerVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysRoadMarker" parameterType="SysRoadMarker" useGeneratedKeys="true" keyProperty="id">
        insert into sys_road_marker
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="allName != null">all_name,</if>
            <if test="name != null">name,</if>
            <if test="roadType != null">road_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="allName != null">#{allName},</if>
            <if test="name != null">#{name},</if>
            <if test="roadType != null">#{roadType},</if>
         </trim>
    </insert>

    <update id="updateSysRoadMarker" parameterType="SysRoadMarker">
        update sys_road_marker
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="allName != null">all_name = #{allName},</if>
            <if test="name != null">name = #{name},</if>
            <if test="roadType != null">road_type = #{roadType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysRoadMarkerById" parameterType="Long">
        delete from sys_road_marker where id = #{id}
    </delete>

    <delete id="deleteSysRoadMarkerByIds" parameterType="String">
        delete from sys_road_marker where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByCodeAndName" resultMap="SysRoadMarkerResult">
        <include refid="selectSysRoadMarkerVo"/>
        where code = #{code} and name = #{name}
    </select>
</mapper>