<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.InspectTypeMapper">
    
    <resultMap type="InspectType" id="InspectTypeResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="level"    column="level"    />
        <result property="parentId"    column="parent_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updataTime"    column="updata_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectInspectTypeVo">
        select id, name, level, parent_id, create_time, updata_time, del_flag from risk_inspect_type
    </sql>

    <select id="selectInspectTypeList" parameterType="InspectType" resultMap="InspectTypeResult">
        <include refid="selectInspectTypeVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="updataTime != null "> and updata_time = #{updataTime}</if>
        </where>
    </select>
    
    <select id="selectInspectTypeById" parameterType="Long" resultMap="InspectTypeResult">
        <include refid="selectInspectTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertInspectType" parameterType="InspectType">
        insert into risk_inspect_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="level != null">level,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updataTime != null">updata_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="level != null">#{level},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updataTime != null">#{updataTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateInspectType" parameterType="InspectType">
        update risk_inspect_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="level != null">level = #{level},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updataTime != null">updata_time = #{updataTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInspectTypeById" parameterType="Long">
        delete from risk_inspect_type where id = #{id}
    </delete>

    <delete id="deleteInspectTypeByIds" parameterType="String">
        delete from risk_inspect_type where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>