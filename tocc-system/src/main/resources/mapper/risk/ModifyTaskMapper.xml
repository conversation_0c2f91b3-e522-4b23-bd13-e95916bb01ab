<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.ModifyTaskMapper">
    
    <resultMap type="ModifyTask" id="ModifyTaskResult">
        <result property="id"    column="id"    />
        <result property="taskNo"    column="task_no"    />
        <result property="pitfallsId"    column="pitfalls_id"    />
        <result property="dutyUnit"    column="duty_unit"    />
        <result property="dutyBy"    column="duty_by"    />
        <result property="entTime"    column="ent_time"    />
        <result property="status"    column="status"    />
        <result property="remarks"    column="remarks"    />
        <result property="fileUrls"    column="file_urls"    />
        <result property="completeTime"    column="complete_time"    />
        <result property="measures"    column="measures"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createById"    column="create_by_id"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectModifyTaskVo">
        select mt.*, rp.name from risk_modify_task mt
    </sql>

    <select id="selectModifyTaskList" resultType="ModifyTask" >
        <include refid="selectModifyTaskVo"/>
        left join risk_pitfalls rp on mt.pitfalls_id = rp.id
        <where>  
            <if test="taskNo != null  and taskNo != ''"> and task_no = #{taskNo}</if>
            <if test="pitfallsId != null "> and pitfalls_id = #{pitfallsId}</if>
            <if test="dutyUnit != null  and dutyUnit != ''"> and duty_unit = #{dutyUnit}</if>
            <if test="dutyBy != null  and dutyBy != ''"> and duty_by = #{dutyBy}</if>
            <if test="entTime != null "> and ent_time = #{entTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="fileUrls != null  and fileUrls != ''"> and file_urls = #{fileUrls}</if>
            <if test="createById != null "> and create_by_id = #{createById}</if>
        </where>
    </select>
    
    <select id="selectModifyTaskById" parameterType="Long" resultMap="ModifyTaskResult">
        <include refid="selectModifyTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertModifyTask" parameterType="ModifyTask">
        insert into risk_modify_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="taskNo != null">task_no,</if>
            <if test="pitfallsId != null">pitfalls_id,</if>
            <if test="dutyUnit != null">duty_unit,</if>
            <if test="dutyBy != null">duty_by,</if>
            <if test="entTime != null">ent_time,</if>
            <if test="status != null">status,</if>
            <if test="remarks != null">remarks,</if>
            <if test="fileUrls != null">file_urls,</if>
            <if test="completeTime != null">complete_time,</if>
            <if test="measures != null">measures,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="taskNo != null">#{taskNo},</if>
            <if test="pitfallsId != null">#{pitfallsId},</if>
            <if test="dutyUnit != null">#{dutyUnit},</if>
            <if test="dutyBy != null">#{dutyBy},</if>
            <if test="entTime != null">#{entTime},</if>
            <if test="status != null">#{status},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="fileUrls != null">#{fileUrls},</if>
            <if test="completeTime != null">#{completeTime},</if>
            <if test="measures != null">#{measures},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createById != null">#{createById},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateModifyTask" parameterType="ModifyTask">
        update risk_modify_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskNo != null">task_no = #{taskNo},</if>
            <if test="pitfallsId != null">pitfalls_id = #{pitfallsId},</if>
            <if test="dutyUnit != null">duty_unit = #{dutyUnit},</if>
            <if test="dutyBy != null">duty_by = #{dutyBy},</if>
            <if test="entTime != null">ent_time = #{entTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="fileUrls != null">file_urls = #{fileUrls},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
            <if test="measures != null">measures = #{measures},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteModifyTaskById" parameterType="Long">
        delete from risk_modify_task where id = #{id}
    </delete>

    <delete id="deleteModifyTaskByIds" parameterType="String">
        delete from risk_modify_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByPitfallsId" resultMap="ModifyTaskResult">
        select * from risk_modify_task where pitfalls_id = #{id} and del_flag = 0
    </select>
</mapper>