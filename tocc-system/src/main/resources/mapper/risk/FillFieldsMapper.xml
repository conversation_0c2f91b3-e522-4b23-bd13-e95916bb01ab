<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.risk.mapper.FillFieldsMapper">
    
    <resultMap type="FillFields" id="FillFieldsResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="contents"    column="contents"    />
        <result property="isMust"    column="is_must"    />
        <result property="remarks"    column="remarks"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectFillFieldsVo">
        select id, name, type, contents, is_must, remarks, create_time, update_time, del_flag from risk_fill_fields
    </sql>

    <select id="selectFillFieldsList" parameterType="FillFields" resultMap="FillFieldsResult">
        <include refid="selectFillFieldsVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="contents != null  and contents != ''"> and contents = #{contents}</if>
            <if test="isMust != null "> and is_must = #{isMust}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
        </where>
    </select>

    <select id="getListByIds" parameterType="FillFields" resultMap="FillFieldsResult">
        <include refid="selectFillFieldsVo"/>
        <where>
            id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectFillFieldsById" parameterType="Long" resultMap="FillFieldsResult">
        <include refid="selectFillFieldsVo"/>
        where id = #{id}
    </select>

    <insert id="insertFillFields" parameterType="FillFields">
        insert into risk_fill_fields
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="contents != null">contents,</if>
            <if test="isMust != null">is_must,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="contents != null">#{contents},</if>
            <if test="isMust != null">#{isMust},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateFillFields" parameterType="FillFields">
        update risk_fill_fields
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="contents != null">contents = #{contents},</if>
            <if test="isMust != null">is_must = #{isMust},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFillFieldsById" parameterType="Long">
        delete from risk_fill_fields where id = #{id}
    </delete>

    <delete id="deleteFillFieldsByIds" parameterType="Long">
        delete from risk_fill_fields where id = #{id}
    </delete>
</mapper>