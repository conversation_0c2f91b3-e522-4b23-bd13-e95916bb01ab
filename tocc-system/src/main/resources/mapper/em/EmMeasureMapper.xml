<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.em.mapper.EmMeasureMapper">

    <resultMap type="EmMeasure" id="EmMeasureResult">
        <result property="id"    column="id"    />
        <result property="version"    column="version"    />
        <result property="prePlanId"    column="pre_plan_id"    />
        <result property="eventLevel"    column="event_level"    />
        <result property="triggerCondition"    column="trigger_condition"    />
        <result property="measureContent"    column="measure_content"    />
        <result property="isDispatchTeam"    column="is_dispatch_team"    />
        <result property="needReport"    column="need_report"    />
        <result property="resourceTypes"    column="resource_types"    />
        <result property="specialOperations"    column="special_operations"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmMeasureVo">
        select id, version, pre_plan_id, event_level, trigger_condition, measure_content, is_dispatch_team, need_report, resource_types, special_operations, create_time, creator, update_time, updater, del_flag from em_measure
    </sql>

    <select id="selectEmMeasureList" parameterType="EmMeasure" resultMap="EmMeasureResult">
        <include refid="selectEmMeasureVo"/>
        <where>
            del_flag = 0
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="prePlanId != null  and prePlanId != ''"> and pre_plan_id = #{prePlanId}</if>
            <if test="eventLevel != null "> and event_level = #{eventLevel}</if>
            <if test="triggerCondition != null  and triggerCondition != ''"> and trigger_condition = #{triggerCondition}</if>
            <if test="measureContent != null  and measureContent != ''"> and measure_content = #{measureContent}</if>
            <if test="isDispatchTeam != null "> and is_dispatch_team = #{isDispatchTeam}</if>
            <if test="needReport != null "> and need_report = #{needReport}</if>
            <if test="resourceTypes != null  and resourceTypes != ''"> and resource_types = #{resourceTypes}</if>
            <if test="specialOperations != null  and specialOperations != ''"> and special_operations = #{specialOperations}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
        </where>
    </select>

    <select id="selectEmMeasureById" parameterType="Long" resultMap="EmMeasureResult">
        <include refid="selectEmMeasureVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <insert id="insertEmMeasure" parameterType="EmMeasure">
        insert into em_measure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="version != null">version,</if>
            <if test="prePlanId != null and prePlanId != ''">pre_plan_id,</if>
            <if test="eventLevel != null">event_level,</if>
            <if test="triggerCondition != null and triggerCondition != ''">trigger_condition,</if>
            <if test="measureContent != null and measureContent != ''">measure_content,</if>
            <if test="isDispatchTeam != null">is_dispatch_team,</if>
            <if test="needReport != null">need_report,</if>
            <if test="resourceTypes != null">resource_types,</if>
            <if test="specialOperations != null">special_operations,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="version != null">#{version},</if>
            <if test="prePlanId != null and prePlanId != ''">#{prePlanId},</if>
            <if test="eventLevel != null">#{eventLevel},</if>
            <if test="triggerCondition != null and triggerCondition != ''">#{triggerCondition},</if>
            <if test="measureContent != null and measureContent != ''">#{measureContent},</if>
            <if test="isDispatchTeam != null">#{isDispatchTeam},</if>
            <if test="needReport != null">#{needReport},</if>
            <if test="resourceTypes != null">#{resourceTypes},</if>
            <if test="specialOperations != null">#{specialOperations},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmMeasure" parameterType="EmMeasure">
        update em_measure
        <trim prefix="SET" suffixOverrides=",">
            <if test="version != null">version = #{version},</if>
            <if test="prePlanId != null and prePlanId != ''">pre_plan_id = #{prePlanId},</if>
            <if test="eventLevel != null">event_level = #{eventLevel},</if>
            <if test="triggerCondition != null and triggerCondition != ''">trigger_condition = #{triggerCondition},</if>
            <if test="measureContent != null and measureContent != ''">measure_content = #{measureContent},</if>
            <if test="isDispatchTeam != null">is_dispatch_team = #{isDispatchTeam},</if>
            <if test="needReport != null">need_report = #{needReport},</if>
            <if test="resourceTypes != null">resource_types = #{resourceTypes},</if>
            <if test="specialOperations != null">special_operations = #{specialOperations},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmMeasureById" parameterType="String">
        update  em_measure set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteEmMeasureByIds" parameterType="String">
        delete from em_measure where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
