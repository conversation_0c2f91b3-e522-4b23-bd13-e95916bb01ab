<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.em.mapper.EmWarehouseMapper">
    
    <resultMap type="EmWarehouse" id="EmWarehouseResult">
        <result property="id"    column="id"    />
        <result property="warehouseName"    column="warehouse_name"    />
        <result property="warehouseType"    column="warehouse_type"    />
        <result property="belongOrgCode"    column="belong_org_code"    />
        <result property="belongOrgName"    column="belong_org_name"    />
        <result property="address"    column="address"    />
        <result property="principal"    column="principal"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="roadCode"    column="road_code"    />
        <result property="stake"    column="stake"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectEmWarehouseVo">
        select id, warehouse_name, warehouse_type, belong_org_code, belong_org_name, address, principal, contact_phone, road_code, stake, latitude, longitude, remark, create_time, creator, update_time, updater, del_flag from em_warehouse
    </sql>

    <select id="selectEmWarehouseList" parameterType="EmWarehouse" resultMap="EmWarehouseResult">
        <include refid="selectEmWarehouseVo"/>
        <where>  
            <if test="warehouseName != null  and warehouseName != ''"> and warehouse_name like concat('%', #{warehouseName}, '%')</if>
            <if test="warehouseType != null  and warehouseType != ''"> and warehouse_type = #{warehouseType}</if>
            <if test="belongOrgCode != null  and belongOrgCode != ''"> and belong_org_code = #{belongOrgCode}</if>
            <if test="belongOrgName != null  and belongOrgName != ''"> and belong_org_name like concat('%', #{belongOrgName}, '%')</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="principal != null  and principal != ''"> and principal = #{principal}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="roadCode != null  and roadCode != ''"> and road_code = #{roadCode}</if>
            <if test="stake != null  and stake != ''"> and stake = #{stake}</if>
            <if test="latitude != null "> and latitude = #{latitude}</if>
            <if test="longitude != null "> and longitude = #{longitude}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
        </where>
    </select>
    
    <select id="selectEmWarehouseById" parameterType="String" resultMap="EmWarehouseResult">
        <include refid="selectEmWarehouseVo"/>
        where id = #{id}
    </select>

    <insert id="insertEmWarehouse" parameterType="EmWarehouse">
        insert into em_warehouse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="warehouseName != null and warehouseName != ''">warehouse_name,</if>
            <if test="warehouseType != null and warehouseType != ''">warehouse_type,</if>
            <if test="belongOrgCode != null and belongOrgCode != ''">belong_org_code,</if>
            <if test="belongOrgName != null and belongOrgName != ''">belong_org_name,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="principal != null and principal != ''">principal,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="roadCode != null">road_code,</if>
            <if test="stake != null">stake,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="delFlag != null">del_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="warehouseName != null and warehouseName != ''">#{warehouseName},</if>
            <if test="warehouseType != null and warehouseType != ''">#{warehouseType},</if>
            <if test="belongOrgCode != null and belongOrgCode != ''">#{belongOrgCode},</if>
            <if test="belongOrgName != null and belongOrgName != ''">#{belongOrgName},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="principal != null and principal != ''">#{principal},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="roadCode != null">#{roadCode},</if>
            <if test="stake != null">#{stake},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="delFlag != null">#{delFlag},</if>
         </trim>
    </insert>

    <update id="updateEmWarehouse" parameterType="EmWarehouse">
        update em_warehouse
        <trim prefix="SET" suffixOverrides=",">
            <if test="warehouseName != null and warehouseName != ''">warehouse_name = #{warehouseName},</if>
            <if test="warehouseType != null and warehouseType != ''">warehouse_type = #{warehouseType},</if>
            <if test="belongOrgCode != null and belongOrgCode != ''">belong_org_code = #{belongOrgCode},</if>
            <if test="belongOrgName != null and belongOrgName != ''">belong_org_name = #{belongOrgName},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="principal != null and principal != ''">principal = #{principal},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="roadCode != null">road_code = #{roadCode},</if>
            <if test="stake != null">stake = #{stake},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmWarehouseById" parameterType="String">
        delete from em_warehouse where id = #{id}
    </delete>

    <delete id="deleteEmWarehouseByIds" parameterType="String">
        delete from em_warehouse where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>