<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.em.mapper.EmPrePlanFileMapper">

    <resultMap type="EmPrePlanFile" id="EmPrePlanFileResult">
        <result property="id"    column="id"    />
        <result property="version"    column="version"    />
        <result property="bizId"    column="biz_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileDesc"    column="file_desc"    />
        <result property="createTime"    column="create_time"    />
        <result property="creator"    column="creator"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updater"    column="updater"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="url"    column="url"    />
        <result property="fileSize"    column="file_size"    />
        <result property="originalFileName"    column="original_file_name"    />
        <result property="newFileName"    column="new_file_name"    />
    </resultMap>

    <sql id="selectEmPrePlanFileVo">
        select id, version, biz_id, file_name, file_type, file_desc, create_time, creator, update_time, updater, del_flag, url, file_size, original_file_name, new_file_name from em_pre_plan_file
    </sql>

    <select id="selectEmPrePlanFileList" parameterType="EmPrePlanFile" resultMap="EmPrePlanFileResult">
        <include refid="selectEmPrePlanFileVo"/>
        <where>
            del_flag = 0
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="bizId != null  and bizId != ''"> and biz_id = #{bizId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileDesc != null  and fileDesc != ''"> and file_desc = #{fileDesc}</if>
            <if test="creator != null  and creator != ''"> and creator = #{creator}</if>
            <if test="updater != null  and updater != ''"> and updater = #{updater}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="originalFileName != null  and originalFileName != ''"> and original_file_name like concat('%', #{originalFileName}, '%')</if>
            <if test="newFileName != null  and newFileName != ''"> and new_file_name like concat('%', #{newFileName}, '%')</if>
        </where>
    </select>

    <select id="selectEmPrePlanFileById" parameterType="String" resultMap="EmPrePlanFileResult">
        <include refid="selectEmPrePlanFileVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <insert id="insertEmPrePlanFile" parameterType="EmPrePlanFile">
        insert into em_pre_plan_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="version != null">version,</if>
            <if test="bizId != null">biz_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileType != null and fileType != ''">file_type,</if>
            <if test="fileDesc != null">file_desc,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creator != null">creator,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updater != null">updater,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="url != null">url,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="originalFileName != null">original_file_name,</if>
            <if test="newFileName != null">new_file_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="version != null">#{version},</if>
            <if test="bizId != null">#{bizId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileType != null and fileType != ''">#{fileType},</if>
            <if test="fileDesc != null">#{fileDesc},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updater != null">#{updater},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="url != null">#{url},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="originalFileName != null">#{originalFileName},</if>
            <if test="newFileName != null">#{newFileName},</if>
         </trim>
    </insert>

    <update id="updateEmPrePlanFile" parameterType="EmPrePlanFile">
        update em_pre_plan_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="version != null">version = #{version},</if>
            <if test="bizId != null">biz_id = #{bizId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="fileDesc != null">file_desc = #{fileDesc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updater != null">updater = #{updater},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="url != null">url = #{url},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="originalFileName != null">original_file_name = #{originalFileName},</if>
            <if test="newFileName != null">new_file_name = #{newFileName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmPrePlanFileById" parameterType="String">
        update  em_pre_plan_file set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteEmPrePlanFileByIds" parameterType="String">
        delete from em_pre_plan_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
