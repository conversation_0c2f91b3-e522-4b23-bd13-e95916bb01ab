package com.tocc.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tocc.common.annotation.Excel;

import java.util.Date;

/**
 * 告警信息VO
 * 
 * <AUTHOR>
 */
public class AlarmInfoVO {
    
    /** 告警ID */
    private String alarmId;

    /** 告警标题 */
    private String alarmTitle;

    /** 告警类型（字典值） */
    private String alarmType;

    /** 告警类型名称 */
    private String alarmTypeName;

    /** 告警子类型（字典值） */
    private String alarmSubtype;

    /** 告警子类型名称 */
    private String alarmSubtypeName;

    /** 告警级别（字典值） */
    private String alarmLevel;

    /** 告警级别名称 */
    private String alarmLevelName;

    /** 告警内容 */
    private String alarmContent;

    /** 关联源数据ID */
    private String sourceId;

    /** 源数据类型（字典值） */
    private String sourceType;

    /** 源数据类型名称 */
    private String sourceTypeName;

    /** 所属组织ID */
    private String orgId;

    /** 所属组织名称 */
    private String orgName;

    /** 状态（0未处理 1已处理 2已忽略） */
    private String status;

    /** 状态名称 */
    private String statusName;

    /** 告警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date alarmTime;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processTime;

    /** 处理人ID */
    private String processorId;

    /** 处理人姓名 */
    private String processorName;

    /** 处理结果 */
    private String processResult;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 备注 */
    private String remark;

    /** 行政辖区ID */
    private String administrativeAreaId;

    /** 行政辖区名称 */
    private String administrativeArea;

    // Getter and Setter methods
    public String getAlarmId() {
        return alarmId;
    }

    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }

    public String getAlarmTitle() {
        return alarmTitle;
    }

    public void setAlarmTitle(String alarmTitle) {
        this.alarmTitle = alarmTitle;
    }

    public String getAlarmType() {
        return alarmType;
    }

    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }

    public String getAlarmTypeName() {
        return alarmTypeName;
    }

    public void setAlarmTypeName(String alarmTypeName) {
        this.alarmTypeName = alarmTypeName;
    }

    public String getAlarmSubtype() {
        return alarmSubtype;
    }

    public void setAlarmSubtype(String alarmSubtype) {
        this.alarmSubtype = alarmSubtype;
    }

    public String getAlarmSubtypeName() {
        return alarmSubtypeName;
    }

    public void setAlarmSubtypeName(String alarmSubtypeName) {
        this.alarmSubtypeName = alarmSubtypeName;
    }

    public String getAlarmLevel() {
        return alarmLevel;
    }

    public void setAlarmLevel(String alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    public String getAlarmLevelName() {
        return alarmLevelName;
    }

    public void setAlarmLevelName(String alarmLevelName) {
        this.alarmLevelName = alarmLevelName;
    }

    public String getAlarmContent() {
        return alarmContent;
    }

    public void setAlarmContent(String alarmContent) {
        this.alarmContent = alarmContent;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public String getSourceTypeName() {
        return sourceTypeName;
    }

    public void setSourceTypeName(String sourceTypeName) {
        this.sourceTypeName = sourceTypeName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Date getProcessTime() {
        return processTime;
    }

    public void setProcessTime(Date processTime) {
        this.processTime = processTime;
    }

    public String getProcessorId() {
        return processorId;
    }

    public void setProcessorId(String processorId) {
        this.processorId = processorId;
    }

    public String getProcessorName() {
        return processorName;
    }

    public void setProcessorName(String processorName) {
        this.processorName = processorName;
    }

    public String getProcessResult() {
        return processResult;
    }

    public void setProcessResult(String processResult) {
        this.processResult = processResult;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAdministrativeAreaId() {
        return administrativeAreaId;
    }

    public void setAdministrativeAreaId(String administrativeAreaId) {
        this.administrativeAreaId = administrativeAreaId;
    }

    public String getAdministrativeArea() {
        return administrativeArea;
    }

    public void setAdministrativeArea(String administrativeArea) {
        this.administrativeArea = administrativeArea;
    }


    @Override
    public String toString() {
        return "AlarmInfoVO{" +
                "alarmId='" + alarmId + '\'' +
                ", alarmTitle='" + alarmTitle + '\'' +
                ", alarmType='" + alarmType + '\'' +
                ", alarmTypeName='" + alarmTypeName + '\'' +
                ", alarmSubtype='" + alarmSubtype + '\'' +
                ", alarmSubtypeName='" + alarmSubtypeName + '\'' +
                ", alarmLevel='" + alarmLevel + '\'' +
                ", alarmLevelName='" + alarmLevelName + '\'' +
                ", alarmContent='" + alarmContent + '\'' +
                ", sourceId='" + sourceId + '\'' +
                ", sourceType='" + sourceType + '\'' +
                ", sourceTypeName='" + sourceTypeName + '\'' +
                ", orgId='" + orgId + '\'' +
                ", orgName='" + orgName + '\'' +
                ", status='" + status + '\'' +
                ", statusName='" + statusName + '\'' +
                ", alarmTime=" + alarmTime +
                ", processTime=" + processTime +
                ", processorId='" + processorId + '\'' +
                ", processorName='" + processorName + '\'' +
                ", processResult='" + processResult + '\'' +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", remark='" + remark + '\'' +
                ", administrativeArea='" + administrativeArea + '\'' +
                ", administrativeAreaId='" + administrativeAreaId + '\'' +
                '}';
    }
}
