package com.tocc.common.enums;

import cn.hutool.core.util.ObjectUtil;

import java.util.Arrays;

/**
 * 应急事件等级
 */
public enum LevelEnum {
    one(1, "I", "一级"),
    two(2, "II", "二级"),
    three(3, "III", "三级"),
    four(4, "IV", "四级")
    ;
    private final Integer code;
    private final String romeDesc;
    private final String desc;

    LevelEnum(Integer code, String romeDesc, String desc) {
        this.code = code;
        this.romeDesc = romeDesc;
        this.desc = desc;
    }
    /**
     * 根据Code获取罗马数字等级
     * @param code
     * @return
     */
    public static String getRomeDescByCode(Integer code) {
        LevelEnum levelEnum = Arrays.stream(LevelEnum.values())
                .filter(e -> ObjectUtil.equals(e.getCode(), code))
                .findFirst()
                .orElse(null);
        if(levelEnum == null) {
            return String.valueOf(code);
        }
        return levelEnum.getRomeDesc();
    }


    public Integer getCode() {
        return code;
    }

    public String getRomeDesc() {
        return romeDesc;
    }

    public String getDesc() {
        return desc;
    }
}
