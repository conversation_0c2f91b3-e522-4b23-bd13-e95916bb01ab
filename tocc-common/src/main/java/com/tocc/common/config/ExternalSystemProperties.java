package com.tocc.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 外部系统配置属性
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "external.system")
public class ExternalSystemProperties 
{
    /**
     * 基础URL
     */
    private String baseUrl = "http://localhost:8080/api";
    
    /**
     * 连接超时时间
     */
    private int connectTimeout = 5000;
    
    /**
     * 读取超时时间
     */
    private int readTimeout = 10000;
    
    /**
     * API密钥
     */
    private String apiKey = "";
    
    /**
     * 是否启用
     */
    private boolean enabled = true;
    
    /**
     * 认证配置
     */
    private Auth auth = new Auth();

    public String getBaseUrl() 
    {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) 
    {
        this.baseUrl = baseUrl;
    }

    public int getConnectTimeout() 
    {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) 
    {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() 
    {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) 
    {
        this.readTimeout = readTimeout;
    }

    public String getApiKey() 
    {
        return apiKey;
    }

    public void setApiKey(String apiKey) 
    {
        this.apiKey = apiKey;
    }

    public boolean isEnabled() 
    {
        return enabled;
    }

    public void setEnabled(boolean enabled) 
    {
        this.enabled = enabled;
    }

    public Auth getAuth() 
    {
        return auth;
    }

    public void setAuth(Auth auth) 
    {
        this.auth = auth;
    }
    
    /**
     * 认证配置类
     */
    public static class Auth 
    {
        /**
         * 登录用户名
         */
        private String username = "admin";
        
        /**
         * 登录密码
         */
        private String password = "admin123";
        
        /**
         * 登录接口端点
         */
        private String loginEndpoint = "/system/auth/login";
        
        /**
         * 用户信息接口端点
         */
        private String userinfoEndpoint = "/system/auth/get-permission-info";
        
        /**
         * Token刷新接口端点
         */
        private String refreshEndpoint = "/system/auth/refresh-token";
        
        /**
         * Token过期前多少秒开始刷新
         */
        private long refreshBeforeExpire = 300;
        
        /**
         * 密码加密类型
         */
        private String passwordEncryptType = "bcrypt";

        public String getUsername() 
        {
            return username;
        }

        public void setUsername(String username) 
        {
            this.username = username;
        }

        public String getPassword() 
        {
            return password;
        }

        public void setPassword(String password) 
        {
            this.password = password;
        }

        public String getLoginEndpoint() 
        {
            return loginEndpoint;
        }

        public void setLoginEndpoint(String loginEndpoint) 
        {
            this.loginEndpoint = loginEndpoint;
        }

        public String getUserinfoEndpoint() 
        {
            return userinfoEndpoint;
        }

        public void setUserinfoEndpoint(String userinfoEndpoint) 
        {
            this.userinfoEndpoint = userinfoEndpoint;
        }

        public String getRefreshEndpoint() 
        {
            return refreshEndpoint;
        }

        public void setRefreshEndpoint(String refreshEndpoint) 
        {
            this.refreshEndpoint = refreshEndpoint;
        }

        public long getRefreshBeforeExpire() 
        {
            return refreshBeforeExpire;
        }

        public void setRefreshBeforeExpire(long refreshBeforeExpire) 
        {
            this.refreshBeforeExpire = refreshBeforeExpire;
        }

        public String getPasswordEncryptType() 
        {
            return passwordEncryptType;
        }

        public void setPasswordEncryptType(String passwordEncryptType) 
        {
            this.passwordEncryptType = passwordEncryptType;
        }
    }
} 