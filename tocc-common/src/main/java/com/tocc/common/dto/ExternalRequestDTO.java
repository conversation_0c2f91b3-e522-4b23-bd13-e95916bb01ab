package com.tocc.common.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;

/**
 * 外部系统请求参数DTO
 * 
 * <AUTHOR>
 */
@ApiModel(description = "外部系统请求参数")
public class ExternalRequestDTO 
{
    @ApiModelProperty(value = "HTTP方法", notes = "GET, POST等，默认为GET")
    private String method = "GET";
    
    @ApiModelProperty(value = "接口端点", required = true, example = "/api/data/list")
    private String endpoint;
    
    @ApiModelProperty(value = "自定义请求头")
    private Map<String, String> headers;
    
    @ApiModelProperty(value = "请求参数（GET方法使用）")
    private Map<String, Object> params;
    
    @ApiModelProperty(value = "请求数据（POST方法使用）")
    private Object data;
    
    @ApiModelProperty(value = "是否使用认证token", notes = "true: 自动添加Authorization头")
    private boolean useAuth = true;
    
    @ApiModelProperty(value = "是否使用缓存", notes = "仅对GET方法有效")
    private boolean useCache = false;
    
    public ExternalRequestDTO() 
    {
    }
    
    public ExternalRequestDTO(String method, String endpoint) 
    {
        this.method = method;
        this.endpoint = endpoint;
    }
    
    public String getMethod() 
    {
        return method;
    }
    
    public void setMethod(String method) 
    {
        this.method = method;
    }
    
    public String getEndpoint() 
    {
        return endpoint;
    }
    
    public void setEndpoint(String endpoint) 
    {
        this.endpoint = endpoint;
    }
    
    public Map<String, String> getHeaders() 
    {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) 
    {
        this.headers = headers;
    }
    
    public Map<String, Object> getParams() 
    {
        return params;
    }
    
    public void setParams(Map<String, Object> params) 
    {
        this.params = params;
    }
    
    public Object getData() 
    {
        return data;
    }
    
    public void setData(Object data) 
    {
        this.data = data;
    }
    
    public boolean isUseAuth() 
    {
        return useAuth;
    }
    
    public void setUseAuth(boolean useAuth) 
    {
        this.useAuth = useAuth;
    }
    
    public boolean isUseCache() 
    {
        return useCache;
    }
    
    public void setUseCache(boolean useCache) 
    {
        this.useCache = useCache;
    }
    
    @Override
    public String toString() 
    {
        return "ExternalRequestDTO{" +
                "method='" + method + '\'' +
                ", endpoint='" + endpoint + '\'' +
                ", headers=" + headers +
                ", params=" + params +
                ", data=" + data +
                ", useAuth=" + useAuth +
                ", useCache=" + useCache +
                '}';
    }
} 