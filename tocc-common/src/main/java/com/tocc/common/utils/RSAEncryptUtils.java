package com.tocc.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * RSA加密工具类
 * 用于与前端 JSEncrypt 保持一致的加密方式
 * 
 * <AUTHOR>
 * @date 2024
 */
public class RSAEncryptUtils {
    
    private static final Logger log = LoggerFactory.getLogger(RSAEncryptUtils.class);
    
    /**
     * 公钥（与前端保持一致）
     */
    private static final String PUBLIC_KEY = 
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDSUmOXyQmYYSnZacp0btvAZCOvCNPtzixAp7eJmzmAG4mgy/VgrY/s1BDLh9qTNHIRWXepUtwMrf1kYul/A45qE/2oxIbeeq4238YDWQ7ModOVXR9ytEHsT0jpCFvoYfYXYZnnoWRrLIBylQeXzqxbLDxxBxGCs4AjoRKh5S7nNQIDAQAB";
    
    /**
     * RSA加密算法
     */
    private static final String RSA_ALGORITHM = "RSA";
    
    /**
     * RSA加密填充方式（与 JSEncrypt 默认方式一致）
     */
    private static final String RSA_TRANSFORMATION = "RSA/ECB/PKCS1Padding";
    
    /**
     * 使用RSA公钥加密文本
     * 
     * @param plainText 明文
     * @return 加密后的Base64字符串，加密失败返回null
     */
    public static String encrypt(String plainText) {
        if (StringUtils.isEmpty(plainText)) {
            log.warn("RSA加密：明文为空");
            return null;
        }
        
        try {
            // 解码公钥
            byte[] keyBytes = Base64.getDecoder().decode(PUBLIC_KEY);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
            PublicKey publicKey = keyFactory.generatePublic(keySpec);
            
            // 创建加密器
            Cipher cipher = Cipher.getInstance(RSA_TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            
            // 加密数据
            byte[] plainTextBytes = plainText.getBytes("UTF-8");
            byte[] encryptedBytes = cipher.doFinal(plainTextBytes);
            
            // 返回Base64编码的加密结果
            String encryptedText = Base64.getEncoder().encodeToString(encryptedBytes);
            log.debug("RSA加密成功，明文长度：{}，密文长度：{}", plainText.length(), encryptedText.length());
            
            return encryptedText;
            
        } catch (Exception e) {
            log.error("RSA加密失败：{}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 验证加密功能（用于测试）
     * 
     * @param plainText 明文
     * @return 是否加密成功
     */
    public static boolean testEncrypt(String plainText) {
        String encrypted = encrypt(plainText);
        return encrypted != null && !encrypted.isEmpty();
    }
} 