package com.tocc.common.utils;

import com.tocc.common.utils.sign.Md5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 密码加密工具类
 * 
 * <AUTHOR>
 */
public class PasswordEncryptUtils 
{
    private static final Logger log = LoggerFactory.getLogger(PasswordEncryptUtils.class);
    
    private static final BCryptPasswordEncoder bcryptEncoder = new BCryptPasswordEncoder();
    
    /**
     * 根据加密类型加密密码
     * 
     * @param password 明文密码
     * @param encryptType 加密类型：bcrypt, md5, rsa, plain
     * @return 加密后的密码
     */
    public static String encryptPassword(String password, String encryptType) 
    {
        if (StringUtils.isEmpty(password)) 
        {
            return password;
        }
        
        switch (encryptType.toLowerCase()) 
        {
            case "bcrypt":
                return bcryptEncoder.encode(password);
                
            case "md5":
                return md5Encrypt(password);
                
            case "rsa":
                return RSAEncryptUtils.encrypt(password);
                
            case "plain":
                return password;
                
            default:
                log.warn("未知的密码加密类型: {}，使用明文模式", encryptType);
                return password;
        }
    }
    
    /**
     * MD5加密
     * 
     * @param password 明文密码
     * @return MD5加密后的密码
     */
    private static String md5Encrypt(String password) 
    {
        try 
        {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(password.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) 
            {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } 
        catch (NoSuchAlgorithmException e) 
        {
            log.error("MD5加密失败", e);
            return password;
        }
    }
    
    /**
     * 验证密码（仅BCrypt支持验证）
     * 
     * @param rawPassword 明文密码
     * @param encodedPassword 加密密码
     * @param encryptType 加密类型
     * @return 是否匹配
     */
    public static boolean matches(String rawPassword, String encodedPassword, String encryptType) 
    {
        if ("bcrypt".equalsIgnoreCase(encryptType)) 
        {
            return bcryptEncoder.matches(rawPassword, encodedPassword);
        }
        
        // 其他加密方式直接比较加密后的结果
        return encryptPassword(rawPassword, encryptType).equals(encodedPassword);
    }
} 