package com.tocc.common.utils.http;

import com.tocc.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.*;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.Map;

/**
 * HTTP客户端工具类（支持自定义请求头）
 * 
 * <AUTHOR>
 */
public class HttpClientHelper 
{
    private static final Logger log = LoggerFactory.getLogger(HttpClientHelper.class);
    
    private static final int DEFAULT_TIMEOUT = 10000; // 10秒超时
    
    /**
     * 发送GET请求（支持自定义请求头）
     * 
     * @param url 请求URL
     * @param headers 自定义请求头
     * @param timeout 超时时间（毫秒）
     * @return 响应内容
     */
    public static String get(String url, Map<String, String> headers, int timeout) 
    {
        HttpURLConnection connection = null;
        BufferedReader reader = null;
        
        try 
        {
            log.info("发送GET请求: {}", url);
            
            URL urlObj = new URL(url);
            connection = (HttpURLConnection) urlObj.openConnection();
            
            // 设置请求方法
            connection.setRequestMethod("GET");
            
            // 设置超时
            connection.setConnectTimeout(timeout > 0 ? timeout : DEFAULT_TIMEOUT);
            connection.setReadTimeout(timeout > 0 ? timeout : DEFAULT_TIMEOUT);
            
            // 设置默认请求头
            connection.setRequestProperty("Accept", "*/*");
            connection.setRequestProperty("Connection", "Keep-Alive");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
            
            // 设置自定义请求头
            if (headers != null && !headers.isEmpty()) 
            {
                for (Map.Entry<String, String> entry : headers.entrySet()) 
                {
                    connection.setRequestProperty(entry.getKey(), entry.getValue());
                    log.debug("设置请求头: {} = {}", entry.getKey(), entry.getValue());
                }
            }
            
            // 发送请求
            connection.connect();
            
            // 读取响应
            int responseCode = connection.getResponseCode();
            log.debug("响应状态码: {}", responseCode);
            
            InputStream inputStream = responseCode >= 200 && responseCode < 300 
                ? connection.getInputStream() 
                : connection.getErrorStream();
                
            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            StringBuilder response = new StringBuilder();
            String line;
            
            while ((line = reader.readLine()) != null) 
            {
                response.append(line);
            }
            
            String result = response.toString();
            log.debug("GET请求响应: {}", result);
            
            return result;
            
        } 
        catch (ConnectException e) 
        {
            log.error("GET请求连接异常: {}", url, e);
            throw new RuntimeException("连接失败: " + e.getMessage());
        } 
        catch (SocketTimeoutException e) 
        {
            log.error("GET请求超时: {}", url, e);
            throw new RuntimeException("请求超时: " + e.getMessage());
        } 
        catch (IOException e) 
        {
            log.error("GET请求IO异常: {}", url, e);
            throw new RuntimeException("IO异常: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            log.error("GET请求异常: {}", url, e);
            throw new RuntimeException("请求异常: " + e.getMessage());
        } 
        finally 
        {
            // 关闭资源
            if (reader != null) 
            {
                try 
                {
                    reader.close();
                } 
                catch (IOException e) 
                {
                    log.warn("关闭reader失败", e);
                }
            }
            if (connection != null) 
            {
                connection.disconnect();
            }
        }
    }
    
    /**
     * 发送POST请求（支持自定义请求头）
     * 
     * @param url 请求URL
     * @param headers 自定义请求头
     * @param body 请求体
     * @return 响应内容
     */
    public static String post(String url, Map<String, String> headers, String body) 
    {
        return post(url, headers, body, DEFAULT_TIMEOUT);
    }
    
    /**
     * 发送POST请求（支持自定义请求头和超时设置）
     * 
     * @param url 请求URL
     * @param headers 自定义请求头
     * @param body 请求体
     * @param timeout 超时时间（毫秒）
     * @return 响应内容
     */
    public static String post(String url, Map<String, String> headers, String body, int timeout) 
    {
        HttpURLConnection connection = null;
        PrintWriter writer = null;
        BufferedReader reader = null;
        
        try 
        {
            log.info("发送POST请求: {}", url);
            
            URL urlObj = new URL(url);
            connection = (HttpURLConnection) urlObj.openConnection();
            
            // 设置请求方法
            connection.setRequestMethod("POST");
            
            // 设置超时
            connection.setConnectTimeout(timeout > 0 ? timeout : DEFAULT_TIMEOUT);
            connection.setReadTimeout(timeout > 0 ? timeout : DEFAULT_TIMEOUT);
            
            // 允许输入输出
            connection.setDoInput(true);
            connection.setDoOutput(true);
            
            // 设置默认请求头
            connection.setRequestProperty("Accept", "*/*");
            connection.setRequestProperty("Connection", "Keep-Alive");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
            connection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            
            // 设置自定义请求头
            if (headers != null && !headers.isEmpty()) 
            {
                for (Map.Entry<String, String> entry : headers.entrySet()) 
                {
                    connection.setRequestProperty(entry.getKey(), entry.getValue());
                    log.debug("设置请求头: {} = {}", entry.getKey(), entry.getValue());
                }
            }
            
            // 写入请求体
            if (StringUtils.isNotEmpty(body)) 
            {
                writer = new PrintWriter(new OutputStreamWriter(connection.getOutputStream(), StandardCharsets.UTF_8));
                writer.print(body);
                writer.flush();
                log.debug("POST请求体: {}", body);
            }
            
            // 读取响应
            int responseCode = connection.getResponseCode();
            log.debug("响应状态码: {}", responseCode);
            
            InputStream inputStream = responseCode >= 200 && responseCode < 300 
                ? connection.getInputStream() 
                : connection.getErrorStream();
                
            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            StringBuilder response = new StringBuilder();
            String line;
            
            while ((line = reader.readLine()) != null) 
            {
                response.append(line);
            }
            
            String result = response.toString();
            log.debug("POST请求响应: {}", result);
            
            return result;
            
        } 
        catch (ConnectException e) 
        {
            log.error("POST请求连接异常: {}", url, e);
            throw new RuntimeException("连接失败: " + e.getMessage());
        } 
        catch (SocketTimeoutException e) 
        {
            log.error("POST请求超时: {}", url, e);
            throw new RuntimeException("请求超时: " + e.getMessage());
        } 
        catch (IOException e) 
        {
            log.error("POST请求IO异常: {}", url, e);
            throw new RuntimeException("IO异常: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            log.error("POST请求异常: {}", url, e);
            throw new RuntimeException("请求异常: " + e.getMessage());
        } 
        finally 
        {
            // 关闭资源
            if (writer != null) 
            {
                writer.close();
            }
            if (reader != null) 
            {
                try 
                {
                    reader.close();
                } 
                catch (IOException e) 
                {
                    log.warn("关闭reader失败", e);
                }
            }
            if (connection != null) 
            {
                connection.disconnect();
            }
        }
    }
} 