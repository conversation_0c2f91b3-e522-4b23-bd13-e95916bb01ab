package com.tocc.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ccc.model.v20200701.CreateUserResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.ram.model.v20150501.CreateAccessKeyRequest;
import com.aliyuncs.ram.model.v20150501.CreateAccessKeyResponse;
import com.tocc.common.domain.dto.SkillLevelDTO;
import com.tocc.common.domain.vo.UserCccVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.InvalidParameterException;
import java.util.*;

public class AliyunCccUtils {

    private static final String REGION = "cn-shanghai";
    private static final String PRODUCT = "CCC";
    private static final String ENDPOINT = "CCC";
    private static final String DOMAIN = "ccc.cn-shanghai.aliyuncs.com";
    private static final String VERSION = "2020-07-01";

    private static final String RAM_REGION = "cn-hangzhou";
    private static final String RAM_PRODUCT = "RAM";
    private static final String RAM_ENDPOINT = "RAM";
    private static final String RAM_DOMAIN = "ram.aliyuncs.com";

    public static final String INSTANCEID = "cloud_call_test";//实例ID
    public static final String AK = "LTAI5tDK74UimYfYmx9h3b7G";//管理坐席AK
    public static final String AS = "******************************";//管理坐席AS
    public static final String CODE_OK = "OK";//阿里云api成功状态码

    private static final Logger LOGGER = LoggerFactory.getLogger(AliyunCccUtils.class);

    private static DefaultAcsClient ramClient = null;

    /**
     * AK方式调用API
     * @param userCcc 登录名
     * @param action 接口名
     * @param request 请求参数
     * @return 结果
     */
    public static String invokeApiByAk(UserCccVO userCcc, String action, String request) {
        /**
         * 使用CommonAPI调用POP API时，和使用传统产品SDK相比，请求和返回参数的格式都有所不同，因此需要进行一定的格式转换。
         */
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setDomain(DOMAIN);
        commonRequest.setVersion(VERSION);
        commonRequest.setAction(action);
        JSONObject jsonObject = JSONObject.parseObject(request);

        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String key = entry.getKey().trim();
            if (key.length() > 1) {
                key = key.substring(0, 1).toUpperCase() + key.substring(1);
            } else if (key.length() == 1) {
                key = key.toUpperCase();
            } else {
                continue;
            }
            commonRequest.putQueryParameter(key, String.valueOf(entry.getValue()));
        }

        CommonResponse response = null;
        try {
            response = getCccClient(userCcc.getAccessKey(), userCcc.getAccessSecret()).getCommonResponse(commonRequest);
            System.out.println(JSONObject.toJSONString(response.getData()));
        } catch (ClientException e) {
            LOGGER.error("Failed to invoke open API, request=" + JSON.toJSONString(commonRequest), e);
            throw new IllegalArgumentException("requestId:" + e.getRequestId() + "errCode:" + e.getErrCode() + "," + e.getMessage());
        }

        JSONObject jsonResult = JSONObject.parseObject(response.getData());
        JSONObject newJsonResult = new JSONObject();
        copyObject(newJsonResult, jsonResult);
        return JSONObject.toJSONString(newJsonResult);
    }

    public static void copyObject(JSONObject destination, JSONObject source) {
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            String key = entry.getKey().trim();
            if (key.length() > 1) {
                key = key.substring(0, 1).toLowerCase() + key.substring(1);
            } else if (key.length() == 1) {
                key = key.toUpperCase();
            } else {
                continue;
            }

            Object object = entry.getValue();

            if (object instanceof JSONObject) {
                JSONObject tempObject = (JSONObject) object;
                if (tempObject.entrySet().size() == 1) {
                    Object theOne = tempObject.entrySet().iterator().next().getValue();
                    if (theOne instanceof JSONArray) {
                        JSONArray newArray = new JSONArray();
                        destination.put(key, newArray);
                        copyArray(newArray, (JSONArray) theOne);
                        continue;
                    }
                }
                JSONObject newObject = new JSONObject();
                destination.put(key, newObject);
                copyObject(newObject, (JSONObject) object);
            } else if (object instanceof JSONArray) {
                JSONArray newArray = new JSONArray();
                destination.put(key, newArray);
                copyArray(newArray, (JSONArray) object);
            } else {
                destination.put(key, object);
            }
        }
    }

    private static void copyArray(JSONArray destination, JSONArray source) {
        Iterator<Object> iterator = source.iterator();
        while (iterator.hasNext()) {
            Object object = iterator.next();
            if (object instanceof JSONObject) {
                JSONObject newObject = new JSONObject();
                destination.add(newObject);
                copyObject(newObject, (JSONObject) object);
            } else if (object instanceof JSONArray) {
                JSONArray newArray = new JSONArray();
                destination.add(newArray);
                copyArray(newArray, (JSONArray) object);
            } else {
                destination.add(object);
            }
        }
    }

    private synchronized static IAcsClient getCccClient(String ak, String as) {
        DefaultAcsClient client = null;
//        CccUser user = cccRepository.findByUserName(loginName);
        client = createClient(ak, as);
        return client;
    }

    private static DefaultAcsClient createClient(String accessKeyId, String secret) {
        try {
            DefaultProfile.addEndpoint(ENDPOINT, REGION, PRODUCT, DOMAIN);
        } catch (ClientException e) {
            LOGGER.error(e.getMessage());
        }
        if (accessKeyId == null || secret == null) {
            throw new InvalidParameterException("ak, as may not be null.");
        }
        IClientProfile profile = DefaultProfile.getProfile(REGION, accessKeyId, secret);
        return new DefaultAcsClient(profile);
    }

    public static UserCccVO createUser(UserCccVO userCcc) throws ServerException, ClientException {
        String loginName = userCcc.getLoginName();
        String displayName = userCcc.getDisplayName();
        String email = "<EMAIL>";
        String roleId = "Agent@cloud_call_test";
        List<String> skillIdList = Arrays.asList("kefu@cloud_call_test");

        // 1、调用云呼 CreateUser 创建客服
        // TODO 此处采用云呼的SDK调用api，注意云呼2.0 SDK version为 20200701
        com.aliyuncs.ccc.model.v20200701.CreateUserRequest createRequest = new com.aliyuncs.ccc.model
                .v20200701.CreateUserRequest();
        createRequest.setDisplayName(displayName);
        createRequest.setLoginName(loginName);
        createRequest.setEmail(email);
        createRequest.setInstanceId(INSTANCEID);
        createRequest.setRoleId(roleId);
        createRequest.setWorkMode("ON_SITE");
        List<SkillLevelDTO> skillLevelList = new ArrayList<>();
        // TODO 技能组等级目前传入固定值5
        skillIdList.forEach(
                id -> skillLevelList.add(new SkillLevelDTO(id, 5))
        );
        createRequest.setSkillLevelList(JSON.toJSONString(skillLevelList));
        CreateUserResponse createUserResponse = getCccClient(AK, AS).getAcsResponse(createRequest);
        //409:已存在坐席
        if(createUserResponse.getHttpStatusCode() != 200 && createUserResponse.getHttpStatusCode() !=409) {
            throw new IllegalArgumentException(createUserResponse.getMessage());
        }

        // 2、调用 RAM CreateAccessKey 为用户创建访问密钥
        // 这里使用了 RAM SDK
        CreateAccessKeyRequest createAccessKeyRequest = new CreateAccessKeyRequest();
        createAccessKeyRequest.setUserName(loginName);
        CreateAccessKeyResponse createAccessKeyResponse = getRamClient(AK, AS).getAcsResponse(createAccessKeyRequest);
        CreateAccessKeyResponse.AccessKey accessKey = createAccessKeyResponse.getAccessKey();

        // 3、返回新创建的坐席相关信息录入数据库
        userCcc.setAccessKey(accessKey.getAccessKeyId());
        userCcc.setAccessSecret(accessKey.getAccessKeySecret());

        return userCcc;
    }

    private synchronized static IAcsClient getRamClient(String ak, String as) {
//        if (ramClient == null) {
//            synchronized (locker) {
//                if (ramClient == null) {
//                    ramClient = createRamClient(ak, as);
//                }
//            }
//        }
        ramClient = createRamClient(ak, as);
        return ramClient;
    }

    private static DefaultAcsClient createRamClient(String accessKeyId, String secret) {
        try {
            DefaultProfile.addEndpoint(RAM_ENDPOINT, RAM_REGION, RAM_PRODUCT, RAM_DOMAIN);
        } catch (ClientException e) {
            LOGGER.error(e.getMessage());
        }
        if (accessKeyId == null || secret == null) {
            throw new InvalidParameterException("ak, as may not be null.");
        }
        IClientProfile profile = DefaultProfile.getProfile(RAM_REGION, accessKeyId, secret);
        return new DefaultAcsClient(profile);
    }

}
