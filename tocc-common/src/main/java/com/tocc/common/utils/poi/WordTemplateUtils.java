package com.tocc.common.utils.poi;

import org.docx4j.XmlUtils;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.wml.Document;

import javax.xml.bind.JAXBElement;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WordTemplateUtils {

    /**
     * 替换Word模板中的占位符并输出到输出流
     *
     * @param templateStream 模板文件输入流
     * @param data           替换数据Map
     * @param outputStream   输出流
     * @throws Exception 处理过程中可能出现的异常
     */
    public static void processTemplate(InputStream templateStream, Map<String, String> data,
                                       OutputStream outputStream) throws Exception {
        //加载模板
        WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(templateStream);
        //替换内容
        replacePlaceholders(wordMLPackage, data);
        //保存到输出流
        wordMLPackage.save(outputStream);
    }

    /**
     * 替换Word文档中的占位符
     *
     * @param wordMLPackage Word文档对象
     * @param data          替换数据Map
     */
    private static void replacePlaceholders(WordprocessingMLPackage wordMLPackage, Map<String, String> data) throws Exception {
        //预处理数据,确保所有键都有${}包装
        Map<String, String> normalizedData = new HashMap<>();
        for (Map.Entry<String, String> entry : data.entrySet()) {
            String key = entry.getKey();
            String normalizedKey = key.startsWith("${") && key.endsWith("}") ?
                    key : "${" + key + "}";
            normalizedData.put(normalizedKey, entry.getValue());
        }
        //获取文档所有XML内容
        String xml = XmlUtils.marshaltoString(wordMLPackage.getMainDocumentPart().getJaxbElement(), true, true);
        //使用正则表达式全局替换
        for (Map.Entry<String, String> entry : normalizedData.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            String replacement = (value == null) ? "" : value;
            String regex = Pattern.quote(key);
            xml = xml.replaceAll(regex, Matcher.quoteReplacement(replacement));
        }
        //重新构建文档对象
        Object obj = XmlUtils.unmarshalString(xml);
        if (obj instanceof JAXBElement) {
            obj = ((JAXBElement<?>) obj).getValue();
        }
        wordMLPackage.getMainDocumentPart().setContents((Document) obj);
    }

}