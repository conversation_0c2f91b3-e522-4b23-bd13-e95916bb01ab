package com.tocc.common.utils.file;

import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSpacing;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STLineSpacingRule;

import java.math.BigInteger;
import java.util.Map;

public class WordUtils {


    /**
     * 通过段落标识符替换文本内容
     * @param paragraph
     * @param replacements
     */
    public static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> replacements) {
        String text = paragraph.getText();
        if (text != null && !text.isEmpty()) {
            boolean hasReplacement = false;
            for (Map.Entry<String, String> entry : replacements.entrySet()) {
                if (text.contains(entry.getKey())) {
                    text = text.replace(entry.getKey(), entry.getValue());
                    hasReplacement = true;
                }
            }

            if (hasReplacement) {
                // 清除原有内容
                for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                    paragraph.removeRun(i);
                }

                CTP ctp = paragraph.getCTP();
                CTPPr ppr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
                CTSpacing spacing = ppr.isSetSpacing() ? ppr.getSpacing() : ppr.addNewSpacing();
                // 2. 设置行距规则为固定值（关键！）
                spacing.setLineRule(STLineSpacingRule.EXACT); // 必须设为 EXACT[1,6](@ref)
                // 3. 设置28磅行距（1磅=20 twips → 28磅=560 twips）
                spacing.setLine(BigInteger.valueOf(560)); // 单位：twips[1,7](@ref)
                // 4. 可选：清除段前/段后间距
                spacing.setAfter(BigInteger.valueOf(0));
                spacing.setBefore(BigInteger.valueOf(0));
                // 添加新文本（保留段落格式）
                XWPFRun newRun = paragraph.createRun();
                String[] lines = text.split("\n");
                if (lines.length > 1) {
                    for (int i = 0; i < lines.length; i++) {
                        // 添加新的文本行，并设置换行格式
                        newRun.setText(lines[i]);
                        newRun.addBreak();
                    }
                } else {
                    newRun.setText(text);
                }
                newRun.setFontFamily("仿宋");
                newRun.setFontSize(16);
            }
        }
    }
}
