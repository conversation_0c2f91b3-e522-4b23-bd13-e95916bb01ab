package com.tocc.common.service;

import java.util.List;

/**
 * 组织架构通用服务接口
 * 
 * <AUTHOR>
 */
public interface IOrganizationService {
    
    /**
     * 获取单位及其所有子单位的ID列表
     * 
     * @param orgId 单位ID
     * @return 单位ID列表（包含自身和所有子单位）
     */
    List<String> getOrgIdsWithChildren(Long orgId);
    
    /**
     * 根据单位ID获取单位名称
     * 
     * @param orgId 单位ID
     * @return 单位名称
     */
    String getUnitNameByOrgId(Long orgId);
}
