package com.tocc.common.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.tocc.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 外部数据缓存服务类
 * 
 * <AUTHOR>
 */
@Service
public class ExternalDataCacheService 
{
    private static final Logger log = LoggerFactory.getLogger(ExternalDataCacheService.class);
    
    private static final String CACHE_PREFIX = "external:data:";
    private static final long DEFAULT_EXPIRE_TIME = 300; // 5分钟过期
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    /**
     * 获取缓存数据
     * 
     * @param key 缓存键
     * @return 缓存数据
     */
    public JSONObject getCachedData(String key) 
    {
        try 
        {
            String cacheKey = CACHE_PREFIX + key;
            String cachedValue = redisTemplate.opsForValue().get(cacheKey);
            
            if (StringUtils.isNotEmpty(cachedValue)) 
            {
                log.debug("从缓存获取数据: {}", key);
                return JSON.parseObject(cachedValue);
            }
        } 
        catch (Exception e) 
        {
            log.error("获取缓存数据失败: {}", e.getMessage(), e);
        }
        
        return null;
    }
    
    /**
     * 设置缓存数据
     * 
     * @param key 缓存键
     * @param data 数据
     */
    public void setCachedData(String key, JSONObject data) 
    {
        setCachedData(key, data, DEFAULT_EXPIRE_TIME);
    }
    
    /**
     * 设置缓存数据
     * 
     * @param key 缓存键
     * @param data 数据
     * @param expireSeconds 过期时间（秒）
     */
    public void setCachedData(String key, JSONObject data, long expireSeconds) 
    {
        try 
        {
            String cacheKey = CACHE_PREFIX + key;
            String jsonValue = JSON.toJSONString(data);
            
            redisTemplate.opsForValue().set(cacheKey, jsonValue, expireSeconds, TimeUnit.SECONDS);
            log.debug("设置缓存数据: {}", key);
        } 
        catch (Exception e) 
        {
            log.error("设置缓存数据失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 删除缓存数据
     * 
     * @param key 缓存键
     */
    public void deleteCachedData(String key) 
    {
        try 
        {
            String cacheKey = CACHE_PREFIX + key;
            redisTemplate.delete(cacheKey);
            log.debug("删除缓存数据: {}", key);
        } 
        catch (Exception e) 
        {
            log.error("删除缓存数据失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 检查缓存是否存在
     * 
     * @param key 缓存键
     * @return 是否存在
     */
    public boolean existsCachedData(String key) 
    {
        try 
        {
            String cacheKey = CACHE_PREFIX + key;
            return Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey));
        } 
        catch (Exception e) 
        {
            log.error("检查缓存是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 清空所有外部数据缓存
     */
    public void clearAllExternalCache() 
    {
        try 
        {
            String pattern = CACHE_PREFIX + "*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) 
            {
                redisTemplate.delete(keys);
                log.info("清空所有外部数据缓存，共删除 {} 个缓存", keys.size());
            }
        } 
        catch (Exception e) 
        {
            log.error("清空所有外部数据缓存失败: {}", e.getMessage(), e);
        }
    }
} 