package com.tocc.common.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.tocc.common.config.ExternalSystemProperties;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.PasswordEncryptUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.http.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 外部系统认证管理器
 * 
 * <AUTHOR>
 */
@Service
public class ExternalAuthManager 
{
    private static final Logger log = LoggerFactory.getLogger(ExternalAuthManager.class);
    
    private static final String REDIS_TOKEN_KEY = "external:auth:token";
    private static final String REDIS_REFRESH_TOKEN_KEY = "external:auth:refresh_token";
    private static final String REDIS_TOKEN_EXPIRE_KEY = "external:auth:expire_time";
    
    @Autowired
    private ExternalSystemProperties externalSystemProperties;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private ExternalSystemService externalSystemService;
    
    /**
     * 系统启动后自动登录
     */
    @PostConstruct
    public void autoLogin() 
    {
        if (externalSystemProperties.isEnabled()) 
        {
            try 
            {
                login();
            } 
            catch (Exception e) 
            {
                log.error("系统启动时自动登录失败", e);
            }
        }
    }
    
    /**
     * 登录外部系统
     */
    public boolean login() 
    {
        try 
        {
            ExternalSystemProperties.Auth authConfig = externalSystemProperties.getAuth();
            
            // 构建登录请求数据（按照芋道源码格式）
            Map<String, Object> loginData = new HashMap<>();
            loginData.put("captchaVerification", "");
            
            // 根据配置的加密类型加密密码
            String encryptedPassword = PasswordEncryptUtils.encryptPassword(
                authConfig.getPassword(),
                authConfig.getPasswordEncryptType()
            );
            loginData.put("password", encryptedPassword);
            loginData.put("rememberMe", false);
            loginData.put("username", authConfig.getUsername());
            
            log.info("开始登录外部系统，用户名：{}，加密类型：{}", 
                authConfig.getUsername(), authConfig.getPasswordEncryptType());
            
            // 调用登录接口
            JSONObject response = externalSystemService.callExternalPost(
                authConfig.getLoginEndpoint(), 
                loginData
            );
            
            // 解析登录响应
            if (isSuccessResponse(response)) 
            {
                String accessToken = extractToken(response, "accessToken", "token", "data.accessToken", "data.token");
                String refreshToken = extractToken(response, "refreshToken", "refresh_token", "data.refreshToken", "data.refresh_token");
                Long expireTime = extractExpireTime(response);
                
                if (StringUtils.isNotEmpty(accessToken)) 
                {
                    // 保存token到Redis
                    saveTokenToRedis(accessToken, refreshToken, expireTime);
                    
                    log.info("外部系统登录成功，token已保存");
                    return true;
                } 
                else 
                {
                    log.error("登录响应中未找到token");
                }
            } 
            else 
            {
                log.error("外部系统登录失败：{}", response.getString("message"));
            }
        } 
        catch (Exception e) 
        {
            log.error("登录外部系统异常", e);
        }
        
        return false;
    }
    
    /**
     * 获取当前有效的token
     */
    public String getValidToken() 
    {
        try 
        {
            String token = redisTemplate.opsForValue().get(REDIS_TOKEN_KEY);
            
            if (StringUtils.isEmpty(token)) 
            {
                log.warn("Token不存在，尝试重新登录");
                if (login()) 
                {
                    token = redisTemplate.opsForValue().get(REDIS_TOKEN_KEY);
                }
            } 
            else 
            {
                // 检查token是否即将过期（提前30分钟）
                if (isTokenExpired()) 
                {
                    log.warn("Token已过期，尝试重新登录");
                    if (login()) 
                    {
                        token = redisTemplate.opsForValue().get(REDIS_TOKEN_KEY);
                    } 
                    else 
                    {
                        token = null;
                    }
                } 
                else 
                {
                    log.debug("Token有效，无需重新登录");
                }
            }
            
            return token;
        } 
        catch (Exception e) 
        {
            log.error("获取有效token失败", e);
            return null;
        }
    }
    
    /**
     * 检查token是否过期
     */
    private boolean isTokenExpired() 
    {
        try 
        {
            String expireTimeStr = redisTemplate.opsForValue().get(REDIS_TOKEN_EXPIRE_KEY);
            if (StringUtils.isEmpty(expireTimeStr)) 
            {
                log.warn("Token过期时间不存在，认为已过期");
                return true;
            }
            
            long expireTime = Long.parseLong(expireTimeStr);
            long currentTime = System.currentTimeMillis();
            
            // 提前30分钟检查过期（30 * 60 * 1000 = 1800000毫秒）
            long refreshThreshold = 30 * 60 * 1000L;
            
            boolean willExpireSoon = (expireTime - currentTime) <= refreshThreshold;
            
            if (willExpireSoon) 
            {
                log.info("Token即将过期，过期时间：{}，当前时间：{}", 
                    new java.util.Date(expireTime), new java.util.Date(currentTime));
            }
            
            return willExpireSoon;
        } 
        catch (Exception e) 
        {
            log.error("检查token过期时间失败", e);
            return true; // 出错时认为已过期，触发重新登录
        }
    }
    
    /**
     * 刷新token（如果系统支持）
     */
    public boolean refreshToken() 
    {
        try 
        {
            String refreshTokenValue = redisTemplate.opsForValue().get(REDIS_REFRESH_TOKEN_KEY);
            if (StringUtils.isEmpty(refreshTokenValue)) 
            {
                log.warn("Refresh token不存在，尝试重新登录");
                return login();
            }
            
            ExternalSystemProperties.Auth authConfig = externalSystemProperties.getAuth();
            String refreshEndpoint = authConfig.getRefreshEndpoint();
            
            if (StringUtils.isEmpty(refreshEndpoint)) 
            {
                log.info("未配置token刷新接口，直接重新登录");
                return login();
            }
            
            // 构建刷新请求数据
            Map<String, Object> refreshData = new HashMap<>();
            refreshData.put("refreshToken", refreshTokenValue);
            
            log.info("尝试刷新token...");
            
            // 调用刷新接口
            JSONObject response = externalSystemService.callExternalPost(refreshEndpoint, refreshData);
            
            if (isSuccessResponse(response)) 
            {
                String newAccessToken = extractToken(response, "accessToken", "access_token", "token", "data.accessToken", "data.access_token", "data.token");
                String newRefreshToken = extractToken(response, "refreshToken", "refresh_token", "data.refreshToken", "data.refresh_token");
                Long expireTime = extractExpireTime(response);
                
                if (StringUtils.isNotEmpty(newAccessToken)) 
                {
                    // 保存新的token
                    saveTokenToRedis(newAccessToken, newRefreshToken, expireTime);
                    log.info("Token刷新成功");
                    return true;
                } 
                else 
                {
                    log.error("刷新响应中未找到新token");
                }
            } 
            else 
            {
                log.error("Token刷新失败：{}，尝试重新登录", response.getString("message"));
                return login();
            }
            
        } 
        catch (Exception e) 
        {
            log.error("刷新token异常，尝试重新登录", e);
            return login();
        }
        
        return false;
    }
    


    /**
     * 保存token到Redis
     */
    private void saveTokenToRedis(String accessToken, String refreshToken, Long expireTime) 
    {
        // Token有效期设置为8小时
        redisTemplate.opsForValue().set(REDIS_TOKEN_KEY, accessToken, 8, TimeUnit.HOURS);
        
        if (StringUtils.isNotEmpty(refreshToken)) 
        {
            redisTemplate.opsForValue().set(REDIS_REFRESH_TOKEN_KEY, refreshToken, 7, TimeUnit.DAYS);
        }
        
        // 如果没有提供过期时间，则设置为当前时间 + 8小时（毫秒）
        if (expireTime == null) 
        {
            expireTime = System.currentTimeMillis() + 8 * 60 * 60 * 1000L;
        }
        
        redisTemplate.opsForValue().set(REDIS_TOKEN_EXPIRE_KEY, expireTime.toString(), 8, TimeUnit.HOURS);
        log.info("Token已保存，有效期8小时，过期时间：{}", new java.util.Date(expireTime));
    }
    
    /**
     * 判断响应是否成功
     */
    private boolean isSuccessResponse(JSONObject response) 
    {
        if (response == null) 
        {
            return false;
        }
        
        // 尝试多种成功判断方式
        Integer code = response.getInteger("code");
        if (code != null) 
        {
            return code == 200 || code == 0;
        }
        
        Boolean success = response.getBoolean("success");
        if (success != null) 
        {
            return success;
        }
        
        String status = response.getString("status");
        if (StringUtils.isNotEmpty(status)) 
        {
            return "success".equalsIgnoreCase(status) || "ok".equalsIgnoreCase(status);
        }
        
        // 如果都没有，则认为有data字段就是成功
        return response.containsKey("data");
    }
    
    /**
     * 从响应中提取token
     */
    private String extractToken(JSONObject response, String... tokenKeys) 
    {
        for (String key : tokenKeys) 
        {
            String token = getNestedValue(response, key);
            if (StringUtils.isNotEmpty(token)) 
            {
                return token;
            }
        }
        return null;
    }
    
    /**
     * 从响应中提取过期时间
     */
    private Long extractExpireTime(JSONObject response) 
    {
        String[] expireKeys = {"expireTime", "expire_time", "expiresIn", "expires_in", "data.expireTime", "data.expire_time"};
        
        for (String key : expireKeys) 
        {
            String expireTimeStr = getNestedValue(response, key);
            if (StringUtils.isNotEmpty(expireTimeStr)) 
            {
                try 
                {
                    return Long.parseLong(expireTimeStr);
                } 
                catch (NumberFormatException e) 
                {
                    log.debug("解析过期时间失败：{}", expireTimeStr);
                }
            }
        }
        
        return null;
    }
    
    /**
     * 获取嵌套的JSON值
     */
    private String getNestedValue(JSONObject json, String key) 
    {
        if (json == null || StringUtils.isEmpty(key)) 
        {
            return null;
        }
        
        if (key.contains(".")) 
        {
            String[] keys = key.split("\\.");
            JSONObject current = json;
            
            for (int i = 0; i < keys.length - 1; i++) 
            {
                Object obj = current.get(keys[i]);
                if (obj instanceof JSONObject) 
                {
                    current = (JSONObject) obj;
                } 
                else 
                {
                    return null;
                }
            }
            
            Object value = current.get(keys[keys.length - 1]);
            return value != null ? value.toString() : null;
        } 
        else 
        {
            Object value = json.get(key);
            return value != null ? value.toString() : null;
        }
    }
    
    /**
     * 清除token缓存
     */
    public void clearToken() 
    {
        redisTemplate.delete(REDIS_TOKEN_KEY);
        redisTemplate.delete(REDIS_REFRESH_TOKEN_KEY);
        redisTemplate.delete(REDIS_TOKEN_EXPIRE_KEY);
        log.info("已清除外部系统token缓存");
    }
} 