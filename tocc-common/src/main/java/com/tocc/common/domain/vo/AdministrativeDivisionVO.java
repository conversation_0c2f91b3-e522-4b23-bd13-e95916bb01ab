package com.tocc.common.domain.vo;
import java.util.List;

/**
 * 行政区划 VO
 *
 * <AUTHOR>
 */
public class AdministrativeDivisionVO {

    private String id;

    private String pid;

    private Integer deep;

    private String name;

    private String pinyinPrefix;

    private String extId;

    private String extName;

    private List<AdministrativeDivisionVO> children;

    public AdministrativeDivisionVO() {
    }

    public AdministrativeDivisionVO(String id, String pid, Integer deep, String name,
                                String pinyinPrefix, String extId, String extName) {
        this.id = id;
        this.pid = pid;
        this.deep = deep;
        this.name = name;
        this.pinyinPrefix = pinyinPrefix;
        this.extId = extId;
        this.extName = extName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public Integer getDeep() {
        return deep;
    }

    public void setDeep(Integer deep) {
        this.deep = deep;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyinPrefix() {
        return pinyinPrefix;
    }

    public void setPinyinPrefix(String pinyinPrefix) {
        this.pinyinPrefix = pinyinPrefix;
    }

    public String getExtId() {
        return extId;
    }

    public void setExtId(String extId) {
        this.extId = extId;
    }

    public String getExtName() {
        return extName;
    }

    public void setExtName(String extName) {
        this.extName = extName;
    }

    public List<AdministrativeDivisionVO> getChildren() {
        return children;
    }

    public void setChildren(List<AdministrativeDivisionVO> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return "AdministrativeDivisionVO{" +
                "id='" + id + '\'' +
                ", pid='" + pid + '\'' +
                ", deep=" + deep +
                ", name='" + name + '\'' +
                ", pinyinPrefix='" + pinyinPrefix + '\'' +
                ", extId='" + extId + '\'' +
                ", extName='" + extName + '\'' +
                ", children=" + (children != null ? children.size() + " items" : "null") +
                '}';
    }
}
