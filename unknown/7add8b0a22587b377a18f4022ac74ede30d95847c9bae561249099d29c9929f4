package com.tocc.utils;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;

import com.google.gson.Gson;
import org.apache.http.HttpEntity;
import org.apache.http.HttpVersion;
import org.apache.http.NameValuePair;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPatch;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class HttpClientUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientUtils.class);

    public static CloseableHttpClient getClient() {
        return HttpClientBuilder.create().build();
    }
    public static CloseableHttpClient getClient(String url) {
        CloseableHttpClient httpClient = null;
        if(url != null && url.startsWith("https")) {
            httpClient = createSSLClientDefault();
        } else {
            httpClient = HttpClientUtils.getClient();
        }
        return httpClient;
    }

    public static RequestConfig getConfig() {
        return RequestConfig.custom().setConnectionRequestTimeout(10000).setSocketTimeout(10000).setConnectTimeout(10000).build();
    }
    public static RequestConfig getConfig(int timeout) {
        return RequestConfig.custom().setConnectionRequestTimeout(timeout).setSocketTimeout(timeout).setConnectTimeout(timeout).build();
    }

    /**
     * @Desc POST请求，body参数
     */
    public static String post(String url, Map<String, String> headParam, String body) {
        return post(10000, url, headParam, body);
    }
    public static String postWithBody(String url, Map<String, String> headParam, Map<String, String> body) {
        return post(10000, url, headParam, new Gson().toJson(body));
    }
    public static String postWithBody(int timeout, String url, Map<String, String> headParam, Map<String, String> body) {
        return post(timeout, url, headParam, new Gson().toJson(body));
    }
    /**
     * @Desc POST请求，body参数
     */
    public static String post(int timeout, String url, Map<String, String> headParam, String body) {
        CloseableHttpClient httpClient = getClient(url);

        RequestConfig config = getConfig(timeout);
        CloseableHttpResponse res = null;
        HttpPost post = new HttpPost(url);
        post.setProtocolVersion(HttpVersion.HTTP_1_0);
        post.setConfig(config);
        String content = null;
        post.setHeader("content-type", "application/json;charset=UTF-8");
        try {
            if(headParam != null) {
                for (String key : headParam.keySet()) {
                    LOGGER.info("Header=key:{},value:{}", key, headParam.get(key));
                    post.setHeader(key, headParam.get(key));
                }
            }
            if(body != null) {
                post.setEntity(new StringEntity(body, "UTF-8"));
            }
            res = httpClient.execute(post);
            HttpEntity entity = res.getEntity();
            content = EntityUtils.toString(entity);
            EntityUtils.consume(entity);
            LOGGER.info("URL:{},content:{}", url, content);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            e.printStackTrace();
        } finally {
            if(httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return content;
    }

    /**
     * @Desc POST请求，Parameters参数
     */
    public static String postWithParam(String url, Map<String, String> headParam, Map<String, String> params) {
        return postWithParam(10000, url, headParam, params);
    }
    /**
     * @Desc POST请求，Parameters参数
     */
    public static String postWithParam(int timeout, String url, Map<String, String> headParam, Map<String, String> params) {
        LOGGER.info(url);
        CloseableHttpClient httpClient = getClient(url);
        RequestConfig config = getConfig(timeout);
        CloseableHttpResponse res = null;
        HttpPost post = new HttpPost(url);
        post.setProtocolVersion(HttpVersion.HTTP_1_0);
        post.setConfig(config);
        String content = null;
        post.setHeader("content-type", "application/json;charset=UTF-8");
        try {
            if(headParam != null) {
                for (String key : headParam.keySet()) {
                    LOGGER.info("Header=key:{},value:{}", key, headParam.get(key));
                    post.setHeader(key, headParam.get(key));
                }
            }
            if(params != null) {
                List<NameValuePair> paramsList = new ArrayList<>();

                for (String key : params.keySet()) {
                    String value = params.get(key);
                    System.out.println("Key: " + key + ", Value: " + value);
                    paramsList.add(new BasicNameValuePair(key, value));
                }
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramsList, StandardCharsets.UTF_8);
                post.setEntity(entity);
            }
            res = httpClient.execute(post);
            HttpEntity entity = res.getEntity();
            content = EntityUtils.toString(entity);
            EntityUtils.consume(entity);
            LOGGER.info("URL:{},content:{}", url, content);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            e.printStackTrace();
        } finally {
            if(httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return content;
    }

    /**
     * @Desc POST请求，同时有Parameters参数和body参数
     */
    public static String postWithParamBody(String url, Map<String, String> headParam, Map<String, String> params, String body) {
        return postWithParamBody(10000, url, headParam, params, body);
    }
    /**
     * @Desc POST请求，同时有Parameters参数和body参数
     */
    public static String postWithParamBody(String url, Map<String, String> headParam, Map<String, String> params, Map<String, String> body) {
        return postWithParamBody(10000, url, headParam, params, new Gson().toJson(body));
    }
    /**
     * @Desc POST请求，同时有Parameters参数和body参数
     */
    public static String postWithParamBody(int timeout, String url, Map<String, String> headParam, Map<String, String> params, String body) {
        LOGGER.info(url);
        LOGGER.info(params.toString());
        CloseableHttpClient httpClient = null;
        if(url != null && url.startsWith("https")) {
            httpClient = createSSLClientDefault();
        } else {
            httpClient = HttpClientUtils.getClient();
        }

        RequestConfig config = RequestConfig.custom().setConnectionRequestTimeout(timeout).setSocketTimeout(timeout).setConnectTimeout(timeout).build();
        CloseableHttpResponse res = null;
        URIBuilder uriBuilder = null;
        try {
            uriBuilder = new URIBuilder(url);
        } catch (URISyntaxException e1) {
            e1.printStackTrace();
            return null;
        }
        if(params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                uriBuilder.setParameter(entry.getKey(), entry.getValue());
            }
        }
        URI uri = null;
        try {
            uri = uriBuilder.build();
        } catch (URISyntaxException e1) {
            e1.printStackTrace();
            return null;
        }
        // 创建POST请求
        HttpPost post = new HttpPost(uri);
        // 设置请求头信息
        post.setHeader("content-type", "application/json;charset=UTF-8");

        // 创建JSON数据并设置为请求体
        post.setProtocolVersion(HttpVersion.HTTP_1_0);
        post.setConfig(config);
        String content = null;
        try {
            if(headParam != null) {
                for (String key : headParam.keySet()) {
                    LOGGER.info("Header=key:{},value:{}", key, headParam.get(key));
                    post.setHeader(key, headParam.get(key));
                }
            }
            post.setEntity(new StringEntity(body,"UTF-8"));
            res = httpClient.execute(post);
            HttpEntity entity = res.getEntity();
            content = EntityUtils.toString(entity);
            EntityUtils.consume(entity);
            LOGGER.info("URL:{},content:{}", url, content);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            e.printStackTrace();
        } finally {
            if(httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return content;
    }

    public static String patch(String url, Map<String, String> headParam, String params) {
        LOGGER.info(params);
        CloseableHttpClient httpClient = HttpClientUtils.getClient();
        RequestConfig config = HttpClientUtils.getConfig();
        CloseableHttpResponse res = null;
        HttpPatch patch = new HttpPatch(url);
        patch.setConfig(config);
        String content = null;
        patch.setHeader("content-type", "application/json;charset=UTF-8");
        try {
            if(headParam != null) {
                for (String key : headParam.keySet()) {
                    patch.setHeader(key, headParam.get(key));
                }
            }
            patch.setEntity(new StringEntity(params,"UTF-8"));
            res = httpClient.execute(patch);
            HttpEntity entity = res.getEntity();
            content = EntityUtils.toString(entity);
            EntityUtils.consume(entity);
            LOGGER.info(url + "patch-----------content=" + content);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            //e.printStackTrace();
        }
        return content;
    }

    public static String get(String url, Map<String, String> headers, int timeout) {
        CloseableHttpClient httpClient = HttpClientUtils.getClient();
        httpClient = createSSLClientDefault();
        if (timeout < 0) {
            timeout = 10000;
        }
        RequestConfig config = RequestConfig.custom().setConnectionRequestTimeout(timeout).setSocketTimeout(timeout).setConnectTimeout(timeout).build();
        CloseableHttpResponse res = null;
        HttpGet get = new HttpGet(url);
        get.setConfig(config);
        String content = null;
        get.setHeader("content-type", "application/json;charset=UTF-8");
        try {
            if (headers != null && headers.size() > 0) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    get.setHeader(entry.getKey(),entry.getValue());
                }
            }
            res = httpClient.execute(get);
            HttpEntity entity = res.getEntity();
            content = EntityUtils.toString(entity);
            EntityUtils.consume(entity);
            LOGGER.info(url + "get-----------content=" + content);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return content;
    }

    //创建Https-Client
    public static CloseableHttpClient createSSLClientDefault(){
        try {
            HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            });
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                //信任所有证书
                @Override
                public boolean isTrusted(java.security.cert.X509Certificate[] chain, String authType)
                        throws java.security.cert.CertificateException {
                    return true;
                }
            }).build();

            HttpClientBuilder clientBuilder = HttpClients.custom().setSSLContext(sslContext).setSSLHostnameVerifier(new NoopHostnameVerifier());
            return clientBuilder.build();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        return HttpClients.createDefault();
    }

}
