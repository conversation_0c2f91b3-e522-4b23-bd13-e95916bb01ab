package com.tocc.service.impl;

import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.uuid.IdUtils;
import com.tocc.domain.dto.MaterialDTO;
import com.tocc.domain.dto.WarehouseMaterialDTO;
import com.tocc.domain.dto.RescueTeamMaterialDTO;
import com.tocc.domain.vo.MaterialVO;
import com.tocc.mapper.MaterialMapper;
import com.tocc.mapper.WarehouseMaterialMapper;
import com.tocc.mapper.RescueTeamMaterialMapper;
import com.tocc.service.IMaterialService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 物资Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class MaterialServiceImpl implements IMaterialService {
    
    @Autowired
    private MaterialMapper materialMapper;
    
    @Autowired
    private WarehouseMaterialMapper warehouseMaterialMapper;
    
    @Autowired
    private RescueTeamMaterialMapper rescueTeamMaterialMapper;

    /**
     * 查询物资列表
     * 
     * @param material 物资查询条件
     * @return 物资
     */
    @Override
    public List<MaterialVO> selectMaterialList(MaterialDTO material) {
        return materialMapper.selectMaterialList(material);
    }

    /**
     * 新增物资
     * 
     * @param material 物资
     * @return 结果
     */
    @Override
    @Transactional
    public int insertMaterial(MaterialDTO material) {
        // 生成物资ID
        String materialId = IdUtils.simpleUUID();
        material.setId(materialId);
        material.setCreator(getUsername());
        material.setDelFlag(0);
        
        // 插入物资基础信息
        int result = materialMapper.insertMaterial(material);
        
        if (result > 0) {
            // 根据归属创建对应的关联关系
            if (material.getWarehouseId() != null && !material.getWarehouseId().isEmpty()) {
                // 归属于仓库
                WarehouseMaterialDTO warehouseMaterial = new WarehouseMaterialDTO();
                warehouseMaterial.setWarehouseId(material.getWarehouseId());
                warehouseMaterial.setMaterialId(materialId);
                warehouseMaterialMapper.insertWarehouseMaterial(warehouseMaterial);
            } else if (material.getTeamId() != null && !material.getTeamId().isEmpty()) {
                // 归属于救援队伍
                RescueTeamMaterialDTO teamMaterial = new RescueTeamMaterialDTO();
                teamMaterial.setTeamId(material.getTeamId());
                teamMaterial.setMaterialId(materialId);
                rescueTeamMaterialMapper.insertTeamMaterial(teamMaterial);
            }
        }
        
        return result;
    }

    /**
     * 修改物资
     * 
     * @param material 物资
     * @return 结果
     */
    @Override
    @Transactional
    public int updateMaterial(MaterialDTO material) {
        material.setUpdater(getUsername());
        
        // 更新物资基础信息
        int result = materialMapper.updateMaterial(material);
        
        if (result > 0) {
            // 清理原有的关联关系
            warehouseMaterialMapper.deleteWarehouseMaterialsByMaterialId(material.getId());
            rescueTeamMaterialMapper.deleteTeamMaterialsByMaterialId(material.getId());
            
            // 创建新的关联关系
            if (material.getWarehouseId() != null && !material.getWarehouseId().isEmpty()) {
                // 归属于仓库
                WarehouseMaterialDTO warehouseMaterial = new WarehouseMaterialDTO();
                warehouseMaterial.setWarehouseId(material.getWarehouseId());
                warehouseMaterial.setMaterialId(material.getId());
                warehouseMaterialMapper.insertWarehouseMaterial(warehouseMaterial);
            } else if (material.getTeamId() != null && !material.getTeamId().isEmpty()) {
                // 归属于救援队伍
                RescueTeamMaterialDTO teamMaterial = new RescueTeamMaterialDTO();
                teamMaterial.setTeamId(material.getTeamId());
                teamMaterial.setMaterialId(material.getId());
                rescueTeamMaterialMapper.insertTeamMaterial(teamMaterial);
            }
        }
        
        return result;
    }

    /**
     * 批量删除物资
     * 
     * @param ids 需要删除的物资主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMaterialByIds(String[] ids) {
        // 删除关联关系
        for (String id : ids) {
            warehouseMaterialMapper.deleteWarehouseMaterialsByMaterialId(id);
            rescueTeamMaterialMapper.deleteTeamMaterialsByMaterialId(id);
        }
        
        // 软删除物资
        return materialMapper.deleteMaterialByIds(ids);
    }

    /**
     * 删除物资信息
     * 
     * @param id 物资主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMaterialById(String id) {
        // 删除关联关系
        warehouseMaterialMapper.deleteWarehouseMaterialsByMaterialId(id);
        rescueTeamMaterialMapper.deleteTeamMaterialsByMaterialId(id);
        
        // 软删除物资
        return materialMapper.deleteMaterialById(id);
    }

    /**
     * 获取当前用户名
     */
    private String getUsername() {
        try {
            return SecurityUtils.getUsername();
        } catch (Exception e) {
            return "system";
        }
    }
}
