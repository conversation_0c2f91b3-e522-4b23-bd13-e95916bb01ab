package com.tocc.domain.vo;

public class YkTokenVO {
    private Integer code;
    private String token;
    // 供Websocket使用，这个判断唯一浏览器（电脑）
    private String uuid;
    private String message;
    public YkTokenVO() {}
    public YkTokenVO(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    public YkTokenVO(Integer code, String message, String token, String uuid) {
        this.code = code;
        this.message = message;
        this.token = token;
        this.uuid = uuid;
    }
    public Integer getCode() {
        return code;
    }
    public void setCode(Integer code) {
        this.code = code;
    }
    public String getToken() {
        return token;
    }
    public void setToken(String token) {
        this.token = token;
    }
    public String getUuid() {
        return uuid;
    }
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    public String getMessage() {
        return message;
    }
    public void setMessage(String message) {
        this.message = message;
    }
}
