package com.tocc.service;

import com.tocc.domain.dto.MaterialDTO;
import com.tocc.domain.vo.MaterialVO;
import java.util.List;

/**
 * 物资Service接口
 * 
 * <AUTHOR>
 */
public interface IMaterialService {
    
    /**
     * 查询物资列表（包含完整信息）
     * 
     * @param material 物资查询条件
     * @return 物资集合
     */
    List<MaterialVO> selectMaterialList(MaterialDTO material);

    /**
     * 新增物资
     * 
     * @param material 物资
     * @return 结果
     */
    int insertMaterial(MaterialDTO material);

    /**
     * 修改物资
     * 
     * @param material 物资
     * @return 结果
     */
    int updateMaterial(MaterialDTO material);

    /**
     * 批量删除物资
     * 
     * @param ids 需要删除的物资主键集合
     * @return 结果
     */
    int deleteMaterialByIds(String[] ids);

    /**
     * 删除物资信息
     * 
     * @param id 物资主键
     * @return 结果
     */
    int deleteMaterialById(String id);
}
