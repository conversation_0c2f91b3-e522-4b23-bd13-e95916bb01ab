<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tocc.mapper.DrillPlanMapper">
    

        
    <select id="list" resultType="com.tocc.domain.vo.DrillPlanVo">
        SELECT id, company, drill_name, drill_content, drill_way, drill_date, drill_scale, organizer, second_organizer, responsible, drill_scene, reporter, phone, create_time, create_by, update_time, update_by, del_flag, status
        FROM tocc.drill_plan where del_flag=0
        <if test="drillName != null and drillName != ''">
            AND drill_name like concat('%', #{drillName}, '%')
        </if>
    </select>

<!--    <insert id="batchInsertWeatherWarningArea" parameterType="list">-->
<!--        insert into weather_warning_area (warning_id, region_id, region_name, create_time)-->
<!--        values-->
<!--        <foreach collection="list" item="item" separator=",">-->
<!--            (#{item.warningId}, #{item.regionId}, #{item.regionName}, #{item.createTime})-->
<!--        </foreach>-->
<!--    </insert>-->

<!--    <update id="updateWeatherWarningArea" parameterType="WeatherWarningArea">-->
<!--        update weather_warning_area-->
<!--        <trim prefix="SET" suffixOverrides=",">-->
<!--            <if test="regionName != null">region_name = #{regionName},</if>-->
<!--        </trim>-->
<!--        where warning_id = #{warningId} and region_id = #{regionId}-->
<!--    </update>-->

<!--    <delete id="deleteWeatherWarningAreaByIds">-->
<!--        delete from weather_warning_area -->
<!--        where warning_id = #{warningId} and region_id = #{regionId}-->
<!--    </delete>-->

<!--    <delete id="deleteWeatherWarningAreaByWarningId" parameterType="String">-->
<!--        delete from weather_warning_area where warning_id = #{warningId}-->
<!--    </delete>-->

<!--    <delete id="deleteWeatherWarningAreaByWarningIds" parameterType="String">-->
<!--        delete from weather_warning_area where warning_id in -->
<!--        <foreach item="warningId" collection="array" open="(" separator="," close=")">-->
<!--            #{warningId}-->
<!--        </foreach>-->
<!--    </delete>-->

</mapper>
