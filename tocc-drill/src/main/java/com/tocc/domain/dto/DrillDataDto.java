package com.tocc.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DrillDataDto {
    private Long id;

    private Long drillPlanId;

    @ApiModelProperty(value = "事故情景描述")
    private String accidentSceneDescription;

    @ApiModelProperty(value = "演练目标达成情况")
    private String drillAchievement;

    @ApiModelProperty(value = "暴露问题和薄弱环节")
    private String question;

    @ApiModelProperty(value = "改进措施")
    private String improve;

    @ApiModelProperty(value = "演练地点")
    private String address;

    @ApiModelProperty(value = "演练照片")
    private String photo;

    @ApiModelProperty(value = "演练签到表")
    private String signInSheet;

    @ApiModelProperty(value = "附件")
    private String annex;

    @ApiModelProperty(value = "填报人")
    private String reporter;
}
