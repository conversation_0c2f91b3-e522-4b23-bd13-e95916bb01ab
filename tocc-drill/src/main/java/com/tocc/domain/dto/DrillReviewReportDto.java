package com.tocc.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DrillReviewReportDto {

    private Long id;

    private Long drillPlanId;

    @ApiModelProperty(value = "演练情况分析")
    private String analyse;

    @ApiModelProperty(value = "演练目标的实现")
    private String goalAchieved;

    @ApiModelProperty(value = "改进建议和意见")
    private String suggest;

    @ApiModelProperty(value = "评估.1,2,3,4 优、良、中、差")
    private String evaluation;

    @ApiModelProperty(value = "复盘会议室")
    private String reviewAddress;

    @ApiModelProperty(value = "复盘签到表")
    private String reviewSignIn;

    @ApiModelProperty(value = "复盘会议照片")
    private String reviewPhoto;

    @ApiModelProperty(value = "复盘报告")
    private String reviewReport;

    @ApiModelProperty(value = "填报人")
    private String reporter;
}
