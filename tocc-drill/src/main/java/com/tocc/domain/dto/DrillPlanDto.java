package com.tocc.domain.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DrillPlanDto {
    @ApiModelProperty(value = "${comment}")
    private Long id;

    @ApiModelProperty(value = "填报单位")
    private String company;

    @ApiModelProperty(value = "演练名称")
    private String drillName;

    @ApiModelProperty(value = "演练内容")
    private String drillContent;

    @ApiModelProperty(value = "演练方式")
    private String drillWay;

    @ApiModelProperty(value = "演练规模")
    private String drillScale;

    @ApiModelProperty(value = "主办单位")
    private String organizer;

    @ApiModelProperty(value = "承办单位")
    private String secondOrganizer;


    @ApiModelProperty(value = "负责人")
    private String responsible;

    @ApiModelProperty(value = "演练场景")
    private String drillScene;


    @ApiModelProperty(value = "填报人")
    private String reporter;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "演练时间")
    private Date drillDate;
}
