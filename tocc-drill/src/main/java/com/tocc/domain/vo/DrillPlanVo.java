package com.tocc.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DrillPlanVo {
    private Long id;

    private String drillName;

    private String organizer;

    private String responsible;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date drillDate;

    private String drillWay;

    @ApiModelProperty(value = "演练场景")
    private String drillScene;

    private String status;
}
