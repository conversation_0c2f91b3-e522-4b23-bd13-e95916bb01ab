package com.tocc.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DrillDataVo {

    private Long id;

    private Long drillPlanId;
    @ApiModelProperty(value = "填报单位")
    private String company;

    @ApiModelProperty(value = "演练名称")
    private String drillName;

    @ApiModelProperty(value = "演练方式")
    private String drillWay;

    @ApiModelProperty(value = "演练时间")
    private Date drillDate;

    @ApiModelProperty(value = "演练规模")
    private String drillScale;

    @ApiModelProperty(value = "负责人")
    private String responsible;

    @ApiModelProperty(value = "填报人")
    private String reporter;

    @ApiModelProperty(value = "事故情景描述")
    private String accidentSceneDescription;


    @ApiModelProperty(value = "演练目标达成情况")
    private String drillAchievement;


    @ApiModelProperty(value = "暴露问题和薄弱环节")
    private String question;

    @ApiModelProperty(value = "改进措施")
    private String improve;

    @ApiModelProperty(value = "演练地点")
    private String address;


    @ApiModelProperty(value = "演练照片")
    private String photo;

    @ApiModelProperty(value = "演练签到表")
    private String signInSheet;

    @ApiModelProperty(value = "附件")
    private String annex;

    private String status;


}
