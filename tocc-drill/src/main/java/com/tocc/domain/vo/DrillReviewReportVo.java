package com.tocc.domain.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DrillReviewReportVo {

    private Long id;

    private Long drillPlanId;

    @ApiModelProperty(value = "填报单位")
    private String company;

    @ApiModelProperty(value = "演练名称")
    private String drillName;

    @ApiModelProperty(value = "演练方式")
    private String drillWay;

    @ApiModelProperty(value = "演练时间")
    private Date drillDate;

    @ApiModelProperty(value = "演练规模")
    private String drillScale;

    @ApiModelProperty(value = "负责人")
    private String responsible;

    @ApiModelProperty(value = "填报人")
    private String reporter;

    @ApiModelProperty(value = "演练情况分析")
    private String analyse;

    @ApiModelProperty(value = "演练目标的实现")
    private String goalAchieved;

    @ApiModelProperty(value = "改进建议和意见")
    private String suggest;

    @ApiModelProperty(value = "评估.1,2,3,4 优、良、中、差")
    private String evaluation;

    @ApiModelProperty(value = "复盘会议室")
    private String reviewAddress;

    @ApiModelProperty(value = "复盘签到表")
    private String reviewSignIn;

    @ApiModelProperty(value = "复盘会议照片")
    private String reviewPhoto;

    @ApiModelProperty(value = "复盘报告")
    private String reviewReport;

    private String status;
}
