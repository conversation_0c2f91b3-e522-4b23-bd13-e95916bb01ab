package com.tocc.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.tocc.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("drill_plan")
public class DrillPlan  {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Excel(name = "填报单位")
    @ApiModelProperty(value = "填报单位")
    private String company;

    @Excel(name = "演练名称")
    @ApiModelProperty(value = "演练名称")
    private String drillName;

    @Excel(name = "演练内容")
    @ApiModelProperty(value = "演练内容")
    private String drillContent;

    @Excel(name = "演练方式")
    @ApiModelProperty(value = "演练方式")
    private String drillWay;

    @Excel(name = "演练规模")
    @ApiModelProperty(value = "演练规模")
    private String drillScale;

    @Excel(name = "主办单位")
    @ApiModelProperty(value = "主办单位")
    private String organizer;

    @Excel(name = "承办单位")
    @ApiModelProperty(value = "承办单位")
    private String secondOrganizer;

    @Excel(name = "负责人")
    @ApiModelProperty(value = "负责人")
    private String responsible;

    @Excel(name = "演练场景")
    @ApiModelProperty(value = "演练场景")
    private String drillScene;

    @Excel(name = "填报人")
    @ApiModelProperty(value = "填报人")
    private String reporter;

    @Excel(name = "电话")
    @ApiModelProperty(value = "电话")
    private String phone;

    @Excel(name = "演练时间")
    @ApiModelProperty(value = "演练时间")
    private Date drillDate;

    /** 1-未演练，2-准备演练，3-已演练 */
    @Excel(name = "演练状态", readConverterExp = "1-未演练，2-准备演练，3-已演练")
    @ApiModelProperty(value = "填报状态")
    private Integer status;

    /** 是否删除（0-否，1-是） */
    @ApiModelProperty(value = "删除状态")
    @TableLogic(value = "0",delval = "1")
    private Integer delFlag;

    /** 创建者 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新者 */
    @TableField(value = "update_by",fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
