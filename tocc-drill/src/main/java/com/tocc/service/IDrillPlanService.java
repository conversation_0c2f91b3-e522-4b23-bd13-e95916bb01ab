package com.tocc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tocc.domain.DrillPlan;
import com.tocc.domain.dto.DrillPlanDto;
import com.tocc.domain.vo.DrillDataVo;
import com.tocc.domain.vo.DrillPlanStaticVo;
import com.tocc.domain.vo.DrillPlanVo;
import com.tocc.domain.vo.DrillReviewReportVo;

import java.util.List;

public interface IDrillPlanService extends IService<DrillPlan> {

    Boolean insertEntity(DrillPlanDto dto);

    List<DrillPlanVo> drillPlanListVO(DrillPlanDto dto);

    DrillDataVo drillDataDetail(Long id);

    DrillReviewReportVo DrillReviewReportDetail(Long id);


    DrillPlanStaticVo staticDetail();

    Boolean deleteByIds(List<Long> ids);
}
