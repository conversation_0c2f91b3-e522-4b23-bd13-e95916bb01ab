package com.tocc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.domain.DrillData;
import com.tocc.domain.DrillPlan;
import com.tocc.domain.dto.DrillDataDto;
import com.tocc.mapper.DrillDataMapper;
import com.tocc.service.IDrillDataService;
import com.tocc.service.IDrillPlanService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class DrillDataServiceImpl extends ServiceImpl<DrillDataMapper, DrillData> implements IDrillDataService {
    @Override
    public Boolean saveEntity(DrillDataDto dto) {

        if (ObjectUtil.isNotNull(dto.getDrillPlanId())){
            IDrillPlanService service = SpringUtil.getBean(IDrillPlanService.class);
            List<DrillPlan> drillPlans = service.listByIds(Collections.singleton(dto.getDrillPlanId()));
            if (CollectionUtil.isNotEmpty(drillPlans)){
                DrillPlan drillPlan = drillPlans.get(0);
//                drillPlan.setReporter(dto.getReporter());
                if (drillPlan.getStatus() != 3){
                    drillPlan.setStatus(2);
                }
                service.updateById(drillPlan);
            }
        }

        DrillData drillData = BeanUtil.copyProperties(dto, DrillData.class);
        if (ObjectUtil.isNotNull(drillData.getId())){
            return this.updateById(drillData);
        }
        return this.save(drillData);
    }

    @Override
    public DrillData selectByDrillPlanId(Long id) {
        QueryWrapper<DrillData> qw = new QueryWrapper<>();
        qw.eq("drill_plan_id",id);
        List<DrillData> list = this.list(qw);
        if (CollectionUtil.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }
}
