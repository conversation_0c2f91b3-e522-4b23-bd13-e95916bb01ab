package com.tocc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tocc.domain.DrillData;
import com.tocc.domain.DrillPlan;
import com.tocc.domain.DrillReviewReport;
import com.tocc.domain.dto.DrillPlanDto;
import com.tocc.domain.vo.DrillDataVo;
import com.tocc.domain.vo.DrillPlanStaticVo;
import com.tocc.domain.vo.DrillPlanVo;
import com.tocc.domain.vo.DrillReviewReportVo;
import com.tocc.mapper.DrillPlanMapper;
import com.tocc.service.IDrillDataService;
import com.tocc.service.IDrillPlanService;
import com.tocc.service.IDrillReviewReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class DrillPlanServiceImpl extends ServiceImpl<DrillPlanMapper, DrillPlan> implements IDrillPlanService {

    @Autowired
    private DrillPlanMapper drillPlanMapper;

    @Autowired
    private IDrillDataService drillDataService;

    @Autowired
    private IDrillReviewReportService drillReviewReportService;


    @Override
    public Boolean insertEntity(DrillPlanDto dto) {
        DrillPlan drillPlan = BeanUtil.copyProperties(dto, DrillPlan.class);
        drillPlan.setStatus(1);
        if (ObjectUtil.isNotNull(drillPlan.getId())){
            return this.updateById(drillPlan);
        }
        return this.save(drillPlan);
    }

    @Override
    public List<DrillPlanVo> drillPlanListVO(DrillPlanDto dto) {


        return drillPlanMapper.list(dto);
    }

    @Override
    public DrillDataVo drillDataDetail(Long id) {

        List<DrillPlan> drillPlans = this.listByIds(Collections.singleton(id));
        if (CollectionUtil.isNotEmpty(drillPlans)){
            DrillDataVo drillDataVo = BeanUtil.copyProperties(drillPlans.get(0), DrillDataVo.class);
            drillDataVo.setReporter(null);
            drillDataVo.setId(null);
            drillDataVo.setDrillPlanId(drillPlans.get(0).getId());
            DrillData drillData = drillDataService.selectByDrillPlanId(id);
            if (ObjectUtil.isNotNull(drillData)){
                drillDataVo.setId(drillData.getId());
                drillDataVo.setAccidentSceneDescription(drillData.getAccidentSceneDescription());
                drillDataVo.setDrillAchievement(drillData.getDrillAchievement());
                drillDataVo.setQuestion(drillData.getQuestion());
                drillDataVo.setImprove(drillData.getImprove());
                drillDataVo.setAddress(drillData.getAddress());
                drillDataVo.setPhoto(drillData.getPhoto());
                drillDataVo.setSignInSheet(drillData.getSignInSheet());
                drillDataVo.setAnnex(drillData.getAnnex());
                drillDataVo.setReporter(drillData.getReporter());
            }
            return drillDataVo;
        }
        return null;
    }

    @Override
    public DrillReviewReportVo DrillReviewReportDetail(Long id) {
        List<DrillPlan> drillPlans = this.listByIds(Collections.singleton(id));
        if (CollectionUtil.isNotEmpty(drillPlans)){
            DrillReviewReportVo drillReviewReportVo = BeanUtil.copyProperties(drillPlans.get(0), DrillReviewReportVo.class);
            drillReviewReportVo.setReporter(null);
            drillReviewReportVo.setReporter(null);
            drillReviewReportVo.setId(null);
            drillReviewReportVo.setDrillPlanId(drillPlans.get(0).getId());
            DrillReviewReport drillReviewReport = drillReviewReportService.selectByDrillPlanId(id);
            if (ObjectUtil.isNotNull(drillReviewReport)){
                drillReviewReportVo.setId(drillReviewReport.getId());
                drillReviewReportVo.setAnalyse(drillReviewReport.getAnalyse());
                drillReviewReportVo.setGoalAchieved(drillReviewReport.getGoalAchieved());
                drillReviewReportVo.setSuggest(drillReviewReport.getSuggest());
                drillReviewReportVo.setEvaluation(drillReviewReport.getEvaluation());
                drillReviewReportVo.setReviewAddress(drillReviewReport.getReviewAddress());
                drillReviewReportVo.setReviewSignIn(drillReviewReport.getReviewSignIn());
                drillReviewReportVo.setReviewPhoto(drillReviewReport.getReviewPhoto());
                drillReviewReportVo.setReviewReport(drillReviewReport.getReviewReport());
                drillReviewReportVo.setReporter(drillReviewReport.getReporter());
            }
            return drillReviewReportVo;
        }
        return null;
    }

    @Override
    public DrillPlanStaticVo staticDetail() {
        DrillPlanStaticVo vo = new DrillPlanStaticVo();
        List<DrillPlan> list = this.list();
        if (CollectionUtil.isNotEmpty(list)){
            int completedNum = list.stream().filter(item -> item.getStatus().equals(3)).mapToInt(e -> 1).sum();
            int planningNum = list.stream().filter(item -> item.getStatus().equals(2)).mapToInt(e -> 1).sum();
            int notPlanNum = list.stream().filter(item -> item.getStatus().equals(1)).mapToInt(e -> 1).sum();

            int drillPlanActualNum = list.stream().filter(item -> item.getDrillWay().equals("1")).mapToInt(e -> 1).sum();
            int drillPlanDesktopNum = list.stream().filter(item -> item.getDrillWay().equals("2")).mapToInt(e -> 1).sum();
            int drillPlanUnitedNum = list.stream().filter(item -> item.getDrillWay().equals("3")).mapToInt(e -> 1).sum();
            vo.setCompletedNum(completedNum);
            vo.setPlanningNum(planningNum);
            vo.setNotPlanNum(notPlanNum);
            vo.setDrillPlanActualNum(drillPlanActualNum);
            vo.setDrillPlanDesktopNum(drillPlanDesktopNum);
            vo.setDrillPlanUnitedNum(drillPlanUnitedNum);
        }
        return vo;
    }

    @Override
    public Boolean deleteByIds(List<Long> ids) {
        return this.removeBatchByIds(ids);
    }

}
